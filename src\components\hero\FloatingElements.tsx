/**
 * Enhanced Floating Elements with Grain Filter and Vignette
 *
 * Modern 2024-2025 approach using SVG noise patterns for grain effect
 * and CSS radial gradients for subtle vignette around edges.
 */
'use client';

import React from 'react';
import { motion, useReducedMotion } from 'framer-motion';

export function FloatingElements() {
  const shouldReduceMotion = useReducedMotion();

  // Create a grid of dots similar to the TRAE image
  const createDotGrid = () => {
    const dots = [];
    const rows = 15;
    const cols = 25;

    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        // Create wave-like pattern
        const waveOffset = Math.sin((col / cols) * Math.PI * 2) * 8;
        const delay = col * 0.03 + row * 0.02;

        // Skip some dots randomly for a more organic look
        if (Math.random() > 0.7) continue;

        dots.push({
          id: `${row}-${col}`,
          x: (col / cols) * 100,
          y: (row / rows) * 100 + waveOffset,
          delay,
          opacity: Math.random() * 0.4 + 0.3,
        });
      }
    }
    return dots;
  };

  const dots = createDotGrid();

  // Floating larger elements
  const floatingElements = [
    { size: 3, x: 15, y: 25, duration: 20, delay: 0 },
    { size: 4, x: 85, y: 15, duration: 25, delay: 2 },
    { size: 2, x: 75, y: 85, duration: 18, delay: 4 },
    { size: 5, x: 25, y: 75, duration: 22, delay: 1 },
    { size: 2, x: 95, y: 65, duration: 15, delay: 3 },
    { size: 3, x: 10, y: 90, duration: 28, delay: 5 },
    { size: 2, x: 65, y: 10, duration: 16, delay: 2.5 },
    { size: 4, x: 45, y: 45, duration: 30, delay: 1.5 },
  ];

  const dotVariants = {
    initial: { opacity: 0.2, scale: 0.5 },
    animate: (delay: number) => ({
      opacity: [0.2, 0.8, 0.4, 0.9, 0.3],
      scale: [0.5, 1.2, 0.8, 1.5, 0.7],
      transition: {
        duration: shouldReduceMotion ? 0 : 12,
        delay: shouldReduceMotion ? 0 : delay,
        repeat: shouldReduceMotion ? 0 : Infinity,
        repeatType: 'reverse' as const,
        ease: [0.4, 0, 0.2, 1] as any,
      },
    }),
  };

  const floatingVariants = {
    initial: { opacity: 0, scale: 0 },
    animate: (custom: { duration: number; delay: number }) => ({
      opacity: [0, 0.4, 0.2, 0.6, 0.1],
      scale: [0, 1, 0.8, 1.2, 0.9],
      y: shouldReduceMotion ? 0 : [-10, 10, -8, 12, -5],
      x: shouldReduceMotion ? 0 : [-5, 8, -3, 10, -4],
      transition: {
        duration: custom.duration,
        repeat: shouldReduceMotion ? 0 : Infinity,
        repeatType: 'reverse' as const,
        delay: custom.delay,
        ease: [0.4, 0, 0.2, 1] as any,
      },
    }),
  };

  if (shouldReduceMotion) {
    return (
      <div className="absolute inset-0" data-oid="sq10w.s">
        {/* Static dot grid */}
        {dots.slice(0, 100).map(dot => (
          <div
            key={dot.id}
            className="absolute w-1 h-1 bg-green-500/30 rounded-full"
            style={{
              left: `${dot.x}%`,
              top: `${dot.y}%`,
              transform: 'translate(-50%, -50%)',
            }}
            data-oid="fkh-ofk"
          />
        ))}
        {/* Static floating elements */}
        {floatingElements.slice(0, 3).map((element, index) => (
          <div
            key={index}
            className="absolute rounded-full bg-green-500/20 blur-sm"
            style={{
              width: element.size * 4,
              height: element.size * 4,
              left: `${element.x}%`,
              top: `${element.y}%`,
              transform: 'translate(-50%, -50%)',
            }}
            data-oid="tatwhor"
          />
        ))}
      </div>
    );
  }

  return (
    <div className="absolute inset-0 overflow-hidden" data-oid="5re3k5p">
      {/* SVG Grain Filter - Adapted for white background */}
      <svg
        className="absolute inset-0 w-full h-full pointer-events-none"
        style={{ filter: 'opacity(0.02)' }}
        data-oid="rw0pya-"
      >
        <defs data-oid="mdxmeg2">
          <filter id="grain-white" x="0%" y="0%" width="100%" height="100%" data-oid=":04uwn3">
            <feTurbulence
              baseFrequency="0.8"
              numOctaves="3"
              result="noise"
              seed="5"
              data-oid="h58_alh"
            />

            <feColorMatrix
              in="noise"
              type="matrix"
              values="0 0 0 0 0.3
                      0 0 0 0 0.3
                      0 0 0 0 0.3
                      0 0 0 1 0"
              data-oid="8l8ku4-"
            />

            <feComponentTransfer data-oid="tn.re6m">
              <feFuncA type="discrete" tableValues="0.01 0.02 0.03 0.04" data-oid="y1e5.j2" />
            </feComponentTransfer>
            <feComposite operator="multiply" in2="SourceGraphic" data-oid="86kh0ni" />
          </filter>

          {/* Animated grain for subtle movement */}
          <animateTransform
            attributeName="transform"
            attributeType="XML"
            type="translate"
            values="0 0; 0.5 0.5; 0 0"
            dur="4s"
            repeatCount="indefinite"
            data-oid="xmi1dtf"
          />
        </defs>
        <rect width="100%" height="100%" filter="url(#grain-white)" data-oid="-1_tgss" />
      </svg>

      {/* Vignette Effect - Subtle edges for white background */}
      <div
        className="absolute inset-0 pointer-events-none"
        style={{
          background: `radial-gradient(ellipse at center,
            transparent 0%,
            transparent 60%,
            rgba(0,0,0,0.02) 75%,
            rgba(0,0,0,0.05) 90%,
            rgba(0,0,0,0.08) 100%)`,
        }}
        data-oid="e9lu3:r"
      />

      {/* Animated dot grid - Updated for white background */}
      {dots.map(dot => (
        <motion.div
          key={dot.id}
          className="absolute w-1 h-1 bg-green-600 rounded-full shadow-sm"
          style={{
            left: `${dot.x}%`,
            top: `${dot.y}%`,
            transform: 'translate(-50%, -50%)',
          }}
          variants={dotVariants}
          initial="initial"
          animate="animate"
          custom={dot.delay}
          data-oid="4:70.us"
        />
      ))}

      {/* Floating elements */}
      {floatingElements.map((element, index) => (
        <motion.div
          key={index}
          className="absolute rounded-full bg-green-500/20 blur-sm"
          style={{
            width: element.size * 4,
            height: element.size * 4,
            left: `${element.x}%`,
            top: `${element.y}%`,
            transform: 'translate(-50%, -50%)',
          }}
          variants={floatingVariants}
          initial="initial"
          animate="animate"
          custom={{ duration: element.duration, delay: element.delay }}
          data-oid="941sd41"
        />
      ))}

      {/* Additional animated background elements */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-32 h-32 rounded-full bg-gradient-to-r from-green-500/10 to-transparent blur-2xl"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.2, 0.4, 0.2],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
        data-oid="b1ypymi"
      />

      <motion.div
        className="absolute bottom-1/3 right-1/4 w-40 h-40 rounded-full bg-gradient-to-r from-green-400/10 to-transparent blur-2xl"
        animate={{
          scale: [1.2, 1, 1.2],
          opacity: [0.3, 0.1, 0.3],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: 'easeInOut',
          delay: 2,
        }}
        data-oid="t1i0pe1"
      />

      {/* Wave-like pattern overlay */}
      <div className="absolute inset-0 opacity-10" data-oid="2ag-ubb">
        <svg
          className="w-full h-full"
          viewBox="0 0 100 100"
          preserveAspectRatio="none"
          data-oid="p5y569y"
        >
          <defs data-oid="obe2uf2">
            <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="0%" data-oid="5g30uo1">
              <stop offset="0%" stopColor="#10b981" stopOpacity="0.1" data-oid="hv.i9kk" />
              <stop offset="50%" stopColor="#10b981" stopOpacity="0.3" data-oid="u3._c7z" />
              <stop offset="100%" stopColor="#10b981" stopOpacity="0.1" data-oid="p-90uj-" />
            </linearGradient>
          </defs>
          <path
            d="M0,50 Q25,30 50,50 T100,50 L100,100 L0,100 Z"
            fill="url(#waveGradient)"
            data-oid="e5r:w97"
          />
        </svg>
      </div>
    </div>
  );
}
