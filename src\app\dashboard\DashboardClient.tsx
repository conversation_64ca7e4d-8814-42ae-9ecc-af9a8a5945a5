'use client';

import React from 'react';
import { UserButton } from '@clerk/nextjs';
import {
  BarChart3,
  Users,
  MessageSquare,
  Settings,
  Activity,
  TrendingUp,
  Bug,
  Lightbulb,
  Heart,
  Shield,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useFeatureFlags } from '@/lib/feature-flags';
import { FeedbackWidget } from '@/components/feedback/FeedbackWidget';

interface DashboardClientProps {
  userId: string;
  userRole: string;
  userEmail: string;
}

export function DashboardClient({ userRole, userEmail }: DashboardClientProps) {
  const { flags, isLoading } = useFeatureFlags();

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800';
      case 'developer':
        return 'bg-purple-100 text-purple-800';
      case 'beta-tester':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return Shield;
      case 'developer':
        return Settings;
      case 'beta-tester':
        return Activity;
      default:
        return Users;
    }
  };

  const mockStats = {
    totalUsers: 127,
    activeSessions: 23,
    feedbackCount: 45,
    bugReports: 12,
    featureRequests: 18,
    generalFeedback: 15,
  };

  const RoleIcon = getRoleIcon(userRole);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center" data-oid="0cegi2i">
        <div
          className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"
          data-oid="8m2_96k"
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50" data-oid="b8rja5f">
      {/* Header */}
      <header className="bg-white shadow-sm border-b" data-oid="0rtxifb">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-oid="ps3qq0y">
          <div className="flex justify-between items-center h-16" data-oid="gjuigu_">
            <div className="flex items-center space-x-4" data-oid="xccikm7">
              <h1 className="text-2xl font-bold text-gray-900" data-oid="uo12vtx">
                GGsim Beta Dashboard
              </h1>
              <div
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(userRole)}`}
                data-oid="14dqa5t"
              >
                <RoleIcon className="h-3 w-3 mr-1" data-oid="ptkenrg" />
                {userRole || 'beta-tester'}
              </div>
            </div>
            <div className="flex items-center space-x-4" data-oid="jgfp2er">
              <span className="text-sm text-gray-600" data-oid="8z2riek">
                {userEmail}
              </span>
              <UserButton afterSignOutUrl="/" data-oid="txj-4w0" />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" data-oid="ln5owx4">
        {/* Welcome Section */}
        <div className="mb-8" data-oid="dp2ohut">
          <h2 className="text-3xl font-bold text-gray-900 mb-2" data-oid="3afz9w5">
            Welcome to GGsim Beta! 👋
          </h2>
          <p className="text-gray-600" data-oid="_4p-07s">
            Thank you for participating in our beta testing program. Your feedback helps us build a
            better eSIM platform.
          </p>
        </div>

        {/* Stats Grid */}
        {(userRole === 'admin' || userRole === 'developer') && (
          <div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
            data-oid="a.34et."
          >
            <Card data-oid=":t5bjgu">
              <CardHeader
                className="flex flex-row items-center justify-between space-y-0 pb-2"
                data-oid="vvk-sz-"
              >
                <CardTitle className="text-sm font-medium" data-oid="5..nc.7">
                  Total Beta Users
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" data-oid="gnf.fvt" />
              </CardHeader>
              <CardContent data-oid="q:6rumu">
                <div className="text-2xl font-bold" data-oid="m:w1j53">
                  {mockStats.totalUsers}
                </div>
                <p className="text-xs text-muted-foreground" data-oid="uji0pr:">
                  +12% from last week
                </p>
              </CardContent>
            </Card>

            <Card data-oid="snti9l4">
              <CardHeader
                className="flex flex-row items-center justify-between space-y-0 pb-2"
                data-oid="bc7g.h0"
              >
                <CardTitle className="text-sm font-medium" data-oid="eqhdljw">
                  Active Sessions
                </CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" data-oid="to_cpbc" />
              </CardHeader>
              <CardContent data-oid="djwvao8">
                <div className="text-2xl font-bold" data-oid="hgixxlq">
                  {mockStats.activeSessions}
                </div>
                <p className="text-xs text-muted-foreground" data-oid="hc-sv9j">
                  Currently online
                </p>
              </CardContent>
            </Card>

            <Card data-oid="00pbgdm">
              <CardHeader
                className="flex flex-row items-center justify-between space-y-0 pb-2"
                data-oid="d9d4i2z"
              >
                <CardTitle className="text-sm font-medium" data-oid="gjv9ws:">
                  Total Feedback
                </CardTitle>
                <MessageSquare className="h-4 w-4 text-muted-foreground" data-oid="-ep1h4l" />
              </CardHeader>
              <CardContent data-oid="x.kn-jh">
                <div className="text-2xl font-bold" data-oid="m6oo7-4">
                  {mockStats.feedbackCount}
                </div>
                <p className="text-xs text-muted-foreground" data-oid="xp5odg2">
                  +8 this week
                </p>
              </CardContent>
            </Card>

            <Card data-oid="6bkpe8_">
              <CardHeader
                className="flex flex-row items-center justify-between space-y-0 pb-2"
                data-oid="nxtsysm"
              >
                <CardTitle className="text-sm font-medium" data-oid="7-_u924">
                  Engagement Rate
                </CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" data-oid="kktpb4a" />
              </CardHeader>
              <CardContent data-oid="ddgfr_m">
                <div className="text-2xl font-bold" data-oid="4uuujte">
                  87%
                </div>
                <p className="text-xs text-muted-foreground" data-oid="lg.n0-4">
                  +5% from last week
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Feature Access Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8" data-oid="riklnxa">
          {/* Available Features */}
          <Card data-oid="z9gbfp:">
            <CardHeader data-oid="t7v_u6d">
              <CardTitle data-oid="xmcih7k">Available Features</CardTitle>
              <CardDescription data-oid="6gt5dja">
                Features currently enabled for your role
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4" data-oid="p63-:6c">
              {flags['enable-search'] && (
                <div
                  className="flex items-center justify-between p-3 bg-green-50 rounded-lg"
                  data-oid="mql:b3."
                >
                  <div className="flex items-center space-x-3" data-oid="2_pu04.">
                    <div className="w-2 h-2 bg-green-500 rounded-full" data-oid="6ac1kbd" />
                    <span className="font-medium" data-oid="lyov37a">
                      eSIM Search
                    </span>
                  </div>
                  <Button size="sm" asChild data-oid="xhuwekv">
                    <a href="/search" data-oid="383k-de">
                      Try Now
                    </a>
                  </Button>
                </div>
              )}

              {flags['enable-user-dashboard'] && (
                <div
                  className="flex items-center justify-between p-3 bg-green-50 rounded-lg"
                  data-oid="9fnlycm"
                >
                  <div className="flex items-center space-x-3" data-oid="r-ncjpp">
                    <div className="w-2 h-2 bg-green-500 rounded-full" data-oid="bgqh27u" />
                    <span className="font-medium" data-oid="jn74i-4">
                      User Dashboard
                    </span>
                  </div>
                  <span className="text-sm text-green-600" data-oid="89q-dz7">
                    Active
                  </span>
                </div>
              )}

              {flags['enable-feedback-widget'] && (
                <div
                  className="flex items-center justify-between p-3 bg-green-50 rounded-lg"
                  data-oid="24q73ms"
                >
                  <div className="flex items-center space-x-3" data-oid="lu8ulnj">
                    <div className="w-2 h-2 bg-green-500 rounded-full" data-oid="uqn8dd7" />
                    <span className="font-medium" data-oid="atnr8m4">
                      Feedback System
                    </span>
                  </div>
                  <span className="text-sm text-green-600" data-oid="1u:c769">
                    Active
                  </span>
                </div>
              )}

              {!flags['enable-checkout'] && (
                <div
                  className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg"
                  data-oid="pzuf:d1"
                >
                  <div className="flex items-center space-x-3" data-oid="2u6eek_">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full" data-oid="dyc_msb" />
                    <span className="font-medium" data-oid="mucm8:x">
                      Checkout Process
                    </span>
                  </div>
                  <span className="text-sm text-yellow-600" data-oid="byowe_q">
                    Coming Soon
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Feedback Summary */}
          {(userRole === 'admin' || userRole === 'developer') && (
            <Card data-oid="53mjhy5">
              <CardHeader data-oid="vs:o4l3">
                <CardTitle data-oid="br4kaw1">Feedback Summary</CardTitle>
                <CardDescription data-oid="m.hmrqs">
                  Recent feedback from beta testers
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4" data-oid="9iqyjho">
                <div className="flex items-center justify-between" data-oid="s2yerk3">
                  <div className="flex items-center space-x-3" data-oid="1bvcg45">
                    <Bug className="h-5 w-5 text-red-500" data-oid="aj15cpa" />
                    <span data-oid="_h9rquw">Bug Reports</span>
                  </div>
                  <span className="font-semibold" data-oid="y3_z038">
                    {mockStats.bugReports}
                  </span>
                </div>

                <div className="flex items-center justify-between" data-oid="zbun6qb">
                  <div className="flex items-center space-x-3" data-oid="y8u-84z">
                    <Lightbulb className="h-5 w-5 text-yellow-500" data-oid="a4uc1o5" />
                    <span data-oid="5j-kfr_">Feature Requests</span>
                  </div>
                  <span className="font-semibold" data-oid="27b3ujj">
                    {mockStats.featureRequests}
                  </span>
                </div>

                <div className="flex items-center justify-between" data-oid="g_wtf.v">
                  <div className="flex items-center space-x-3" data-oid="1m8d7dn">
                    <Heart className="h-5 w-5 text-pink-500" data-oid="xfy0z3c" />
                    <span data-oid="010w3mj">Positive Feedback</span>
                  </div>
                  <span className="font-semibold" data-oid="-hu97ee">
                    {mockStats.generalFeedback}
                  </span>
                </div>

                <Button className="w-full mt-4" asChild data-oid="11irzz:">
                  <a href="/admin/feedback" data-oid="9nn67gv">
                    View All Feedback
                  </a>
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Quick Actions */}
        <Card data-oid="o6kt4na">
          <CardHeader data-oid="zhptw-4">
            <CardTitle data-oid="t3-zowh">Quick Actions</CardTitle>
            <CardDescription data-oid=".i57d-h">Common tasks and navigation</CardDescription>
          </CardHeader>
          <CardContent data-oid="z-7flm9">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4" data-oid="h6xw42c">
              <Button variant="outline" className="h-20 flex flex-col" asChild data-oid="6t3wees">
                <a href="/hero-demo" data-oid="lp4gxkw">
                  <BarChart3 className="h-6 w-6 mb-2" data-oid=":kmp7tj" />
                  View Hero Demo
                </a>
              </Button>

              {flags['enable-search'] && (
                <Button variant="outline" className="h-20 flex flex-col" asChild data-oid="0f3k0k1">
                  <a href="/search" data-oid="zo4w14q">
                    <Activity className="h-6 w-6 mb-2" data-oid="_:7yv5_" />
                    Search eSIMs
                  </a>
                </Button>
              )}

              <Button variant="outline" className="h-20 flex flex-col" asChild data-oid="rribuw-">
                <a href="/profile" data-oid="if2_1mg">
                  <Settings className="h-6 w-6 mb-2" data-oid=".9:v5wr" />
                  Profile Settings
                </a>
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>

      {/* Feedback Widget */}
      <FeedbackWidget data-oid="d-d.guy" />
    </div>
  );
}
