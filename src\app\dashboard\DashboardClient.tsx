'use client'

import React from 'react'
import { UserButton } from '@clerk/nextjs'
import { 
  BarChart3, 
  Users, 
  MessageSquare, 
  Settings, 
  Activity,
  TrendingUp,
  Bug,
  Lightbulb,
  Heart,
  Shield
} from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useFeatureFlags } from '@/lib/feature-flags'
import { FeedbackWidget } from '@/components/feedback/FeedbackWidget'

interface DashboardClientProps {
  userId: string
  userRole: string
  userEmail: string
}

export function DashboardClient({ userRole, userEmail }: DashboardClientProps) {
  const { flags, isLoading } = useFeatureFlags()

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800'
      case 'developer':
        return 'bg-purple-100 text-purple-800'
      case 'beta-tester':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return Shield
      case 'developer':
        return Settings
      case 'beta-tester':
        return Activity
      default:
        return Users
    }
  }

  const mockStats = {
    totalUsers: 127,
    activeSessions: 23,
    feedbackCount: 45,
    bugReports: 12,
    featureRequests: 18,
    generalFeedback: 15,
  }

  const RoleIcon = getRoleIcon(userRole)

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900">GGsim Beta Dashboard</h1>
              <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(userRole)}`}>
                <RoleIcon className="h-3 w-3 mr-1" />
                {userRole || 'beta-tester'}
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">{userEmail}</span>
              <UserButton afterSignOutUrl="/" />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome to GGsim Beta! 👋
          </h2>
          <p className="text-gray-600">
            Thank you for participating in our beta testing program. Your feedback helps us build a better eSIM platform.
          </p>
        </div>

        {/* Stats Grid */}
        {(userRole === 'admin' || userRole === 'developer') && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Beta Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{mockStats.totalUsers}</div>
                <p className="text-xs text-muted-foreground">
                  +12% from last week
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{mockStats.activeSessions}</div>
                <p className="text-xs text-muted-foreground">
                  Currently online
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Feedback</CardTitle>
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{mockStats.feedbackCount}</div>
                <p className="text-xs text-muted-foreground">
                  +8 this week
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Engagement Rate</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">87%</div>
                <p className="text-xs text-muted-foreground">
                  +5% from last week
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Feature Access Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Available Features */}
          <Card>
            <CardHeader>
              <CardTitle>Available Features</CardTitle>
              <CardDescription>
                Features currently enabled for your role
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {flags['enable-search'] && (
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span className="font-medium">eSIM Search</span>
                  </div>
                  <Button size="sm" asChild>
                    <a href="/search">Try Now</a>
                  </Button>
                </div>
              )}

              {flags['enable-user-dashboard'] && (
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span className="font-medium">User Dashboard</span>
                  </div>
                  <span className="text-sm text-green-600">Active</span>
                </div>
              )}

              {flags['enable-feedback-widget'] && (
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span className="font-medium">Feedback System</span>
                  </div>
                  <span className="text-sm text-green-600">Active</span>
                </div>
              )}

              {!flags['enable-checkout'] && (
                <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                    <span className="font-medium">Checkout Process</span>
                  </div>
                  <span className="text-sm text-yellow-600">Coming Soon</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Feedback Summary */}
          {(userRole === 'admin' || userRole === 'developer') && (
            <Card>
              <CardHeader>
                <CardTitle>Feedback Summary</CardTitle>
                <CardDescription>
                  Recent feedback from beta testers
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Bug className="h-5 w-5 text-red-500" />
                    <span>Bug Reports</span>
                  </div>
                  <span className="font-semibold">{mockStats.bugReports}</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Lightbulb className="h-5 w-5 text-yellow-500" />
                    <span>Feature Requests</span>
                  </div>
                  <span className="font-semibold">{mockStats.featureRequests}</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Heart className="h-5 w-5 text-pink-500" />
                    <span>Positive Feedback</span>
                  </div>
                  <span className="font-semibold">{mockStats.generalFeedback}</span>
                </div>

                <Button className="w-full mt-4" asChild>
                  <a href="/admin/feedback">View All Feedback</a>
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and navigation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button variant="outline" className="h-20 flex flex-col" asChild>
                <a href="/hero-demo">
                  <BarChart3 className="h-6 w-6 mb-2" />
                  View Hero Demo
                </a>
              </Button>

              {flags['enable-search'] && (
                <Button variant="outline" className="h-20 flex flex-col" asChild>
                  <a href="/search">
                    <Activity className="h-6 w-6 mb-2" />
                    Search eSIMs
                  </a>
                </Button>
              )}

              <Button variant="outline" className="h-20 flex flex-col" asChild>
                <a href="/profile">
                  <Settings className="h-6 w-6 mb-2" />
                  Profile Settings
                </a>
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>

      {/* Feedback Widget */}
      <FeedbackWidget />
    </div>
  )
}
