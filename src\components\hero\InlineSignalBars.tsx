/**
 * Inline Signal Bars Component
 *
 * Displays animated signal bars that fill sequentially to simulate
 * connection progress. Designed to be used inline with text.
 */
'use client';

import React from 'react';
import { motion, useReducedMotion } from 'framer-motion';

export function InlineSignalBars() {
  const shouldReduceMotion = useReducedMotion();

  // Signal bar configuration - heights and animation delays
  const bars = [
    { height: 12, delay: 0 },
    { height: 16, delay: 0.3 },
    { height: 20, delay: 0.6 },
    { height: 24, delay: 0.9 },
    { height: 28, delay: 1.2 },
  ];

  // Animation variants for sequential bar filling
  const barVariants = {
    initial: {
      scaleY: 0,
      opacity: 0.3,
    },
    animate: (index: number) => ({
      scaleY: [0, 0, 1, 1, 0],
      opacity: [0.3, 0.3, 1, 1, 0.3],
      transition: {
        duration: shouldReduceMotion ? 0 : 2.5,
        delay: shouldReduceMotion ? 0 : index * 0.3,
        repeat: shouldReduceMotion ? 0 : Infinity,
        repeatDelay: 1.5,
        ease: [0.4, 0, 0.2, 1] as any,
        times: [0, 0.2, 0.4, 0.8, 1], // Control timing of keyframes
      },
    }),
  };

  return (
    <div
      className="inline-flex items-end space-x-1 ml-3 mb-1"
      role="img"
      aria-label="Signal strength indicator"
      data-oid="1.g1uvd"
    >
      {bars.map((bar, index) => (
        <div
          key={index}
          className="bg-gray-200 rounded-sm border border-gray-300 relative overflow-hidden shadow-sm"
          style={{
            width: 6,
            height: bar.height,
          }}
          data-oid="yoybn6c"
        >
          {/* Animated fill bar */}
          <motion.div
            className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-green-600 to-green-500 rounded-sm shadow-sm"
            variants={barVariants}
            initial="initial"
            animate="animate"
            custom={index}
            style={{
              transformOrigin: 'bottom',
            }}
            data-oid=".oszthk"
          />
        </div>
      ))}
    </div>
  );
}
