/**
 * Enhanced Global Coverage Component
 *
 * Features a world map SVG with animated pins on each continent
 * and transparent black background with white text
 */
'use client';

import React from 'react';
import { motion, useReducedMotion } from 'framer-motion';
import { ComposableMap, Geographies, Geography, Marker } from 'react-simple-maps';

export function GlobalCoverage() {
  const shouldReduceMotion = useReducedMotion();

  // Continent coordinates for animated pins
  const continentPins = [
    { name: 'North America', coordinates: [-100, 45], delay: 0 },
    { name: 'South America', coordinates: [-60, -15], delay: 0.3 },
    { name: 'Europe', coordinates: [10, 50], delay: 0.6 },
    { name: 'Africa', coordinates: [20, 0], delay: 0.9 },
    { name: 'Asia', coordinates: [100, 35], delay: 1.2 },
    { name: 'Australia', coordinates: [135, -25], delay: 1.5 },
  ];

  // Animation variants for pins
  const pinVariants = {
    initial: { scale: 0, opacity: 0 },
    animate: (delay: number) => ({
      scale: [0, 1.3, 1],
      opacity: [0, 1, 0.9],
      transition: {
        duration: shouldReduceMotion ? 0 : 1.5,
        delay: shouldReduceMotion ? 0 : delay,
        repeat: shouldReduceMotion ? 0 : Infinity,
        repeatDelay: 3,
        ease: [0.4, 0, 0.2, 1] as any,
      },
    }),
  };

  // Pulse ring animation for pins
  const pulseVariants = {
    initial: { scale: 1, opacity: 0.8 },
    animate: (delay: number) => ({
      scale: [1, 2.5, 1],
      opacity: [0.8, 0, 0.8],
      transition: {
        duration: shouldReduceMotion ? 0 : 2,
        delay: shouldReduceMotion ? 0 : delay,
        repeat: shouldReduceMotion ? 0 : Infinity,
        repeatDelay: 3,
        ease: [0.4, 0, 0.2, 1] as any,
      },
    }),
  };

  return (
    <div className="relative w-full h-64 overflow-hidden" data-oid="mdcn0v5">
      {/* World Map - Larger and without container box */}
      <div className="absolute inset-0" data-oid="hke2s.m">
        <ComposableMap
          projection="geoMercator"
          projectionConfig={{
            scale: 120,
            center: [0, 10],
          }}
          className="w-[477px] h-[303px]"
          viewBox="0 0 800 400"
          data-oid="86_z3w1"
        >
          <Geographies
            geography="https://cdn.jsdelivr.net/npm/world-atlas@2/countries-110m.json"
            data-oid="ye8t5ms"
          >
            {({ geographies }) =>
              geographies
                .filter(geo => {
                  // Filter out Antarctica (AQ) and other southern territories
                  const countryCode = geo.properties?.ISO_A2;
                  return countryCode !== 'AQ' && geo.properties?.NAME !== 'Antarctica';
                })
                .map(geo => {})
            }
            <Geography
              key={geo.rsmKey}
              geography={geo}
              fill="rgba(34, 197, 94, 0.08)"
              stroke="rgba(34, 197, 94, 0.3)"
              strokeWidth={0.4}
              style={{
                default: { outline: 'none' },
                hover: { outline: 'none' },
                pressed: { outline: 'none' },
              }}
              data-oid="hzqaw0m"
            />
          </Geographies>

          {/* Animated Pins on Continents */}
          {continentPins.map(pin => (
            <Marker
              key={pin.name}
              coordinates={pin.coordinates as [number, number]}
              data-oid="cnuumyt"
            >
              <motion.g
                variants={pinVariants}
                initial="initial"
                animate="animate"
                custom={pin.delay}
                data-oid="o3_yl:3"
              >
                {/* Pulse Ring */}
                <motion.circle
                  r={8}
                  fill="none"
                  stroke="#22c55e"
                  strokeWidth={2}
                  strokeOpacity={0.6}
                  variants={pulseVariants}
                  initial="initial"
                  animate="animate"
                  custom={pin.delay}
                  data-oid="zs:-y8o"
                />

                {/* Pin Dot */}
                <circle
                  r={3}
                  fill="#22c55e"
                  stroke="#ffffff"
                  strokeWidth={1}
                  className="drop-shadow-lg"
                  data-oid="o.ibj85"
                />

                {/* Pin Glow */}
                <circle
                  r={3}
                  fill="#22c55e"
                  opacity={0.4}
                  className="animate-pulse"
                  data-oid="0rzjfhd"
                />
              </motion.g>
            </Marker>
          ))}
        </ComposableMap>
      </div>

      {/* Coverage Label */}
      <div
        className="absolute bottom-3 left-4 text-sm text-gray-800 font-semibold bg-white/90 px-3 py-1 rounded-lg backdrop-blur-sm shadow-sm border border-gray-200"
        data-oid="63z91sn"
      >
        Global Coverage
      </div>

      {/* Status Indicator */}
      <div
        className="absolute top-3 right-4 flex items-center space-x-2 bg-white/90 px-3 py-1 rounded-lg backdrop-blur-sm shadow-sm border border-gray-200"
        data-oid="macelag"
      >
        <motion.div
          className="w-2 h-2 bg-green-500 rounded-full"
          animate={shouldReduceMotion ? {} : { opacity: [1, 0.3, 1], scale: [1, 1.2, 1] }}
          transition={{ duration: 2, repeat: Infinity, ease: [0.4, 0, 0.2, 1] as any }}
          data-oid="7m:q82c"
        />

        <span className="text-sm text-gray-800 font-semibold" data-oid="hc5v9a5">
          6 Continents
        </span>
      </div>

      {/* Connection Stats */}
      <div
        className="absolute bottom-3 right-4 text-sm text-gray-700 font-semibold bg-white/90 px-3 py-1 rounded-lg backdrop-blur-sm shadow-sm border border-gray-200"
        data-oid="9td8p90"
      >
        200+ Countries
      </div>
    </div>
  );
}
