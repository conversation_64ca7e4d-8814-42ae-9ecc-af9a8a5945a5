/**
 * Main Layout Component
 * 
 * Provides consistent layout structure across all pages with:
 * - Responsive navbar
 * - Main content area with proper spacing
 * - SEO-optimized footer
 * - Accessibility features
 */
'use client'

import React from 'react'
import { Navbar } from './Navbar'
import { Footer } from './Footer'

interface MainLayoutProps {
  children: React.ReactNode
  className?: string
  showNavbar?: boolean
  showFooter?: boolean
}

export function MainLayout({ 
  children, 
  className = '',
  showNavbar = true,
  showFooter = true 
}: MainLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Navigation */}
      {showNavbar && <Navbar />}
      
      {/* Main Content */}
      <main className={`flex-1 ${showNavbar ? 'pt-16' : ''} ${className}`}>
        {children}
      </main>
      
      {/* Footer */}
      {showFooter && <Footer />}
    </div>
  )
}
