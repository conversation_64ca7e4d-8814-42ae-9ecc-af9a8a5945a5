'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { SiteHeader } from '@/components/site-header';

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    // Basic validation
    if (!formData.firstName || !formData.lastName || !formData.email || !formData.password) {
      setError('All fields are required');
      setIsSubmitting(false);
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      setIsSubmitting(false);
      return;
    }

    if (formData.password.length < 6) {
      setError('Password must be at least 6 characters long');
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          password: formData.password,
          role: 'user',
        }),
      });

      if (response.ok) {
        // Registration successful, redirect to login
        router.push('/login?message=Registration successful! Please log in.');
      } else {
        const errorData = await response.json();
        console.error('Registration error:', response.status, errorData);
        setError(
          errorData.message ||
            errorData.errors?.[0]?.message ||
            'Registration failed. Please try again.'
        );
      }
    } catch (err) {
      console.error('Registration fetch error:', err);
      setError('Registration failed. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div
      className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4"
      data-oid="ghip:gh"
    >
      <div className="max-w-md w-full space-y-8" data-oid="_:fy9nh">
        <SiteHeader
          title="Create your account"
          subtitle={
            <>
              Already have an account?{' '}
              <Link
                href="/login"
                className="font-medium text-green-600 hover:text-green-500"
                data-oid="qzsvky7"
              >
                Sign in
              </Link>
            </>
          }
          data-oid="r.nqme2"
        />

        {/* Registration Form */}
        <Card data-oid="jbsz8rl">
          <CardHeader data-oid="xsv00xh">
            <CardTitle data-oid="b:0iun_">Sign up</CardTitle>
            <CardDescription data-oid="v84n1kk">
              Enter your information to create an account
            </CardDescription>
          </CardHeader>
          <CardContent data-oid="ol2n63c">
            <form onSubmit={handleSubmit} className="space-y-4" data-oid="gfwfnvn">
              {error && (
                <Alert variant="destructive" data-oid="6rois18">
                  <AlertDescription data-oid="eezcinh">{error}</AlertDescription>
                </Alert>
              )}

              <div className="grid grid-cols-2 gap-4" data-oid="dehggwc">
                <div className="space-y-2" data-oid="lcs5kv0">
                  <label
                    htmlFor="firstName"
                    className="text-sm font-medium text-gray-700"
                    data-oid="do8t::4"
                  >
                    First Name
                  </label>
                  <Input
                    id="firstName"
                    name="firstName"
                    type="text"
                    required
                    value={formData.firstName}
                    onChange={handleInputChange}
                    placeholder="John"
                    data-oid="xuyvsz2"
                  />
                </div>
                <div className="space-y-2" data-oid=":im-2ty">
                  <label
                    htmlFor="lastName"
                    className="text-sm font-medium text-gray-700"
                    data-oid="o:8hesc"
                  >
                    Last Name
                  </label>
                  <Input
                    id="lastName"
                    name="lastName"
                    type="text"
                    required
                    value={formData.lastName}
                    onChange={handleInputChange}
                    placeholder="Doe"
                    data-oid="mvj9t4u"
                  />
                </div>
              </div>

              <div className="space-y-2" data-oid="-w9_j61">
                <label
                  htmlFor="email"
                  className="text-sm font-medium text-gray-700"
                  data-oid="h93--4u"
                >
                  Email address
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  required
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="<EMAIL>"
                  data-oid="p15vnwd"
                />
              </div>

              <div className="space-y-2" data-oid="7458:do">
                <label
                  htmlFor="password"
                  className="text-sm font-medium text-gray-700"
                  data-oid="be74-x_"
                >
                  Password
                </label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  required
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="••••••••"
                  data-oid="1_6:zv4"
                />
              </div>

              <div className="space-y-2" data-oid="viwpwad">
                <label
                  htmlFor="confirmPassword"
                  className="text-sm font-medium text-gray-700"
                  data-oid="98dq_vi"
                >
                  Confirm Password
                </label>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  required
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  placeholder="••••••••"
                  data-oid="gejj0u1"
                />
              </div>

              <Button type="submit" className="w-full" disabled={isSubmitting} data-oid="ormh80r">
                {isSubmitting ? 'Creating account...' : 'Create account'}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Back to Home */}
        <div className="text-center" data-oid="4rmn:pz">
          <Link href="/" className="text-sm text-gray-600 hover:text-gray-900" data-oid="m7p.d7u">
            ← Back to home
          </Link>
        </div>
      </div>
    </div>
  );
}
