/**
 * Search Page - eSIM Plans Discovery
 *
 * Advanced search and filtering for eSIM plans
 * SEO-optimized with structured data and comprehensive filtering
 */
'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  MapPinIcon,
  CalendarDaysIcon,
  CurrencyDollarIcon,
  SignalIcon,
  StarIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { MainLayout } from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/button';

// Mock data for demonstration
const mockPlans = [
  {
    id: 1,
    provider: 'GlobalConnect',
    country: 'United States',
    region: 'North America',
    data: '5GB',
    duration: '30 days',
    price: 29.99,
    currency: 'USD',
    rating: 4.8,
    reviews: 1247,
    coverage: '99%',
    speed: '5G',
    features: ['Instant Activation', 'Hotspot Enabled', '24/7 Support'],
    popular: true,
  },
  {
    id: 2,
    provider: 'EuroSIM',
    country: 'France',
    region: 'Europe',
    data: '10GB',
    duration: '15 days',
    price: 24.99,
    currency: 'USD',
    rating: 4.6,
    reviews: 892,
    coverage: '98%',
    speed: '4G/5G',
    features: ['EU Roaming', 'Instant Activation', 'Voice Calls'],
    popular: false,
  },
  {
    id: 3,
    provider: 'AsiaLink',
    country: 'Japan',
    region: 'Asia',
    data: '8GB',
    duration: '14 days',
    price: 32.99,
    currency: 'USD',
    rating: 4.9,
    reviews: 2156,
    coverage: '99%',
    speed: '5G',
    features: ['Ultra Fast 5G', 'Unlimited Calls', 'Tourist Support'],
    popular: true,
  },
];

const countries = [
  'United States',
  'United Kingdom',
  'France',
  'Germany',
  'Spain',
  'Italy',
  'Japan',
  'South Korea',
  'Australia',
  'Canada',
  'Mexico',
  'Brazil',
  'Thailand',
  'Singapore',
  'India',
  'China',
  'Turkey',
  'Greece',
];

export function SearchClient() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCountry, setSelectedCountry] = useState('');
  const [priceRange, setPriceRange] = useState([0, 100]);
  const [dataRange, setDataRange] = useState('');
  const [duration, setDuration] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  return (
    <MainLayout data-oid="k5rl58v">
      {/* Hero Search Section */}
      <section className="bg-gradient-to-b from-white to-gray-50 py-20" data-oid="0g:i27y">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8" data-oid="i_-7m3-">
          <div className="max-w-4xl mx-auto text-center" data-oid="3-dk4:_">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              data-oid="ankj490"
            >
              <h1
                className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6"
                data-oid="idphgir"
              >
                Find Your Perfect{' '}
                <span
                  className="bg-gradient-to-r from-green-600 to-green-500 bg-clip-text text-transparent"
                  data-oid="anko-05"
                >
                  eSIM Plan
                </span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto" data-oid="knvj2:6">
                Search through thousands of eSIM plans from trusted providers worldwide. Compare
                prices, coverage, and features to find the perfect plan for your journey.
              </p>

              {/* Main Search Bar */}
              <div className="relative max-w-2xl mx-auto mb-8" data-oid="tx1wjwz">
                <div className="relative" data-oid="t4.uhf8">
                  <MagnifyingGlassIcon
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"
                    data-oid="mw4lux6"
                  />

                  <input
                    type="text"
                    placeholder="Search by country, region, or provider..."
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent shadow-sm"
                    data-oid="0xzk_n5"
                  />
                </div>
              </div>

              {/* Quick Filters */}
              <div className="flex flex-wrap justify-center gap-3 mb-8" data-oid="n028rc4">
                {[
                  'Popular Destinations',
                  'Europe',
                  'Asia',
                  'Americas',
                  'Budget Plans',
                  '5G Coverage',
                ].map(filter => (
                  <button
                    key={filter}
                    className="px-4 py-2 bg-white border border-gray-300 rounded-full text-sm font-medium text-gray-700 hover:border-green-500 hover:text-green-600 transition-colors duration-200"
                    data-oid="b0e5569"
                  >
                    {filter}
                  </button>
                ))}
              </div>

              {/* Advanced Filters Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="inline-flex items-center px-4 py-2 text-green-600 hover:text-green-700 font-medium"
                data-oid="7j9z_9:"
              >
                <FunnelIcon className="w-5 h-5 mr-2" data-oid="e519mw2" />
                Advanced Filters
              </button>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Advanced Filters */}
      {showFilters && (
        <motion.section
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="bg-white border-b border-gray-200 py-8"
          data-oid="bzrvqbs"
        >
          <div className="container mx-auto px-4 sm:px-6 lg:px-8" data-oid="wnm-0ua">
            <div className="max-w-6xl mx-auto" data-oid="x26ys5a">
              <div
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
                data-oid="zqa32on"
              >
                {/* Country Filter */}
                <div data-oid="ub1gh3a">
                  <label
                    className="block text-sm font-medium text-gray-700 mb-2"
                    data-oid="25n3h1k"
                  >
                    <MapPinIcon className="w-4 h-4 inline mr-1" data-oid="iur-7-b" />
                    Country
                  </label>
                  <select
                    value={selectedCountry}
                    onChange={e => setSelectedCountry(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    data-oid="a:e:7yz"
                  >
                    <option value="" data-oid="2.ygsl4">
                      All Countries
                    </option>
                    {countries.map(country => (
                      <option key={country} value={country} data-oid=".rtjpbr">
                        {country}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Duration Filter */}
                <div data-oid="q__e-ok">
                  <label
                    className="block text-sm font-medium text-gray-700 mb-2"
                    data-oid="sj1aekt"
                  >
                    <CalendarDaysIcon className="w-4 h-4 inline mr-1" data-oid=".lq2kn3" />
                    Duration
                  </label>
                  <select
                    value={duration}
                    onChange={e => setDuration(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    data-oid="xaaac1r"
                  >
                    <option value="" data-oid="dxaq82e">
                      Any Duration
                    </option>
                    <option value="7" data-oid="0ne5o7y">
                      7 days
                    </option>
                    <option value="14" data-oid="ndmunu0">
                      14 days
                    </option>
                    <option value="30" data-oid="04h:g-m">
                      30 days
                    </option>
                    <option value="90" data-oid="pljprdo">
                      90 days
                    </option>
                  </select>
                </div>

                {/* Data Amount Filter */}
                <div data-oid="15pyxdh">
                  <label
                    className="block text-sm font-medium text-gray-700 mb-2"
                    data-oid="9m2fw8d"
                  >
                    <SignalIcon className="w-4 h-4 inline mr-1" data-oid="46rgzdh" />
                    Data Amount
                  </label>
                  <select
                    value={dataRange}
                    onChange={e => setDataRange(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    data-oid="2y:f-w4"
                  >
                    <option value="" data-oid="mm_508-">
                      Any Amount
                    </option>
                    <option value="1-5" data-oid="z:gh_98">
                      1-5 GB
                    </option>
                    <option value="5-10" data-oid="erep:ux">
                      5-10 GB
                    </option>
                    <option value="10-20" data-oid="io.ggvz">
                      10-20 GB
                    </option>
                    <option value="unlimited" data-oid="xi7n6e3">
                      Unlimited
                    </option>
                  </select>
                </div>

                {/* Price Range Filter */}
                <div data-oid="lmwue_4">
                  <label
                    className="block text-sm font-medium text-gray-700 mb-2"
                    data-oid="ky0wjl2"
                  >
                    <CurrencyDollarIcon className="w-4 h-4 inline mr-1" data-oid="i:b_jyl" />
                    Price Range
                  </label>
                  <div className="space-y-2" data-oid="gxe0t2p">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={priceRange[1]}
                      onChange={e => setPriceRange([0, parseInt(e.target.value)])}
                      className="w-full"
                      data-oid="ackj6qa"
                    />

                    <div className="text-sm text-gray-600" data-oid="q_rdcrr">
                      $0 - ${priceRange[1]}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.section>
      )}

      {/* Search Results */}
      <section className="py-12 bg-gray-50" data-oid="v4uqu3:">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8" data-oid="1:10xsd">
          <div className="max-w-6xl mx-auto" data-oid="j2imzcn">
            {/* Results Header */}
            <div className="flex justify-between items-center mb-8" data-oid="3xui02u">
              <div data-oid="pxy.b11">
                <h2 className="text-2xl font-bold text-gray-900" data-oid="_wwc964">
                  {mockPlans.length} eSIM Plans Found
                </h2>
                <p className="text-gray-600" data-oid="t8omm-s">
                  Showing results for your search criteria
                </p>
              </div>
              <select
                className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                data-oid="8:y6l:p"
              >
                <option data-oid="7i8d6yi">Sort by: Best Match</option>
                <option data-oid="y3.5-bg">Price: Low to High</option>
                <option data-oid="yx_mvwl">Price: High to Low</option>
                <option data-oid="5zbgzc1">Rating: Highest</option>
                <option data-oid="rpvaknw">Data: Most to Least</option>
              </select>
            </div>

            {/* Plan Cards */}
            <div
              className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"
              data-oid="o5-f0eb"
            >
              {mockPlans.map((plan, index) => (
                <motion.div
                  key={plan.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg transition-shadow duration-300 overflow-hidden"
                  data-oid="5wb0i9."
                >
                  {/* Plan Header */}
                  <div className="p-6 border-b border-gray-100" data-oid="m51tuqp">
                    <div className="flex justify-between items-start mb-4" data-oid="gehvo89">
                      <div data-oid="ndgqa3q">
                        <h3 className="text-lg font-semibold text-gray-900" data-oid="-ohbp-q">
                          {plan.country}
                        </h3>
                        <p className="text-sm text-gray-600" data-oid="gydgtg6">
                          {plan.provider}
                        </p>
                      </div>
                      {plan.popular && (
                        <span
                          className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full"
                          data-oid="1my_e-h"
                        >
                          Popular
                        </span>
                      )}
                    </div>

                    {/* Plan Details */}
                    <div className="grid grid-cols-2 gap-4 mb-4" data-oid="01941fk">
                      <div data-oid="dv2_e:-">
                        <p className="text-sm text-gray-600" data-oid="49420sd">
                          Data
                        </p>
                        <p className="font-semibold text-gray-900" data-oid=":xv:f8b">
                          {plan.data}
                        </p>
                      </div>
                      <div data-oid="7k9x01v">
                        <p className="text-sm text-gray-600" data-oid=".m7_fw:">
                          Duration
                        </p>
                        <p className="font-semibold text-gray-900" data-oid="9zmuu:c">
                          {plan.duration}
                        </p>
                      </div>
                      <div data-oid="9keemjn">
                        <p className="text-sm text-gray-600" data-oid="idvenzt">
                          Coverage
                        </p>
                        <p className="font-semibold text-gray-900" data-oid="xg1l_so">
                          {plan.coverage}
                        </p>
                      </div>
                      <div data-oid="x4fq7f0">
                        <p className="text-sm text-gray-600" data-oid="_yf_x.w">
                          Speed
                        </p>
                        <p className="font-semibold text-gray-900" data-oid="y1gaynm">
                          {plan.speed}
                        </p>
                      </div>
                    </div>

                    {/* Rating */}
                    <div className="flex items-center mb-4" data-oid="gtjaqij">
                      <div className="flex items-center" data-oid="w._p9j4">
                        {[...Array(5)].map((_, i) => (
                          <StarIcon
                            key={i}
                            className={`w-4 h-4 ${
                              i < Math.floor(plan.rating)
                                ? 'text-yellow-400 fill-current'
                                : 'text-gray-300'
                            }`}
                            data-oid="7ihupug"
                          />
                        ))}
                      </div>
                      <span className="ml-2 text-sm text-gray-600" data-oid="t_5w9rt">
                        {plan.rating} ({plan.reviews} reviews)
                      </span>
                    </div>

                    {/* Features */}
                    <div className="space-y-1 mb-4" data-oid="kxgxft0">
                      {plan.features.map((feature, idx) => (
                        <div
                          key={idx}
                          className="flex items-center text-sm text-gray-600"
                          data-oid="79b-rwt"
                        >
                          <CheckCircleIcon
                            className="w-4 h-4 text-green-500 mr-2"
                            data-oid="o-:0a.7"
                          />

                          {feature}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Plan Footer */}
                  <div className="p-6" data-oid="s6hcwm9">
                    <div className="flex justify-between items-center" data-oid="zdwolg3">
                      <div data-oid="u:dk_fd">
                        <span className="text-2xl font-bold text-gray-900" data-oid="ejh5c.h">
                          ${plan.price}
                        </span>
                        <span className="text-sm text-gray-600 ml-1" data-oid="-9.os3o">
                          {plan.currency}
                        </span>
                      </div>
                      <Button className="bg-green-600 hover:bg-green-700" data-oid="04-ltpz">
                        Select Plan
                      </Button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Load More */}
            <div className="text-center mt-12" data-oid="-nintma">
              <Button variant="outline" size="lg" data-oid="1e5ghz6">
                Load More Plans
              </Button>
            </div>
          </div>
        </div>
      </section>
    </MainLayout>
  );
}
