/**
 * Search Page - eSIM Plans Discovery
 * 
 * Advanced search and filtering for eSIM plans
 * SEO-optimized with structured data and comprehensive filtering
 */
'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  MagnifyingGlassIcon,
  FunnelIcon,
  MapPinIcon,
  CalendarDaysIcon,
  CurrencyDollarIcon,
  SignalIcon,
  StarIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'
import { MainLayout } from '@/components/layout/MainLayout'
import { Button } from '@/components/ui/button'

// Mock data for demonstration
const mockPlans = [
  {
    id: 1,
    provider: 'GlobalConnect',
    country: 'United States',
    region: 'North America',
    data: '5GB',
    duration: '30 days',
    price: 29.99,
    currency: 'USD',
    rating: 4.8,
    reviews: 1247,
    coverage: '99%',
    speed: '5G',
    features: ['Instant Activation', 'Hotspot Enabled', '24/7 Support'],
    popular: true
  },
  {
    id: 2,
    provider: 'EuroSIM',
    country: 'France',
    region: 'Europe',
    data: '10GB',
    duration: '15 days',
    price: 24.99,
    currency: 'USD',
    rating: 4.6,
    reviews: 892,
    coverage: '98%',
    speed: '4G/5G',
    features: ['EU Roaming', 'Instant Activation', 'Voice Calls'],
    popular: false
  },
  {
    id: 3,
    provider: 'AsiaLink',
    country: 'Japan',
    region: 'Asia',
    data: '8GB',
    duration: '14 days',
    price: 32.99,
    currency: 'USD',
    rating: 4.9,
    reviews: 2156,
    coverage: '99%',
    speed: '5G',
    features: ['Ultra Fast 5G', 'Unlimited Calls', 'Tourist Support'],
    popular: true
  }
]

const countries = [
  'United States', 'United Kingdom', 'France', 'Germany', 'Spain', 'Italy',
  'Japan', 'South Korea', 'Australia', 'Canada', 'Mexico', 'Brazil',
  'Thailand', 'Singapore', 'India', 'China', 'Turkey', 'Greece'
]

const regions = [
  'North America', 'Europe', 'Asia', 'Oceania', 'South America', 'Africa'
]

export function SearchClient() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCountry, setSelectedCountry] = useState('')
  const [selectedRegion, setSelectedRegion] = useState('')
  const [priceRange, setPriceRange] = useState([0, 100])
  const [dataRange, setDataRange] = useState('')
  const [duration, setDuration] = useState('')
  const [showFilters, setShowFilters] = useState(false)

  return (
    <MainLayout>
      {/* Hero Search Section */}
      <section className="bg-gradient-to-b from-white to-gray-50 py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
                Find Your Perfect{' '}
                <span className="bg-gradient-to-r from-green-600 to-green-500 bg-clip-text text-transparent">
                  eSIM Plan
                </span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                Search through thousands of eSIM plans from trusted providers worldwide. 
                Compare prices, coverage, and features to find the perfect plan for your journey.
              </p>

              {/* Main Search Bar */}
              <div className="relative max-w-2xl mx-auto mb-8">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search by country, region, or provider..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent shadow-sm"
                  />
                </div>
              </div>

              {/* Quick Filters */}
              <div className="flex flex-wrap justify-center gap-3 mb-8">
                {['Popular Destinations', 'Europe', 'Asia', 'Americas', 'Budget Plans', '5G Coverage'].map((filter) => (
                  <button
                    key={filter}
                    className="px-4 py-2 bg-white border border-gray-300 rounded-full text-sm font-medium text-gray-700 hover:border-green-500 hover:text-green-600 transition-colors duration-200"
                  >
                    {filter}
                  </button>
                ))}
              </div>

              {/* Advanced Filters Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="inline-flex items-center px-4 py-2 text-green-600 hover:text-green-700 font-medium"
              >
                <FunnelIcon className="w-5 h-5 mr-2" />
                Advanced Filters
              </button>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Advanced Filters */}
      {showFilters && (
        <motion.section
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="bg-white border-b border-gray-200 py-8"
        >
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-6xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Country Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <MapPinIcon className="w-4 h-4 inline mr-1" />
                    Country
                  </label>
                  <select
                    value={selectedCountry}
                    onChange={(e) => setSelectedCountry(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    <option value="">All Countries</option>
                    {countries.map((country) => (
                      <option key={country} value={country}>{country}</option>
                    ))}
                  </select>
                </div>

                {/* Duration Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <CalendarDaysIcon className="w-4 h-4 inline mr-1" />
                    Duration
                  </label>
                  <select
                    value={duration}
                    onChange={(e) => setDuration(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    <option value="">Any Duration</option>
                    <option value="7">7 days</option>
                    <option value="14">14 days</option>
                    <option value="30">30 days</option>
                    <option value="90">90 days</option>
                  </select>
                </div>

                {/* Data Amount Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <SignalIcon className="w-4 h-4 inline mr-1" />
                    Data Amount
                  </label>
                  <select
                    value={dataRange}
                    onChange={(e) => setDataRange(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    <option value="">Any Amount</option>
                    <option value="1-5">1-5 GB</option>
                    <option value="5-10">5-10 GB</option>
                    <option value="10-20">10-20 GB</option>
                    <option value="unlimited">Unlimited</option>
                  </select>
                </div>

                {/* Price Range Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <CurrencyDollarIcon className="w-4 h-4 inline mr-1" />
                    Price Range
                  </label>
                  <div className="space-y-2">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={priceRange[1]}
                      onChange={(e) => setPriceRange([0, parseInt(e.target.value)])}
                      className="w-full"
                    />
                    <div className="text-sm text-gray-600">
                      $0 - ${priceRange[1]}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.section>
      )}

      {/* Search Results */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            {/* Results Header */}
            <div className="flex justify-between items-center mb-8">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">
                  {mockPlans.length} eSIM Plans Found
                </h2>
                <p className="text-gray-600">
                  Showing results for your search criteria
                </p>
              </div>
              <select className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                <option>Sort by: Best Match</option>
                <option>Price: Low to High</option>
                <option>Price: High to Low</option>
                <option>Rating: Highest</option>
                <option>Data: Most to Least</option>
              </select>
            </div>

            {/* Plan Cards */}
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {mockPlans.map((plan, index) => (
                <motion.div
                  key={plan.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg transition-shadow duration-300 overflow-hidden"
                >
                  {/* Plan Header */}
                  <div className="p-6 border-b border-gray-100">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {plan.country}
                        </h3>
                        <p className="text-sm text-gray-600">{plan.provider}</p>
                      </div>
                      {plan.popular && (
                        <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                          Popular
                        </span>
                      )}
                    </div>

                    {/* Plan Details */}
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <p className="text-sm text-gray-600">Data</p>
                        <p className="font-semibold text-gray-900">{plan.data}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Duration</p>
                        <p className="font-semibold text-gray-900">{plan.duration}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Coverage</p>
                        <p className="font-semibold text-gray-900">{plan.coverage}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Speed</p>
                        <p className="font-semibold text-gray-900">{plan.speed}</p>
                      </div>
                    </div>

                    {/* Rating */}
                    <div className="flex items-center mb-4">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <StarIcon
                            key={i}
                            className={`w-4 h-4 ${
                              i < Math.floor(plan.rating)
                                ? 'text-yellow-400 fill-current'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                      <span className="ml-2 text-sm text-gray-600">
                        {plan.rating} ({plan.reviews} reviews)
                      </span>
                    </div>

                    {/* Features */}
                    <div className="space-y-1 mb-4">
                      {plan.features.map((feature, idx) => (
                        <div key={idx} className="flex items-center text-sm text-gray-600">
                          <CheckCircleIcon className="w-4 h-4 text-green-500 mr-2" />
                          {feature}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Plan Footer */}
                  <div className="p-6">
                    <div className="flex justify-between items-center">
                      <div>
                        <span className="text-2xl font-bold text-gray-900">
                          ${plan.price}
                        </span>
                        <span className="text-sm text-gray-600 ml-1">
                          {plan.currency}
                        </span>
                      </div>
                      <Button className="bg-green-600 hover:bg-green-700">
                        Select Plan
                      </Button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Load More */}
            <div className="text-center mt-12">
              <Button variant="outline" size="lg">
                Load More Plans
              </Button>
            </div>
          </div>
        </div>
      </section>
    </MainLayout>
  )
}
