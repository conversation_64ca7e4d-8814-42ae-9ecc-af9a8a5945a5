import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { getProviderManager } from '@/lib/providers/provider-manager'
import { ESIMPurchaseRequest } from '@/lib/providers/base-provider'

export async function POST(request: NextRequest) {
  try {
    const payloadConfig = await config
    const payload = await getPayload({ config: payloadConfig })

    // Get user from the request
    const { user } = await payload.auth({ headers: request.headers })

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { orderId, planId, provider } = body

    // Validate required fields
    if (!orderId || !planId || !provider) {
      return NextResponse.json(
        { error: 'Missing required fields: orderId, planId, provider' },
        { status: 400 }
      )
    }

    // Get the order from database
    const order = await payload.findByID({
      collection: 'orders',
      id: orderId,
    })

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    // Verify order belongs to user
    if (order.user !== user.id) {
      return NextResponse.json(
        { error: 'Order does not belong to user' },
        { status: 403 }
      )
    }

    // Verify order is in correct status
    if (order.status !== 'completed') {
      return NextResponse.json(
        { error: 'Order payment not completed' },
        { status: 400 }
      )
    }

    if ((order as any).esimData?.activationStatus !== 'pending') {
      return NextResponse.json(
        { error: 'eSIM already processed' },
        { status: 400 }
      )
    }

    // Get the eSIM plan details
    const esimPlan = await payload.findByID({
      collection: 'esim-plans' as any,
      id: planId,
    })

    if (!esimPlan) {
      return NextResponse.json(
        { error: 'eSIM plan not found' },
        { status: 404 }
      )
    }

    // Verify provider matches
    if (esimPlan.provider !== provider) {
      return NextResponse.json(
        { error: 'Provider mismatch' },
        { status: 400 }
      )
    }

    const providerManager = getProviderManager()

    // Prepare purchase request
    const purchaseRequest: ESIMPurchaseRequest = {
      planId: esimPlan.providerPlanId, // Use the provider's plan ID
      customerEmail: user.email,
      customerName: `${user.firstName} ${user.lastName}`,
      metadata: {
        orderId: order.id,
        userId: user.id,
        internalPlanId: planId,
      },
    }

    try {
      // Purchase the eSIM from the provider
      const purchaseResponse = await providerManager.purchasePlan(provider, purchaseRequest)

      // Calculate expiry date
      const expiryDate = new Date()
      expiryDate.setDate(expiryDate.getDate() + esimPlan.validity)

      // Update order with eSIM data
      const updatedOrder = await payload.update({
        collection: 'orders',
        id: order.id,
        data: {
          esimData: {
            activationCode: purchaseResponse.activationCode,
            iccid: purchaseResponse.iccid,
            activationStatus: purchaseResponse.status,
            expiryDate: expiryDate.toISOString(),
            providerOrderId: purchaseResponse.orderId,
            activationInstructions: purchaseResponse.activationInstructions,
          },
        } as any,
      })

      // Future: Send activation email to customer
      // await sendActivationEmail(user.email, purchaseResponse)

      return NextResponse.json({
        success: true,
        message: 'eSIM purchased successfully',
        order: {
          id: updatedOrder.id,
          status: updatedOrder.status,
          paymentStatus: (updatedOrder as any).paymentStatus,
          esimData: (updatedOrder as any).esimData,
        },
        esim: {
          activationCode: purchaseResponse.activationCode,
          iccid: purchaseResponse.iccid,
          status: purchaseResponse.status,
          expiryDate: expiryDate.toISOString(),
          activationInstructions: purchaseResponse.activationInstructions,
          networkInfo: purchaseResponse.networkInfo,
        },
      })

    } catch (providerError) {
      console.error('Provider purchase error:', providerError)

      // Update order to indicate provisioning failed
      await payload.update({
        collection: 'orders',
        id: order.id,
        data: {
          esimData: {
            ...(order as any).esimData,
            activationStatus: 'pending',
          },
        } as any,
      })

      return NextResponse.json(
        { 
          error: 'Failed to purchase eSIM from provider',
          code: 'PROVIDER_PURCHASE_ERROR',
          details: process.env.NODE_ENV === 'development' ? (providerError as Error).message : undefined
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('eSIM purchase error:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to purchase eSIM',
        code: 'PURCHASE_ERROR',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      },
      { status: 500 }
    )
  }
}

// Get purchase status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const orderId = searchParams.get('orderId')
    const providerOrderId = searchParams.get('providerOrderId')
    const provider = searchParams.get('provider')

    if (!orderId && !providerOrderId) {
      return NextResponse.json(
        { error: 'Either orderId or providerOrderId is required' },
        { status: 400 }
      )
    }

    const payloadConfig = await config
    const payload = await getPayload({ config: payloadConfig })

    // Get user from the request
    const { user } = await payload.auth({ headers: request.headers })

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    let order
    
    if (orderId) {
      // Get order by internal ID
      order = await payload.findByID({
        collection: 'orders',
        id: orderId,
      })
    } else if (providerOrderId && provider) {
      // Find order by provider order ID
      const orders = await payload.find({
        collection: 'orders',
        where: {
          and: [
            {
              user: {
                equals: user.id,
              },
            },
            {
              'esimData.providerOrderId': {
                equals: providerOrderId,
              },
            },
          ],
        },
        limit: 1,
      })

      if (orders.docs.length > 0) {
        order = orders.docs[0]
      }
    }

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    // Verify order belongs to user
    if (order.user !== user.id) {
      return NextResponse.json(
        { error: 'Order does not belong to user' },
        { status: 403 }
      )
    }

    // If we have provider info, get updated status from provider
    if ((order as any).esimData?.providerOrderId && provider) {
      try {
        const providerManager = getProviderManager()
        const providerStatus = await providerManager.getOrderStatus(
          provider,
          (order as any).esimData.providerOrderId
        )

        // Update order if status changed
        if (providerStatus.status !== (order as any).esimData.activationStatus) {
          await payload.update({
            collection: 'orders',
            id: order.id,
            data: {
              esimData: {
                ...(order as any).esimData,
                activationStatus: providerStatus.status,
              },
            } as any,
          })

          ;(order as any).esimData.activationStatus = providerStatus.status
        }
      } catch (error) {
        console.error('Error getting provider status:', error)
        // Continue with cached status
      }
    }

    return NextResponse.json({
      success: true,
      order: {
        id: order.id,
        status: order.status,
        paymentStatus: (order as any).paymentStatus,
        esimData: (order as any).esimData,
        orderDate: (order as any).orderDate,
        totalAmount: (order as any).totalAmount,
      },
    })

  } catch (error) {
    console.error('Get purchase status error:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to get purchase status',
        code: 'STATUS_ERROR',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      },
      { status: 500 }
    )
  }
}
