import { NextRequest, NextResponse } from 'next/server'
import { getProviderManager } from '@/lib/providers/provider-manager'
import { PlanQuery } from '@/lib/providers/base-provider'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const query: PlanQuery = {
      country: searchParams.get('country') || undefined,
      countryCode: searchParams.get('countryCode') || undefined,
      region: searchParams.get('region') || undefined,
      dataAmount: searchParams.get('dataAmount') ? parseInt(searchParams.get('dataAmount')!) : undefined,
      maxPrice: searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')!) : undefined,
      provider: searchParams.get('provider') || undefined,
      features: searchParams.get('features')?.split(',') || undefined,
    }

    // Validate query - at least one location parameter is required
    if (!query.country && !query.countryCode && !query.region) {
      return NextResponse.json(
        { 
          error: 'At least one location parameter (country, countryCode, or region) is required',
          code: 'INVALID_QUERY'
        },
        { status: 400 }
      )
    }

    const providerManager = getProviderManager()

    // Get sorting preference
    const sortBy = searchParams.get('sortBy') || 'best' // best, price, data, validity
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 50

    let plans
    
    if (sortBy === 'price') {
      // Get cheapest plan first, then all plans sorted by price
      const cheapestPlan = await providerManager.findCheapestPlan(query)
      const allPlans = await providerManager.getAllPlans(query)
      plans = allPlans.sort((a, b) => a.price - b.price).slice(0, limit)
      
      // Add cheapest plan indicator
      if (cheapestPlan && plans.length > 0) {
        plans[0] = { ...plans[0], isCheapest: true } as any
      }
    } else if (sortBy === 'best') {
      // Get best plan first, then all plans sorted by score
      const bestPlan = await providerManager.findBestPlan(query)
      const allPlans = await providerManager.getAllPlans(query)
      plans = allPlans.slice(0, limit)
      
      // Add best plan indicator
      if (bestPlan && plans.length > 0) {
        plans[0] = { ...plans[0], isBest: true } as any
      }
    } else {
      // Get all plans and sort by specified criteria
      const allPlans = await providerManager.getAllPlans(query)
      
      switch (sortBy) {
        case 'data':
          plans = allPlans.sort((a, b) => b.dataAmount - a.dataAmount)
          break
        case 'validity':
          plans = allPlans.sort((a, b) => b.validity - a.validity)
          break
        default:
          plans = allPlans
      }
      
      plans = plans.slice(0, limit)
    }

    // Get provider health status
    const providersHealth = await providerManager.getProvidersHealth()
    const healthyProviders = providersHealth.filter(p => p.healthy).map(p => p.name)
    const unhealthyProviders = providersHealth.filter(p => !p.healthy).map(p => p.name)

    // Calculate statistics
    const stats = {
      totalPlans: plans.length,
      providers: {
        total: providersHealth.length,
        healthy: healthyProviders.length,
        unhealthy: unhealthyProviders.length,
        healthyProviders,
        unhealthyProviders,
      },
      priceRange: plans.length > 0 ? {
        min: Math.min(...plans.map(p => p.price)),
        max: Math.max(...plans.map(p => p.price)),
        average: plans.reduce((sum, p) => sum + p.price, 0) / plans.length,
      } : null,
      dataRange: plans.length > 0 ? {
        min: Math.min(...plans.map(p => p.dataAmount)),
        max: Math.max(...plans.map(p => p.dataAmount)),
      } : null,
      validityRange: plans.length > 0 ? {
        min: Math.min(...plans.map(p => p.validity)),
        max: Math.max(...plans.map(p => p.validity)),
      } : null,
    }

    // Group plans by provider for easier frontend handling
    const plansByProvider = plans.reduce((acc, plan) => {
      if (!acc[plan.provider]) {
        acc[plan.provider] = []
      }
      acc[plan.provider].push(plan)
      return acc
    }, {} as Record<string, typeof plans>)

    return NextResponse.json({
      success: true,
      query,
      plans,
      plansByProvider,
      stats,
      meta: {
        sortBy,
        limit,
        timestamp: new Date().toISOString(),
        cached: false, // Future: Implement Redis caching for improved performance
      },
    })

  } catch (error) {
    console.error('eSIM search error:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to search eSIM plans',
        code: 'SEARCH_ERROR',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const query: PlanQuery = body

    // Validate query
    if (!query.country && !query.countryCode && !query.region) {
      return NextResponse.json(
        { 
          error: 'At least one location parameter (country, countryCode, or region) is required',
          code: 'INVALID_QUERY'
        },
        { status: 400 }
      )
    }

    const providerManager = getProviderManager()

    // For POST requests, we can handle more complex queries
    const plans = await providerManager.getAllPlans(query)

    // Apply additional filtering if specified
    let filteredPlans = plans

    if (body.filters) {
      const { minPrice, maxPrice, minData, maxData, minValidity, maxValidity, providers, features } = body.filters

      filteredPlans = plans.filter(plan => {
        if (minPrice && plan.price < minPrice) return false
        if (maxPrice && plan.price > maxPrice) return false
        if (minData && plan.dataAmount < minData) return false
        if (maxData && plan.dataAmount > maxData) return false
        if (minValidity && plan.validity < minValidity) return false
        if (maxValidity && plan.validity > maxValidity) return false
        if (providers && providers.length > 0 && !providers.includes(plan.provider)) return false
        if (features && features.length > 0) {
          const hasRequiredFeatures = features.every((feature: string) => plan.features.includes(feature))
          if (!hasRequiredFeatures) return false
        }
        return true
      })
    }

    // Apply sorting
    if (body.sortBy) {
      switch (body.sortBy) {
        case 'price_asc':
          filteredPlans.sort((a, b) => a.price - b.price)
          break
        case 'price_desc':
          filteredPlans.sort((a, b) => b.price - a.price)
          break
        case 'data_asc':
          filteredPlans.sort((a, b) => a.dataAmount - b.dataAmount)
          break
        case 'data_desc':
          filteredPlans.sort((a, b) => b.dataAmount - a.dataAmount)
          break
        case 'validity_asc':
          filteredPlans.sort((a, b) => a.validity - b.validity)
          break
        case 'validity_desc':
          filteredPlans.sort((a, b) => b.validity - a.validity)
          break
        case 'score':
        default:
          // Already sorted by score from aggregator
          break
      }
    }

    // Apply pagination
    const page = body.page || 1
    const limit = body.limit || 20
    const offset = (page - 1) * limit
    const paginatedPlans = filteredPlans.slice(offset, offset + limit)

    return NextResponse.json({
      success: true,
      query,
      plans: paginatedPlans,
      pagination: {
        page,
        limit,
        total: filteredPlans.length,
        totalPages: Math.ceil(filteredPlans.length / limit),
        hasNext: offset + limit < filteredPlans.length,
        hasPrev: page > 1,
      },
      meta: {
        timestamp: new Date().toISOString(),
        filtersApplied: !!body.filters,
        sortBy: body.sortBy || 'score',
      },
    })

  } catch (error) {
    console.error('eSIM search POST error:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to search eSIM plans',
        code: 'SEARCH_ERROR',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      },
      { status: 500 }
    )
  }
}
