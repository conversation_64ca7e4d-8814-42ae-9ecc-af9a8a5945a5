import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'

// Define route matchers for public routes
const isPublicRoute = createRouteMatcher([
  '/',
  '/how-it-works',
  '/api/webhooks(.*)',
  '/api/health',
  '/sign-in(.*)',
  '/sign-up(.*)',
])

export default clerkMiddleware((auth, req) => {
  // Allow all routes for now - authentication will be handled per page
  return
})

export const config = {
  matcher: [
    // Skip Next.js internals and all static files
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
  ],
};
