import { NextRequest, NextResponse } from 'next/server';
import { info, warn, error as logError } from '@/lib/logging';

/**
 * API route for client-side error reporting
 * 
 * This endpoint receives error reports from the client-side error handling
 * system and logs them appropriately. In a production environment, this would
 * likely send the errors to a monitoring service like Sentry, LogRocket, etc.
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the error data from the request
    const errorData = await request.json();
    
    // Validate the error data
    if (!errorData || typeof errorData !== 'object') {
      return NextResponse.json(
        { error: 'Invalid error data format' },
        { status: 400 }
      );
    }
    
    // Extract error details
    const { message, stack, severity, timestamp, context } = errorData;
    
    // Log the error with appropriate severity
    switch (severity) {
      case 'critical':
      case 'high':
        logError(`Client Error: ${message}`, { stack, timestamp, ...context });
        break;
      case 'medium':
        warn(`Client Warning: ${message}`, { stack, timestamp, ...context });
        break;
      case 'low':
        info(`Client Info: ${message}`, { stack, timestamp, ...context });
        break;
      default:
        info(`Client Log: ${message}`, { stack, timestamp, ...context });
    }
    
    // In a production environment, you would send this to your error monitoring service
    // Example: await sendToErrorMonitoringService(errorData);
    
    // Return a success response
    return NextResponse.json({ success: true });
  } catch (err) {
    // Log the error that occurred while processing the error report
    console.error('Error in error reporting endpoint:', err);
    
    // Return an error response
    return NextResponse.json(
      { error: 'Failed to process error report' },
      { status: 500 }
    );
  }
}