/**
 * Monitoring and analytics utilities for Next.js application
 * 
 * This module provides utilities to monitor application performance,
 * track errors, and collect analytics data.
 */

import { useEffect } from 'react';

// Configuration for monitoring and analytics
export const monitoringConfig = {
  // Enable/disable monitoring features
  enabled: process.env.NODE_ENV === 'production',
  
  // Error tracking configuration
  errorTracking: {
    enabled: true,
    sampleRate: 1.0, // 100% of errors
    ignorePatterns: [
      // Ignore network errors that might be caused by user navigation
      /Network request failed/i,
      /Failed to fetch/i,
      // Ignore errors from third-party scripts
      /Script error/i,
    ],
  },
  
  // Performance monitoring configuration
  performance: {
    enabled: true,
    sampleRate: 0.1, // 10% of page loads
    trackResources: true,
    trackLCP: true, // Largest Contentful Paint
    trackFID: true, // First Input Delay
    trackCLS: true, // Cumulative Layout Shift
  },
  
  // User analytics configuration
  analytics: {
    enabled: true,
    sampleRate: 1.0, // 100% of users
    trackPageViews: true,
    trackClicks: false,
    trackForms: false,
  },
};

/**
 * Initialize monitoring and analytics
 * This would typically integrate with services like Sentry, LogRocket, or Google Analytics
 */
export function initMonitoring() {
  if (!monitoringConfig.enabled) {
    return;
  }
  
  // This is a placeholder for actual monitoring initialization
  // In a real implementation, you would initialize your monitoring services here
  console.log('Monitoring initialized');
  
  // Set up global error handler
  if (monitoringConfig.errorTracking.enabled && typeof window !== 'undefined') {
    const originalOnError = window.onerror;
    
    window.onerror = (message, source, lineno, colno, error) => {
      // Call original handler if it exists
      if (originalOnError) {
        originalOnError.apply(window, [message, source, lineno, colno, error]);
      }
      
      // Check if we should ignore this error
      const shouldIgnore = monitoringConfig.errorTracking.ignorePatterns.some(
        pattern => pattern.test(message.toString())
      );
      
      if (!shouldIgnore) {
        // In a real implementation, you would send this error to your monitoring service
        console.error('Monitored error:', { message, source, lineno, colno, error });
      }
      
      return true;
    };
  }
}

/**
 * Track a custom event
 * @param eventName Name of the event
 * @param eventData Additional data for the event
 */
export function trackEvent(eventName: string, eventData?: Record<string, any>) {
  if (!monitoringConfig.enabled || !monitoringConfig.analytics.enabled) {
    return;
  }
  
  // Apply sampling
  if (Math.random() > monitoringConfig.analytics.sampleRate) {
    return;
  }
  
  // In a real implementation, you would send this event to your analytics service
  console.log('Tracked event:', eventName, eventData);
}

/**
 * Track a page view
 * @param path Page path
 * @param title Page title
 */
export function trackPageView(path: string, title?: string) {
  if (
    !monitoringConfig.enabled ||
    !monitoringConfig.analytics.enabled ||
    !monitoringConfig.analytics.trackPageViews
  ) {
    return;
  }
  
  // In a real implementation, you would send this page view to your analytics service
  trackEvent('page_view', { path, title });
}

/**
 * Hook to track page views
 */
export function usePageViewTracking() {
  useEffect(() => {
    if (typeof window === 'undefined') {
      return;
    }
    
    // Track initial page view
    trackPageView(window.location.pathname, document.title);
    
    // Initialize monitoring
    initMonitoring();
    
    // Set up performance monitoring
    if (
      monitoringConfig.enabled &&
      monitoringConfig.performance.enabled &&
      'performance' in window &&
      'PerformanceObserver' in window
    ) {
      // Track Web Vitals
      try {
        // Track LCP (Largest Contentful Paint)
        if (monitoringConfig.performance.trackLCP) {
          new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            const lastEntry = entries[entries.length - 1];
            
            // In a real implementation, you would send this metric to your monitoring service
            console.log('LCP:', lastEntry.startTime);
          }).observe({ type: 'largest-contentful-paint', buffered: true });
        }
        
        // Track CLS (Cumulative Layout Shift)
        if (monitoringConfig.performance.trackCLS) {
          let clsValue = 0;
          
          new PerformanceObserver((entryList) => {
            for (const entry of entryList.getEntries()) {
              if (!(entry as any).hadRecentInput) {
                clsValue += (entry as any).value;
              }
            }
            
            // In a real implementation, you would send this metric to your monitoring service
            console.log('CLS:', clsValue);
          }).observe({ type: 'layout-shift', buffered: true });
        }
      } catch (e) {
        console.error('Error setting up performance monitoring:', e);
      }
    }
  }, []);
}
