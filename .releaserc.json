{"branches": ["main", {"name": "develop", "prerelease": "beta"}], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "perf", "release": "patch"}, {"type": "revert", "release": "patch"}, {"type": "docs", "release": false}, {"type": "style", "release": false}, {"type": "chore", "release": false}, {"type": "refactor", "release": "patch"}, {"type": "test", "release": false}, {"type": "build", "release": false}, {"type": "ci", "release": false}, {"scope": "breaking", "release": "major"}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "🚀 Features"}, {"type": "fix", "section": "🐛 Bug Fixes"}, {"type": "perf", "section": "⚡ Performance Improvements"}, {"type": "revert", "section": "⏪ Reverts"}, {"type": "docs", "section": "📚 Documentation", "hidden": false}, {"type": "style", "section": "💄 Styles", "hidden": true}, {"type": "chore", "section": "🔧 Chores", "hidden": true}, {"type": "refactor", "section": "♻️ Code Refactoring"}, {"type": "test", "section": "✅ Tests", "hidden": true}, {"type": "build", "section": "🏗️ Build System", "hidden": true}, {"type": "ci", "section": "👷 CI/CD", "hidden": true}]}}], ["@semantic-release/changelog", {"changelogFile": "CHANGELOG.md", "changelogTitle": "# Changelog\n\nAll notable changes to this project will be documented in this file."}], ["@semantic-release/npm", {"npmPublish": false}], ["@semantic-release/github", {"assets": [{"path": ".next/**", "label": "Build artifacts"}]}], ["@semantic-release/git", {"assets": ["CHANGELOG.md", "package.json", "package-lock.json"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}]]}