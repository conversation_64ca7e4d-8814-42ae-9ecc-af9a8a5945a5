import axios, { AxiosInstance, AxiosResponse } from 'axios'
import NodeCache from 'node-cache'

// Cache instance for provider responses (1 hour TTL)
const cache = new NodeCache({ stdTTL: 3600 })

// Base interfaces for eSIM data
export interface ESIMPlan {
  id: string
  name: string
  country: string
  countryCode: string
  region: string
  dataAmount: number // in GB, 0 for unlimited
  validity: number // in days
  price: number // in USD
  currency: string
  provider: string
  features: string[]
  coverage: string[]
  networkInfo?: {
    operator: string
    networkType: string
    apn?: string
  }
}

export interface ESIMPurchaseRequest {
  planId: string
  customerEmail: string
  customerName?: string
  metadata?: Record<string, any>
}

export interface ESIMPurchaseResponse {
  orderId: string
  activationCode: string // QR code data
  iccid: string
  status: 'pending' | 'ready' | 'activated'
  expiryDate: string
  activationInstructions: string
  networkInfo?: {
    operator: string
    apn: string
    networkType: string
  }
}

export interface PlanQuery {
  country?: string
  countryCode?: string
  region?: string
  dataAmount?: number
  maxPrice?: number
  provider?: string
  features?: string[]
}

export interface ProviderConfig {
  apiKey: string
  apiUrl: string
  environment: 'sandbox' | 'production'
  timeout?: number
  retryAttempts?: number
  retryDelay?: number
}

export interface ProviderError {
  code: string
  message: string
  details?: any
  retryable: boolean
}

// Base provider class that all eSIM providers must extend
export abstract class BaseESIMProvider {
  protected client: AxiosInstance
  protected config: ProviderConfig
  protected providerName: string

  constructor(config: ProviderConfig, providerName: string) {
    this.config = config
    this.providerName = providerName

    // Create axios instance with common configuration
    this.client = axios.create({
      baseURL: config.apiUrl,
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': `GGsim/1.0 (${providerName} Integration)`,
      },
    })

    // Add request interceptor for authentication
    this.client.interceptors.request.use(
      (config) => this.addAuthHeaders(config),
      (error) => Promise.reject(error)
    )

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => this.handleError(error)
    )
  }

  // Abstract methods that each provider must implement
  abstract fetchPlans(query: PlanQuery): Promise<ESIMPlan[]>
  abstract purchasePlan(request: ESIMPurchaseRequest): Promise<ESIMPurchaseResponse>
  abstract getOrderStatus(orderId: string): Promise<ESIMPurchaseResponse>
  abstract addAuthHeaders(config: any): any

  // Common utility methods
  protected async makeRequest<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: any,
    useCache: boolean = true
  ): Promise<T> {
    const cacheKey = `${this.providerName}:${method}:${endpoint}:${JSON.stringify(data || {})}`

    // Check cache for GET requests
    if (method === 'GET' && useCache) {
      const cached = cache.get<T>(cacheKey)
      if (cached) {
        console.log(`Cache hit for ${this.providerName}: ${endpoint}`)
        return cached
      }
    }

    try {
      const response: AxiosResponse<T> = await this.client.request({
        method,
        url: endpoint,
        data,
      })

      // Cache successful GET responses
      if (method === 'GET' && useCache && response.data) {
        cache.set(cacheKey, response.data)
      }

      return response.data
    } catch (error) {
      console.error(`${this.providerName} API error:`, error)
      throw this.transformError(error)
    }
  }

  // Retry logic for failed requests
  protected async retryRequest<T>(
    requestFn: () => Promise<T>,
    maxAttempts: number = this.config.retryAttempts || 3,
    delay: number = this.config.retryDelay || 1000
  ): Promise<T> {
    let lastError: any

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await requestFn()
      } catch (error: any) {
        lastError = error
        
        // Don't retry if error is not retryable
        if (!this.isRetryableError(error)) {
          throw error
        }

        // Don't retry on last attempt
        if (attempt === maxAttempts) {
          break
        }

        // Wait before retrying with exponential backoff
        const waitTime = delay * Math.pow(2, attempt - 1)
        console.log(`${this.providerName}: Retrying in ${waitTime}ms (attempt ${attempt}/${maxAttempts})`)
        await this.sleep(waitTime)
      }
    }

    throw lastError
  }

  // Error handling and transformation
  protected handleError(error: any): Promise<never> {
    const providerError = this.transformError(error)
    return Promise.reject(providerError)
  }

  protected transformError(error: any): ProviderError {
    if (error.response) {
      // HTTP error response
      const status = error.response.status
      const data = error.response.data

      return {
        code: `HTTP_${status}`,
        message: data?.message || data?.error || `HTTP ${status} error`,
        details: data,
        retryable: this.isRetryableHttpStatus(status),
      }
    } else if (error.request) {
      // Network error
      return {
        code: 'NETWORK_ERROR',
        message: 'Network request failed',
        details: error.message,
        retryable: true,
      }
    } else {
      // Other error
      return {
        code: 'UNKNOWN_ERROR',
        message: error.message || 'Unknown error occurred',
        details: error,
        retryable: false,
      }
    }
  }

  protected isRetryableError(error: any): boolean {
    if (error.retryable !== undefined) {
      return error.retryable
    }

    // Retry on network errors and certain HTTP status codes
    if (error.code === 'NETWORK_ERROR') {
      return true
    }

    if (error.response) {
      return this.isRetryableHttpStatus(error.response.status)
    }

    return false
  }

  protected isRetryableHttpStatus(status: number): boolean {
    // Retry on server errors and rate limiting
    return status >= 500 || status === 429
  }

  // Utility methods
  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  protected formatPrice(price: number, _unusedCurrency: string = 'USD'): number {
    // Normalize price to USD for comparison
    // In a real implementation, you'd use exchange rates
    return parseFloat(price.toFixed(2))
  }

  protected validatePlanQuery(query: PlanQuery): void {
    if (!query.country && !query.countryCode && !query.region) {
      throw new Error('At least one of country, countryCode, or region must be specified')
    }
  }

  // Cache management
  protected clearCache(): void {
    const keys = cache.keys().filter(key => key.startsWith(`${this.providerName}:`))
    cache.del(keys)
    console.log(`Cleared ${keys.length} cache entries for ${this.providerName}`)
  }

  protected getCacheStats(): { keys: number; hits: number; misses: number } {
    const stats = cache.getStats()
    return {
      keys: stats.keys,
      hits: stats.hits,
      misses: stats.misses,
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      // Each provider should implement a simple health check endpoint
      await this.makeRequest('GET', '/health', undefined, false)
      return true
    } catch (error) {
      console.error(`${this.providerName} health check failed:`, error)
      return false
    }
  }

  // Get provider info
  getProviderInfo(): { name: string; environment: string; healthy: boolean } {
    return {
      name: this.providerName,
      environment: this.config.environment,
      healthy: true, // This could be updated based on recent request success rates
    }
  }
}
