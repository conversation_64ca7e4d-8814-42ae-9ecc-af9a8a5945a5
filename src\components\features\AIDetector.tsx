'use client';

import { useState } from 'react';
import {
  detectAIContent,
  isLikelyAIGenerated,
  getDetectionExplanation,
} from '@/lib/ai/ai-detection';

type AIDetectorProps = {
  initialContent?: string;
  onResultChange?: (isAIGenerated: boolean) => void;
  className?: string;
};

export default function AIDetector({
  initialContent = '',
  onResultChange,
  className = '',
}: AIDetectorProps) {
  const [content, setContent] = useState(initialContent);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const analyzeContent = async () => {
    if (!content.trim()) {
      setError('Please enter some content to analyze');
      return;
    }

    setIsAnalyzing(true);
    setError(null);

    try {
      const detectionResult = await detectAIContent(content);
      setResult(detectionResult);

      const isAIGenerated = isLikelyAIGenerated(detectionResult);
      if (onResultChange) {
        onResultChange(isAIGenerated);
      }
    } catch (err) {
      setError('An error occurred during analysis. Please try again.');
      console.error('AI detection error:', err);
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <div className={`rounded-lg border p-4 ${className}`} data-oid="l4wdpz3">
      <h2 className="text-xl font-semibold mb-4" data-oid="0b9lskw">
        AI Content Detector
      </h2>

      <div className="mb-4" data-oid="::a_j-1">
        <label htmlFor="content" className="block text-sm font-medium mb-2" data-oid="00igih2">
          Content to analyze
        </label>
        <textarea
          id="content"
          rows={6}
          className="w-full p-2 border rounded-md"
          value={content}
          onChange={e => setContent(e.target.value)}
          placeholder="Paste content here to check if it was generated by AI..."
          data-oid="2xkft88"
        />
      </div>

      <button
        onClick={analyzeContent}
        disabled={isAnalyzing}
        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
        data-oid="s1oj:nw"
      >
        {isAnalyzing ? 'Analyzing...' : 'Analyze Content'}
      </button>

      {error && (
        <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md" data-oid="eb5glxx">
          {error}
        </div>
      )}

      {result && (
        <div className="mt-4 p-4 border rounded-md" data-oid="iax9t3c">
          <h3 className="text-lg font-medium mb-2" data-oid="mqch25u">
            Analysis Results
          </h3>

          <div className="mb-3" data-oid="7z_e2kr">
            <div className="flex items-center justify-between mb-1" data-oid="fh.pshh">
              <span data-oid="bat323u">AI Score:</span>
              <span className="font-semibold" data-oid="260d9fi">
                {(result.score * 100).toFixed(1)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5" data-oid="l9khaif">
              <div
                className={`h-2.5 rounded-full ${isLikelyAIGenerated(result) ? 'bg-red-600' : 'bg-green-600'}`}
                style={{ width: `${result.score * 100}%` }}
                data-oid="e686:sa"
              ></div>
            </div>
          </div>

          <p className="mb-3" data-oid="hiv5_51">
            <span className="font-medium" data-oid="cgcjiju">
              Verdict:{' '}
            </span>
            <span
              className={isLikelyAIGenerated(result) ? 'text-red-600' : 'text-green-600'}
              data-oid="57c0oii"
            >
              {getDetectionExplanation(result)}
            </span>
          </p>

          {result.details?.markers && result.details.markers.length > 0 && (
            <div className="mb-3" data-oid="nvg1vuy">
              <span className="font-medium" data-oid="kw5k-b6">
                Detected markers:{' '}
              </span>
              <span data-oid="lmccw3n">{result.details.markers.join(', ')}</span>
            </div>
          )}

          <div className="text-xs text-gray-500 mt-2" data-oid="viej0xt">
            Analyzed with {result.metadata.modelUsed} v{result.metadata.detectionVersion}
            in {result.metadata.processingTimeMs}ms
          </div>
        </div>
      )}
    </div>
  );
}
