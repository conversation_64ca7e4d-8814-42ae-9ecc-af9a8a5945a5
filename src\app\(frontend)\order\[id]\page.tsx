import { headers as getHeaders } from 'next/headers.js';
import Image from 'next/image';
import { getPayload } from 'payload';
import React from 'react';
import Link from 'next/link';
import { redirect } from 'next/navigation';

import config from '@/payload.config';
import OrderForm from './order-form';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Snack, User } from '@/payload-types'; // Import Snack and User types

interface OrderPageProps {
  params: { id: string };
}

export default async function OrderPage({ params }: OrderPageProps) {
  const { id } = params;
  const headers = await getHeaders();
  const payloadConfig = await config;
  const payload = await getPayload({ config: payloadConfig });
  const { user } = (await payload.auth({ headers })) as { user: User | null }; // Cast user to User | null

  // Redirect to login if not authenticated
  if (!user) {
    redirect('/login');
  }

  // Fetch the specific snack
  const snack = (await payload.findByID({
    collection: 'snacks',
    id,
    depth: 2,
  })) as Snack | null; // Cast snack to Snack | null

  if (!snack || !snack.available) {
    return (
      <div className="min-h-screen bg-gray-50" data-oid="tilxa2y">
        <div className="container mx-auto px-4 py-8" data-oid="vj2m3on">
          <Card data-oid="2goouyg">
            <CardHeader data-oid="4-8-zpi">
              <CardTitle data-oid="xj4e-:l">Snack Not Available</CardTitle>
              <CardDescription data-oid="kvu4f3n">
                Sorry, this snack is not available for ordering.
              </CardDescription>
            </CardHeader>
            <CardContent data-oid="65axjqv">
              <Button asChild data-oid=":5:s7:y">
                <Link href="/" data-oid="kh.z6k:">
                  Back to Home
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50" data-oid="6z4.rk5">
      <div className="container mx-auto px-4 py-8" data-oid="bgum8ji">
        <Button asChild variant="ghost" className="mb-6" data-oid="l7ddl.v">
          <Link href="/" data-oid="gxtdnfh">
            ← Back to Snacks
          </Link>
        </Button>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8" data-oid="znefrwe">
          {/* Snack Details */}
          <Card className="overflow-hidden" data-oid="05du-3k">
            {(snack.image && typeof snack.image === 'object' && snack.image.url) ||
            snack.imageUrl ? ( // Simplified image access
              <div className="aspect-video relative" data-oid="v5xzieu">
                <Image
                  src={
                    snack.image && typeof snack.image === 'object' && snack.image.url
                      ? snack.image.url
                      : snack.imageUrl || '' // Ensure src is always a string
                  }
                  alt={
                    (snack.image && typeof snack.image === 'object'
                      ? snack.image.alt
                      : undefined) || snack.name // Simplified image access
                  }
                  fill
                  className="object-cover"
                  data-oid="obxr6.d"
                />
              </div>
            ) : null}
            <CardHeader data-oid="igwrfvc">
              <div className="flex items-center justify-between" data-oid="33fxt.m">
                <CardTitle className="text-2xl" data-oid="syng4-q">
                  {snack.name}
                </CardTitle>
                <Badge variant="secondary" data-oid="7jjng2f">
                  {snack.category}
                </Badge>
              </div>
              <CardDescription data-oid="e-kb6t_">{snack.description}</CardDescription>
            </CardHeader>
            <CardContent data-oid="sjgheo_">
              <p className="text-2xl font-bold text-green-600" data-oid="ydwa66p">
                ${snack.price.toFixed(2)} each
              </p>
            </CardContent>
          </Card>

          {/* Order Form */}
          <Card data-oid=":-7qnp-">
            <CardHeader data-oid="s8zyt57">
              <CardTitle data-oid="lr:.4-5">Place Your Order</CardTitle>
            </CardHeader>
            <CardContent data-oid="r46o6r.">
              <OrderForm snack={snack} user={user} data-oid="vqbpem3" />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
