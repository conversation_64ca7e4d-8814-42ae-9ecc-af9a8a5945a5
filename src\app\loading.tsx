import Image from 'next/image'
import { InlineSignalBars } from '@/components/hero/InlineSignalBars'

export default function Loading() {
  return (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="text-center">
        <div className="mb-8">
          {/* Logo */}
          <div className="relative w-16 h-16 mx-auto mb-6 animate-pulse">
            <Image
              src="/images/LogoT.png"
              alt="GGsim Logo"
              fill
              className="object-contain"
            />
          </div>

          <h1 className="text-2xl font-bold text-white mb-4">
            Loading Your{' '}
            <span className="bg-gradient-to-r from-green-400 to-green-300 bg-clip-text text-transparent">
              Journey
            </span>
            <InlineSignalBars />
          </h1>
          <p className="text-gray-400">Please wait while we prepare your experience...</p>
        </div>
        
        {/* Loading Animation */}
        <div className="flex justify-center space-x-2">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className="w-3 h-3 bg-green-400 rounded-full animate-bounce"
              style={{
                animationDelay: `${i * 0.2}s`,
                animationDuration: '1s'
              }}
            />
          ))}
        </div>
      </div>
    </div>
  )
}
