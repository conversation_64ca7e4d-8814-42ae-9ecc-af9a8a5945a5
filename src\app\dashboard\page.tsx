import { Metadata } from 'next'
import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { DashboardClient } from './DashboardClient'

export const metadata: Metadata = {
  title: 'Dashboard - GGsim Beta',
  description: 'Your GGsim beta testing dashboard',
}

export default async function DashboardPage() {
  const { userId, sessionClaims } = await auth()

  if (!userId) {
    redirect('/sign-in')
  }

  const userRole = (sessionClaims as any)?.metadata?.role as string
  const userEmail = (sessionClaims as any)?.email as string

  return (
    <DashboardClient 
      userId={userId}
      userRole={userRole}
      userEmail={userEmail}
    />
  )
}
