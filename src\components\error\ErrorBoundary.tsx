'use client';

import React, { useState, useEffect } from 'react';
import { handleError } from '@/lib/error-handling';

type ErrorBoundaryProps = {
  children: React.ReactNode;
  fallback?: React.ReactNode | ((error: Error, reset: () => void) => React.ReactNode);
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  componentName?: string;
};

export function ErrorBoundary({
  children,
  fallback,
  componentName = 'UnnamedComponent',
}: ErrorBoundaryProps) {
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    // Reset error when children change
    setError(null);
  }, [children]);

  // Handle errors in event handlers and async code
  useEffect(() => {
    const errorHandler = (event: ErrorEvent) => {
      const error = event.error || new Error(event.message);
      handleError(error, 'high', { component: componentName });
      setError(error);
      event.preventDefault();
    };

    const rejectionHandler = (event: PromiseRejectionEvent) => {
      const error = event.reason instanceof Error 
        ? event.reason 
        : new Error(String(event.reason));
      handleError(error, 'high', { component: componentName });
      setError(error);
      event.preventDefault();
    };

    window.addEventListener('error', errorHandler);
    window.addEventListener('unhandledrejection', rejectionHandler);

    return () => {
      window.removeEventListener('error', errorHandler);
      window.removeEventListener('unhandledrejection', rejectionHandler);
    };
  }, [componentName]);

  // Reset function to clear the error state
  const reset = () => setError(null);

  // If there's an error, show the fallback UI
  if (error) {
    // If fallback is a function, call it with the error and reset function
    if (typeof fallback === 'function') {
      return <>{fallback(error, reset)}</>;
    }
    
    // If fallback is a React node, render it
    if (fallback) {
      return <>{fallback}</>;
    }
    
    // Default fallback UI
    return (
      <div className="p-4 border border-red-500 rounded-md bg-red-50">
        <h2 className="text-lg font-semibold text-red-700 mb-2">Something went wrong</h2>
        <p className="text-red-600 mb-4">{error.message}</p>
        <button
          onClick={reset}
          className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
        >
          Try again
        </button>
      </div>
    );
  }

  // If there's no error, render the children
  return <>{children}</>;
}

// Class component for compatibility with React's error boundary API
export class ErrorBoundaryClass extends React.Component<ErrorBoundaryProps, { hasError: boolean; error: Error | null }> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error
    handleError(error, 'high', {
      component: this.props.componentName || 'UnnamedComponent',
      componentStack: errorInfo.componentStack,
    });

    // Call the onError prop if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      // Reset function to clear the error state
      const reset = () => this.setState({ hasError: false, error: null });

      // If fallback is a function, call it with the error and reset function
      if (typeof this.props.fallback === 'function' && this.state.error) {
        return <>{this.props.fallback(this.state.error, reset)}</>;
      }
      
      // If fallback is a React node, render it
      if (this.props.fallback) {
        return <>{this.props.fallback}</>;
      }
      
      // Default fallback UI
      return (
        <div className="p-4 border border-red-500 rounded-md bg-red-50">
          <h2 className="text-lg font-semibold text-red-700 mb-2">Something went wrong</h2>
          <p className="text-red-600 mb-4">{this.state.error?.message || 'Unknown error'}</p>
          <button
            onClick={() => this.setState({ hasError: false, error: null })}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Try again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
