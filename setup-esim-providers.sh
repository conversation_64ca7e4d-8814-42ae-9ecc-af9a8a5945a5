#!/bin/bash

# eSIM Provider Setup Script for GGsim
# Run this script to set up your eSIM provider integrations

echo "🚀 GGsim eSIM Provider Setup"
echo "================================"

# Function to prompt for input
prompt_input() {
    local prompt="$1"
    local var_name="$2"
    local default="$3"
    
    if [ -n "$default" ]; then
        read -p "$prompt [$default]: " input
        eval "$var_name=\"${input:-$default}\""
    else
        read -p "$prompt: " input
        eval "$var_name=\"$input\""
    fi
}

# Function to update .env.local
update_env() {
    local key="$1"
    local value="$2"
    
    if grep -q "^$key=" .env.local; then
        # Update existing key
        sed -i "s|^$key=.*|$key=$value|" .env.local
    else
        # Add new key
        echo "$key=$value" >> .env.local
    fi
}

echo ""
echo "📋 Choose which eSIM providers to configure:"
echo "1. Airalo Partners API (Recommended)"
echo "2. 1GLOBAL Connect API"
echo "3. eSIM Go API"
echo "4. All providers"
echo ""

prompt_input "Select option (1-4)" choice "1"

case $choice in
    1|4)
        echo ""
        echo "🌍 Setting up Airalo Partners API..."
        echo "Visit: https://partners.airalo.com/ to get your credentials"
        echo ""
        
        prompt_input "Airalo API Client ID" airalo_client_id
        prompt_input "Airalo API Client Secret" airalo_client_secret
        prompt_input "Environment (sandbox/production)" airalo_env "sandbox"
        
        if [ "$airalo_env" = "production" ]; then
            airalo_url="https://partners-api.airalo.com/v2"
        else
            airalo_url="https://sandbox-partners-api.airalo.com/v2"
        fi
        
        update_env "AIRALO_CLIENT_ID" "$airalo_client_id"
        update_env "AIRALO_CLIENT_SECRET" "$airalo_client_secret"
        update_env "AIRALO_API_URL" "$airalo_url"
        update_env "AIRALO_ENVIRONMENT" "$airalo_env"
        
        echo "✅ Airalo configuration saved!"
        ;;
esac

case $choice in
    2|4)
        echo ""
        echo "🔗 Setting up 1GLOBAL Connect API..."
        echo "Contact: <EMAIL> for API access"
        echo ""
        
        prompt_input "1GLOBAL API Key" oneglobal_api_key
        prompt_input "Environment (sandbox/production)" oneglobal_env "sandbox"
        
        if [ "$oneglobal_env" = "production" ]; then
            oneglobal_url="https://connect.1global.com/api/v3"
        else
            oneglobal_url="https://sandbox.connect.1global.com/api/v3"
        fi
        
        update_env "ONE_GLOBAL_API_KEY" "$oneglobal_api_key"
        update_env "ONE_GLOBAL_API_URL" "$oneglobal_url"
        update_env "ONE_GLOBAL_ENVIRONMENT" "$oneglobal_env"
        
        echo "✅ 1GLOBAL configuration saved!"
        ;;
esac

case $choice in
    3|4)
        echo ""
        echo "📱 Setting up eSIM Go API..."
        echo "Contact: <EMAIL> for API access"
        echo ""
        
        prompt_input "eSIM Go API Key" esimgo_api_key
        prompt_input "Environment (sandbox/production)" esimgo_env "sandbox"
        
        if [ "$esimgo_env" = "production" ]; then
            esimgo_url="https://api.esimgo.com/v1"
        else
            esimgo_url="https://sandbox.esimgo.com/v1"
        fi
        
        update_env "ESIM_GO_API_KEY" "$esimgo_api_key"
        update_env "ESIM_GO_API_URL" "$esimgo_url"
        update_env "ESIM_GO_ENVIRONMENT" "$esimgo_env"
        
        echo "✅ eSIM Go configuration saved!"
        ;;
esac

echo ""
echo "🎉 Setup Complete!"
echo ""
echo "📝 Next Steps:"
echo "1. Restart your development server: npm run dev"
echo "2. Test API connections in your application"
echo "3. Review the provider documentation for integration details"
echo ""
echo "📚 Documentation Links:"
echo "- Airalo: https://developers.partners.airalo.com/"
echo "- 1GLOBAL: https://docs.connect.1global.com/"
echo "- eSIM Go: Contact <EMAIL>"
echo ""
echo "🔐 Security Note:"
echo "Your API keys have been saved to .env.local"
echo "Never commit this file to version control!"
