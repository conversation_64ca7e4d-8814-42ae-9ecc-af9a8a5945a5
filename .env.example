# Server Configuration
NEXT_PUBLIC_SERVER_URL=https://ggsim.vercel.app
PAYLOAD_SECRET=your-payload-secret

# Database Configuration
POSTGRES_URL=your-postgres-connection-string

# Email Configuration
GMAIL_USER=your-gmail-user
GOOGLE_APP_PASSWORD=your-google-app-password
EMAIL_DEFAULT_FROM_NAME=ggsim
FROM_EMAIL=<EMAIL>

# Vercel Configuration
VERCEL_PROJECT_PRODUCTION_URL=ggsim.vercel.app

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_51RvbEVQoZYDuXBHACA6pWLpQRmk9tUm1FPprYwDaFMSmBzUKVpvUEyIWsXdi7QLhgyIShCOGW8xiq6lUeFXp5Ftj00LsZ9aZji
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51RvbEVQoZYDuXBHABa79sy3ZVpVfYyj8mhMx6hFHgAYTuIo1tTQhwKfhfhpk7gW9uBdqXMyzErZgNUz0zrE0VplW000dtIzd8Z
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# eSIM Provider APIs
# eSIM Go
ESIM_GO_API_KEY=your_esim_go_api_key_here
ESIM_GO_API_URL=https://sandbox.esimgo.com/api/v1
ESIM_GO_ENVIRONMENT=sandbox

# 1GLOBAL
ONE_GLOBAL_API_KEY=your_one_global_api_key_here
ONE_GLOBAL_API_URL=https://sandbox.1global.com/api/v1
ONE_GLOBAL_ENVIRONMENT=sandbox

# Telna
TELNA_API_KEY=your_telna_api_key_here
TELNA_API_URL=https://sandbox.telna.com/api/v1
TELNA_ENVIRONMENT=sandbox

# Optional Configuration
DYAD_DISABLE_DB_PUSH=false

# Environment & Beta Testing
NEXT_PUBLIC_ENVIRONMENT=development
NEXT_PUBLIC_BETA_ACCESS=true
NEXT_PUBLIC_DEBUG_MODE=false

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key
CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret

# Analytics & Monitoring
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your_analytics_id
NEXT_PUBLIC_GTM_ID=GTM-XXXXXXX
GOOGLE_VERIFICATION_CODE=your_google_verification_code

# Error Tracking
SENTRY_DSN=https://<EMAIL>/project_id
SENTRY_ORG=your_sentry_org
SENTRY_PROJECT=your_sentry_project
SENTRY_AUTH_TOKEN=your_sentry_auth_token

# Feature Flags
VERCEL_FLAGS_SECRET=your_flags_secret

# Feedback & Notifications
SLACK_FEEDBACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url
FEEDBACK_EMAIL_WEBHOOK_URL=https://api.your-email-service.com/send

# Rate Limiting
REDIS_URL=redis://localhost:6379
UPSTASH_REDIS_REST_URL=https://your-redis-url.upstash.io
UPSTASH_REDIS_REST_TOKEN=your_redis_token

# Production URLs
NEXT_PUBLIC_APP_URL=https://beta.ggsim.me
NEXT_PUBLIC_API_URL=https://beta.ggsim.me/api

# Testing
PLAYWRIGHT_TEST_BASE_URL=http://localhost:3000
CODECOV_TOKEN=your_codecov_token
