import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './button';

describe('Button Component', () => {
  it('renders correctly with default props', () => {
    render(<Button data-oid="x3f2o4f">Click me</Button>);
    const button = screen.getByRole('button', { name: /click me/i });
    expect(button).toBeInTheDocument();
    expect(button).not.toBeDisabled();
    expect(button).toHaveClass('inline-flex items-center justify-center');
  });

  it('applies variant classes correctly', () => {
    render(
      <Button variant="destructive" data-oid="71_.6jo">
        Delete
      </Button>
    );
    const button = screen.getByRole('button', { name: /delete/i });
    expect(button).toHaveClass('bg-destructive');
  });

  it('applies size classes correctly', () => {
    render(
      <Button size="sm" data-oid="2gh5wmt">
        Small Button
      </Button>
    );
    const button = screen.getByRole('button', { name: /small button/i });
    expect(button).toHaveClass('h-9 px-3');
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    render(
      <Button onClick={handleClick} data-oid=".puismf">
        Click me
      </Button>
    );
    const button = screen.getByRole('button', { name: /click me/i });

    fireEvent.click(button);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('renders as disabled when disabled prop is true', () => {
    render(
      <Button disabled data-oid="b506_1r">
        Disabled Button
      </Button>
    );
    const button = screen.getByRole('button', { name: /disabled button/i });

    expect(button).toBeDisabled();
    expect(button).toHaveClass('opacity-50 cursor-default');
  });

  it('renders as a different element when asChild is true', () => {
    render(
      <Button asChild data-oid="77kdwcl">
        <a href="/test" data-oid="me-6k6w">
          Link Button
        </a>
      </Button>
    );

    const link = screen.getByRole('link', { name: /link button/i });
    expect(link).toBeInTheDocument();
    expect(link).toHaveAttribute('href', '/test');
    expect(link).toHaveClass('inline-flex items-center justify-center');
  });
});
