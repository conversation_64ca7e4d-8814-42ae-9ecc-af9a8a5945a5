import type { CollectionConfig } from 'payload'
import { admins, adminsOnly, adminsOrOwner, authenticated } from './access'

export const Orders: CollectionConfig = {
  slug: 'orders',
  admin: {
    useAsTitle: 'id',
  },
  access: {
    read: adminsOrOwner('user'), // Admins can read all orders, users can only read their own
    create: authenticated, // Any authenticated user can create orders
    update: admins, // Only admins can update orders
    delete: admins, // Only admins can delete orders
    admin: adminsOnly,
  },
  fields: [
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'orderType',
      type: 'select',
      options: [
        { label: 'eSIM Plan', value: 'esim' },
        { label: 'Snack', value: 'snack' },
      ],
      defaultValue: 'esim',
      required: true,
      admin: {
        description: 'Type of order - eSIM plan or legacy snack order',
      },
    },
    {
      name: 'items',
      type: 'array',
      fields: [
        {
          name: 'snack',
          type: 'relationship',
          relationTo: 'snacks',
          admin: {
            condition: (_, siblingData) => siblingData?.orderType === 'snack',
          },
        },
        {
          name: 'esimPlan',
          type: 'relationship',
          relationTo: 'esim-plans' as any,
          admin: {
            condition: (_, siblingData) => siblingData?.orderType === 'esim',
          },
        },
        {
          name: 'quantity',
          type: 'number',
          required: true,
          min: 1,
          defaultValue: 1,
        },
      ],
      required: true,
      minRows: 1,
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Pending', value: 'pending' },
        { label: 'Completed', value: 'completed' },
        { label: 'Cancelled', value: 'cancelled' },
      ],
      defaultValue: 'pending',
      required: true,
    },
    {
      name: 'totalAmount',
      type: 'number',
      required: true,
      min: 0,
    },
    {
      name: 'orderDate',
      type: 'date',
      defaultValue: () => new Date(),
      required: true,
    },
    // Payment-related fields
    {
      name: 'paymentStatus',
      type: 'select',
      options: [
        { label: 'Pending', value: 'pending' },
        { label: 'Processing', value: 'processing' },
        { label: 'Completed', value: 'completed' },
        { label: 'Failed', value: 'failed' },
        { label: 'Refunded', value: 'refunded' },
      ],
      defaultValue: 'pending',
      required: true,
      admin: {
        description: 'Payment processing status',
      },
    },
    {
      name: 'stripePaymentIntentId',
      type: 'text',
      admin: {
        description: 'Stripe Payment Intent ID for tracking',
      },
    },
    // eSIM-specific fields
    {
      name: 'esimData',
      type: 'group',
      fields: [
        {
          name: 'activationCode',
          type: 'text',
          admin: {
            description: 'QR code data for eSIM activation',
          },
        },
        {
          name: 'iccid',
          type: 'text',
          admin: {
            description: 'eSIM ICCID (SIM card identifier)',
          },
        },
        {
          name: 'activationStatus',
          type: 'select',
          options: [
            { label: 'Pending', value: 'pending' },
            { label: 'Ready for Activation', value: 'ready' },
            { label: 'Activated', value: 'activated' },
            { label: 'Expired', value: 'expired' },
          ],
          defaultValue: 'pending',
        },
        {
          name: 'activatedAt',
          type: 'date',
          admin: {
            description: 'When the eSIM was activated',
          },
        },
        {
          name: 'expiryDate',
          type: 'date',
          admin: {
            description: 'When the eSIM plan expires',
          },
        },
        {
          name: 'providerOrderId',
          type: 'text',
          admin: {
            description: 'Order ID from the eSIM provider',
          },
        },
      ],
      admin: {
        condition: (data) => data?.orderType === 'esim',
        description: 'eSIM-specific data and activation information',
      },
    },
    {
      name: 'stripeCustomerId',
      type: 'text',
      admin: {
        description: 'Stripe Customer ID',
      },
    },
    // Usage tracking (if supported by provider)
    {
      name: 'usageData',
      type: 'group',
      fields: [
        {
          name: 'dataUsed',
          type: 'number',
          admin: {
            description: 'Data used in MB',
          },
        },
        {
          name: 'dataRemaining',
          type: 'number',
          admin: {
            description: 'Data remaining in MB',
          },
        },
        {
          name: 'lastUpdated',
          type: 'date',
          admin: {
            description: 'Last time usage data was updated',
          },
        },
      ],
      admin: {
        condition: (data) => data?.orderType === 'esim',
        description: 'Data usage tracking (if supported by provider)',
      },
    },
  ],
}
