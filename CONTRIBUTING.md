# Contributing to GGsim

Thank you for your interest in contributing to GGsim! This document provides guidelines and instructions for contributing to the project.

## 🌟 Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct. Please be respectful and considerate of others.

## 🔄 Development Workflow

### Branch Strategy

We follow a simplified Git Flow branching strategy:

- `main`: Production-ready code
- `develop`: Integration branch for features
- `feature/*`: New features and enhancements
- `bugfix/*`: Bug fixes
- `hotfix/*`: Urgent production fixes

### Pull Request Process

1. Fork the repository and create your branch from `develop`.
2. Make your changes and ensure they follow the coding standards.
3. Add tests for your changes.
4. Ensure all tests pass.
5. Update documentation as necessary.
6. Submit a pull request to the `develop` branch.

## 🧪 Testing

All new features and bug fixes should include tests. We use Jest for testing.

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch
```

## 📝 Coding Standards

### TypeScript

- Use TypeScript for all new code.
- Ensure strict type checking is enabled.
- Avoid using `any` type when possible.

### Formatting

We use <PERSON>SL<PERSON> and <PERSON><PERSON><PERSON> for code formatting. Run the following commands before submitting a PR:

```bash
# Lint the code
npm run lint

# Format the code
npm run format
```

### Component Structure

- Use functional components with hooks.
- Keep components small and focused on a single responsibility.
- Use the appropriate folder structure for components:
  - `src/components/ui/` for reusable UI components
  - `src/components/features/` for feature-specific components

## 📚 Documentation

- Update the README.md file with details of changes to the interface.
- Add JSDoc comments to all functions and components.
- Update any relevant documentation in the `docs/` directory.

## 🚀 Deployment

Deployment is handled automatically through our CI/CD pipeline. When a PR is merged to `develop`, a preview deployment is created. When changes are merged to `main`, they are deployed to production.

## 🔍 Code Review Process

All submissions require review. We use GitHub pull requests for this purpose.

1. A maintainer will review your PR.
2. Changes may be requested before a PR can be merged.
3. Once approved, a maintainer will merge your PR.

## 🧠 AI Integration Guidelines

### MCP Server Integration

When working with MCP server integration:

- Follow the patterns established in `src/lib/ai/mcp-server.ts`.
- Ensure proper error handling for all AI-related operations.
- Add comprehensive tests for AI functionality.

### AI Detection

When working with AI detection:

- Follow the patterns established in `src/lib/ai/ai-detection.ts`.
- Ensure detection algorithms are efficient and accurate.
- Consider privacy implications of AI detection.

## 🙏 Thank You

Your contributions are what make the open-source community such an amazing place to learn, inspire, and create. Any contributions you make are greatly appreciated!