import type { CollectionConfig } from 'payload'
import { admins, adminsOnly, anyone } from './access'

export const ESIMPlans: CollectionConfig = {
  slug: 'esim-plans',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'country', 'dataAmount', 'price', 'provider', 'available'],
  },
  access: {
    read: anyone, // Anyone can read eSIM plans (for public viewing)
    create: admins,
    update: admins,
    delete: admins,
    admin: adminsOnly,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      admin: {
        description: 'Display name for the eSIM plan (e.g., "Kuwait 5GB - 30 Days")',
      },
    },
    {
      name: 'country',
      type: 'relationship',
      relationTo: 'countries' as any,
      required: true,
      admin: {
        description: 'Country where this eSIM plan is valid',
      },
    },
    {
      name: 'region',
      type: 'select',
      options: [
        { label: 'Middle East', value: 'middle-east' },
        { label: 'Europe', value: 'europe' },
        { label: 'Asia', value: 'asia' },
        { label: 'North America', value: 'north-america' },
        { label: 'South America', value: 'south-america' },
        { label: 'Africa', value: 'africa' },
        { label: 'Oceania', value: 'oceania' },
        { label: 'Global', value: 'global' },
      ],
      required: true,
      admin: {
        description: 'Geographic region for this plan',
      },
    },
    {
      name: 'dataAmount',
      type: 'number',
      required: true,
      min: 0,
      admin: {
        description: 'Data amount in GB (use 0 for unlimited)',
      },
    },
    {
      name: 'validity',
      type: 'number',
      required: true,
      min: 1,
      admin: {
        description: 'Plan validity in days',
      },
    },
    {
      name: 'price',
      type: 'number',
      required: true,
      min: 0,
      admin: {
        description: 'Price in USD',
      },
    },
    {
      name: 'provider',
      type: 'select',
      options: [
        { label: 'eSIM Go', value: 'esim-go' },
        { label: '1GLOBAL', value: 'one-global' },
        { label: 'Telna', value: 'telna' },
      ],
      required: true,
      admin: {
        description: 'eSIM provider for this plan',
      },
    },
    {
      name: 'providerPlanId',
      type: 'text',
      required: true,
      admin: {
        description: 'External provider\'s plan ID for API calls',
      },
    },
    {
      name: 'coverage',
      type: 'array',
      fields: [
        {
          name: 'country',
          type: 'relationship',
          relationTo: 'countries' as any,
          required: true,
        },
      ],
      admin: {
        description: 'List of countries covered by this plan',
      },
    },
    {
      name: 'available',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Whether this plan is available for purchase',
      },
    },
    {
      name: 'features',
      type: 'array',
      fields: [
        {
          name: 'feature',
          type: 'select',
          options: [
            { label: '5G Network', value: '5g' },
            { label: 'Hotspot/Tethering', value: 'hotspot' },
            { label: 'Voice Calls', value: 'voice' },
            { label: 'SMS', value: 'sms' },
            { label: 'No Speed Throttling', value: 'no-throttling' },
            { label: 'Instant Activation', value: 'instant-activation' },
          ],
          required: true,
        },
      ],
      admin: {
        description: 'Features included with this plan',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      admin: {
        description: 'Detailed description of the plan',
      },
    },
    {
      name: 'activationInstructions',
      type: 'richText',
      admin: {
        description: 'Step-by-step activation instructions for customers',
      },
    },
    {
      name: 'networkInfo',
      type: 'group',
      fields: [
        {
          name: 'operator',
          type: 'text',
          admin: {
            description: 'Local network operator name',
          },
        },
        {
          name: 'apn',
          type: 'text',
          admin: {
            description: 'Access Point Name for manual configuration',
          },
        },
        {
          name: 'networkType',
          type: 'select',
          options: [
            { label: '5G', value: '5g' },
            { label: '4G LTE', value: '4g' },
            { label: '3G', value: '3g' },
          ],
        },
      ],
      admin: {
        description: 'Network configuration details',
      },
    },
    {
      name: 'metadata',
      type: 'group',
      fields: [
        {
          name: 'popularity',
          type: 'number',
          defaultValue: 0,
          admin: {
            description: 'Popularity score for sorting (higher = more popular)',
          },
        },
        {
          name: 'lastUpdated',
          type: 'date',
          defaultValue: () => new Date(),
          admin: {
            description: 'Last time this plan was updated from provider',
          },
        },
        {
          name: 'providerMetadata',
          type: 'json',
          admin: {
            description: 'Additional metadata from the provider API',
          },
        },
      ],
      admin: {
        description: 'Internal metadata for plan management',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Auto-update lastUpdated timestamp
        if (data.metadata) {
          data.metadata.lastUpdated = new Date()
        }
        return data
      },
    ],
  },
}
