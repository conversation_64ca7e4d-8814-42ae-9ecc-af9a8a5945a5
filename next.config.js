import createNextIntlPlugin from 'next-intl/plugin';
import { createRequire } from 'module';

const require = createRequire(import.meta.url);
const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: ['localhost', 'ggsim.vercel.app', 'ggsim.me'],
    formats: ['image/avif', 'image/webp'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
  serverExternalPackages: ['sharp'],
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },
  experimental: {
    // Enable latest performance optimizations
    optimizePackageImports: ['@heroicons/react', 'react-simple-maps'],
  },
  webpack: (config) => {
    config.experiments = {
      ...config.experiments,
      topLevelAwait: true,
    };

    // Fix GraphQL module resolution issues
    config.resolve.alias = {
      ...config.resolve.alias,
      'graphql$': require.resolve('graphql/index.js'),
    };

    // Ignore problematic GraphQL modules during build
    config.externals = config.externals || [];
    if (Array.isArray(config.externals)) {
      config.externals.push({
        'graphql/language/location.mjs': 'commonjs graphql/language/location',
        'graphql/language/printLocation.mjs': 'commonjs graphql/language/printLocation',
        'graphql/error/syntaxError.mjs': 'commonjs graphql/error/syntaxError',
        'graphql/error/locatedError.mjs': 'commonjs graphql/error/locatedError',
      });
    }

    return config;
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains; preload',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
    ];
  },
};

export default withNextIntl(nextConfig);
