'use client';

import React, { useState, useEffect } from 'react';
import { monitoringConfig } from '@/lib/monitoring';

/**
 * Performance metrics interface
 */
interface PerformanceMetrics {
  lcp: number | null; // Largest Contentful Paint
  fid: number | null; // First Input Delay
  cls: number | null; // Cumulative Layout Shift
  ttfb: number | null; // Time to First Byte
  fcp: number | null; // First Contentful Paint
}

/**
 * Component to display performance metrics in an admin dashboard
 */
export function PerformanceDashboard() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    lcp: null,
    fid: null,
    cls: null,
    ttfb: null,
    fcp: null,
  });

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (typeof window === 'undefined' || !monitoringConfig.enabled) {
      return;
    }

    setIsLoading(true);

    // Simulate fetching performance data
    // In a real implementation, this would fetch data from your monitoring service
    setTimeout(() => {
      // Mock data - in a real app, this would come from your monitoring service
      setMetrics({
        lcp: 2.5, // seconds
        fid: 100, // milliseconds
        cls: 0.1, // unitless
        ttfb: 200, // milliseconds
        fcp: 1.2, // seconds
      });
      setIsLoading(false);
    }, 1000);
  }, []);

  /**
   * Get status color based on metric value
   */
  const getStatusColor = (metric: string, value: number | null): string => {
    if (value === null) return 'bg-gray-200';

    switch (metric) {
      case 'lcp':
        return value <= 2.5 ? 'bg-green-500' : value <= 4 ? 'bg-yellow-500' : 'bg-red-500';
      case 'fid':
        return value <= 100 ? 'bg-green-500' : value <= 300 ? 'bg-yellow-500' : 'bg-red-500';
      case 'cls':
        return value <= 0.1 ? 'bg-green-500' : value <= 0.25 ? 'bg-yellow-500' : 'bg-red-500';
      case 'ttfb':
        return value <= 200 ? 'bg-green-500' : value <= 500 ? 'bg-yellow-500' : 'bg-red-500';
      case 'fcp':
        return value <= 1.8 ? 'bg-green-500' : value <= 3 ? 'bg-yellow-500' : 'bg-red-500';
      default:
        return 'bg-gray-200';
    }
  };

  /**
   * Format metric value for display
   */
  const formatMetricValue = (metric: string, value: number | null): string => {
    if (value === null) return 'N/A';

    switch (metric) {
      case 'lcp':
      case 'fcp':
        return `${value.toFixed(2)}s`;
      case 'fid':
      case 'ttfb':
        return `${value.toFixed(0)}ms`;
      case 'cls':
        return value.toFixed(2);
      default:
        return value.toString();
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6" data-oid="_d_fyms">
      <h2 className="text-2xl font-bold mb-6" data-oid="ijgpds_">
        Performance Metrics
      </h2>

      {isLoading ? (
        <div className="flex justify-center items-center h-64" data-oid="5iawhi1">
          <div
            className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
            data-oid="x15de-f"
          ></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" data-oid="t86ypnr">
          {/* LCP Card */}
          <div className="bg-gray-50 rounded-lg p-4 shadow" data-oid="8zwje90">
            <div className="flex justify-between items-center mb-2" data-oid="ul_8vw2">
              <h3 className="font-semibold text-gray-700" data-oid="at9z3i1">
                Largest Contentful Paint
              </h3>
              <div
                className={`w-3 h-3 rounded-full ${getStatusColor('lcp', metrics.lcp)}`}
                data-oid="n6ly_ij"
              ></div>
            </div>
            <p className="text-3xl font-bold" data-oid="r4wptbg">
              {formatMetricValue('lcp', metrics.lcp)}
            </p>
            <p className="text-sm text-gray-500 mt-2" data-oid="tp-:k:8">
              Good: &lt;2.5s | Needs Improvement: 2.5-4s | Poor: &gt;4s
            </p>
          </div>

          {/* FID Card */}
          <div className="bg-gray-50 rounded-lg p-4 shadow" data-oid="r4k163m">
            <div className="flex justify-between items-center mb-2" data-oid="r7mad.z">
              <h3 className="font-semibold text-gray-700" data-oid="17v22b4">
                First Input Delay
              </h3>
              <div
                className={`w-3 h-3 rounded-full ${getStatusColor('fid', metrics.fid)}`}
                data-oid="3zid363"
              ></div>
            </div>
            <p className="text-3xl font-bold" data-oid=":51jqux">
              {formatMetricValue('fid', metrics.fid)}
            </p>
            <p className="text-sm text-gray-500 mt-2" data-oid="sv7nyy0">
              Good: &lt;100ms | Needs Improvement: 100-300ms | Poor: &gt;300ms
            </p>
          </div>

          {/* CLS Card */}
          <div className="bg-gray-50 rounded-lg p-4 shadow" data-oid="x6etqy:">
            <div className="flex justify-between items-center mb-2" data-oid="jf:9oes">
              <h3 className="font-semibold text-gray-700" data-oid="9esww7-">
                Cumulative Layout Shift
              </h3>
              <div
                className={`w-3 h-3 rounded-full ${getStatusColor('cls', metrics.cls)}`}
                data-oid="12yp2ut"
              ></div>
            </div>
            <p className="text-3xl font-bold" data-oid="-iquty0">
              {formatMetricValue('cls', metrics.cls)}
            </p>
            <p className="text-sm text-gray-500 mt-2" data-oid="l0:igca">
              Good: &lt;0.1 | Needs Improvement: 0.1-0.25 | Poor: &gt;0.25
            </p>
          </div>

          {/* TTFB Card */}
          <div className="bg-gray-50 rounded-lg p-4 shadow" data-oid="fia.wwg">
            <div className="flex justify-between items-center mb-2" data-oid="fwsuh:b">
              <h3 className="font-semibold text-gray-700" data-oid="o3od5f9">
                Time to First Byte
              </h3>
              <div
                className={`w-3 h-3 rounded-full ${getStatusColor('ttfb', metrics.ttfb)}`}
                data-oid="bjl0fqb"
              ></div>
            </div>
            <p className="text-3xl font-bold" data-oid="-3r9wws">
              {formatMetricValue('ttfb', metrics.ttfb)}
            </p>
            <p className="text-sm text-gray-500 mt-2" data-oid="jmaz0f-">
              Good: &lt;200ms | Needs Improvement: 200-500ms | Poor: &gt;500ms
            </p>
          </div>

          {/* FCP Card */}
          <div className="bg-gray-50 rounded-lg p-4 shadow" data-oid=":vwd8cr">
            <div className="flex justify-between items-center mb-2" data-oid="32cimkh">
              <h3 className="font-semibold text-gray-700" data-oid="nwq.29j">
                First Contentful Paint
              </h3>
              <div
                className={`w-3 h-3 rounded-full ${getStatusColor('fcp', metrics.fcp)}`}
                data-oid="t.wnv1."
              ></div>
            </div>
            <p className="text-3xl font-bold" data-oid="ses1a:b">
              {formatMetricValue('fcp', metrics.fcp)}
            </p>
            <p className="text-sm text-gray-500 mt-2" data-oid="2dk7lh6">
              Good: &lt;1.8s | Needs Improvement: 1.8-3s | Poor: &gt;3s
            </p>
          </div>
        </div>
      )}

      <div className="mt-8 p-4 bg-blue-50 rounded-lg" data-oid="7:efhkx">
        <h3 className="font-semibold text-blue-700 mb-2" data-oid="hj.fv54">
          About Core Web Vitals
        </h3>
        <p className="text-sm text-blue-600" data-oid="p3-actp">
          Core Web Vitals are a set of metrics that measure real-world user experience for loading
          performance, interactivity, and visual stability. Improving these metrics enhances user
          experience and can positively impact SEO rankings.
        </p>
      </div>
    </div>
  );
}
