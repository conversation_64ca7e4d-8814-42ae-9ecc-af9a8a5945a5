'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import * as Sentry from '@sentry/nextjs';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  eventId: string | null;
}

export class GlobalErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      eventId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to Sentry
    const eventId = Sentry.captureException(error, {
      contexts: {
        react: {
          componentStack: errorInfo.componentStack,
        },
      },
    });

    this.setState({
      errorInfo,
      eventId,
    });

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error caught by boundary:', error);
      console.error('Error info:', errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      eventId: null,
    });
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleReportFeedback = () => {
    if (this.state.eventId) {
      Sentry.showReportDialog({ eventId: this.state.eventId });
    }
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div
          className="min-h-screen flex items-center justify-center bg-gray-50 px-4"
          data-oid="45op27_"
        >
          <div
            className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center"
            data-oid=".-kawtt"
          >
            <div className="flex justify-center mb-4" data-oid="1r3knk3">
              <AlertTriangle className="h-12 w-12 text-red-500" data-oid="5sh-o-o" />
            </div>

            <h1 className="text-xl font-semibold text-gray-900 mb-2" data-oid="hpea8xf">
              Something went wrong
            </h1>

            <p className="text-gray-600 mb-6" data-oid="-:c-h9p">
              We're sorry, but something unexpected happened. Our team has been notified.
            </p>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mb-6 text-left" data-oid="rzm.i3o">
                <summary
                  className="cursor-pointer text-sm font-medium text-gray-700 mb-2"
                  data-oid="8ee2pb_"
                >
                  Error Details (Development)
                </summary>
                <pre
                  className="text-xs bg-gray-100 p-3 rounded overflow-auto max-h-32"
                  data-oid="sc:9dsl"
                >
                  {this.state.error.toString()}
                  {this.state.errorInfo?.componentStack}
                </pre>
              </details>
            )}

            <div className="space-y-3" data-oid="n-am4hl">
              <Button
                onClick={this.handleRetry}
                className="w-full flex items-center justify-center"
                data-oid="7oa0rdv"
              >
                <RefreshCw className="h-4 w-4 mr-2" data-oid="pwae13y" />
                Try Again
              </Button>

              <Button
                onClick={this.handleGoHome}
                variant="outline"
                className="w-full flex items-center justify-center"
                data-oid="f6zk:dx"
              >
                <Home className="h-4 w-4 mr-2" data-oid="z7szo6i" />
                Go Home
              </Button>

              {this.state.eventId && (
                <Button
                  onClick={this.handleReportFeedback}
                  variant="ghost"
                  size="sm"
                  className="w-full text-gray-500"
                  data-oid="4_n7vb9"
                >
                  Report Feedback
                </Button>
              )}
            </div>

            <p className="text-xs text-gray-400 mt-4" data-oid="nxkpajg">
              Error ID: {this.state.eventId || 'Unknown'}
            </p>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook for functional components
export function useErrorHandler() {
  return (error: Error, errorInfo?: ErrorInfo) => {
    Sentry.captureException(error, {
      contexts: errorInfo
        ? {
            react: {
              componentStack: errorInfo.componentStack,
            },
          }
        : undefined,
    });
  };
}

// HOC for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) {
  const WrappedComponent = (props: P) => (
    <GlobalErrorBoundary fallback={fallback} data-oid="quwo_yd">
      <Component {...props} data-oid="ko.dc:o" />
    </GlobalErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}
