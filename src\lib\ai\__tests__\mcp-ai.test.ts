import { createMCPAI, registerMCPServer, executeTool } from '../mcp-ai';

// Mock server and tool definitions
const mockServer = {
  name: 'test-server',
  description: 'A test server for MCP AI',
  tools: [
    {
      name: 'test-tool',
      description: 'A test tool',
      inputSchema: {
        type: 'object',
        properties: {
          testParam: { type: 'string' }
        },
        required: ['testParam']
      }
    }
  ]
};

describe('MCP AI Module', () => {
  beforeEach(() => {
    // Reset the registered servers before each test
    // This assumes there's a way to reset the internal state
    // In a real implementation, you might need to expose a reset function
    jest.resetModules();
  });

  describe('registerMCPServer', () => {
    it('registers a valid server successfully', () => {
      const result = registerMCPServer(mockServer);
      expect(result.success).toBe(true);
      expect(result.message).toContain('successfully registered');
    });

    it('rejects a server with invalid structure', () => {
      const invalidServer = {
        // Missing required fields
        tools: []
      };
      
      const result = registerMCPServer(invalidServer as any);
      expect(result.success).toBe(false);
      expect(result.message).toContain('Invalid server structure');
    });

    it('prevents registering a duplicate server', () => {
      // Register the server first time
      registerMCPServer(mockServer);
      
      // Try to register the same server again
      const result = registerMCPServer(mockServer);
      expect(result.success).toBe(false);
      expect(result.message).toContain('already registered');
    });
  });

  describe('executeTool', () => {
    beforeEach(() => {
      // Register the mock server before each test
      registerMCPServer(mockServer);
    });

    it('executes a tool successfully', async () => {
      // Mock the actual execution function
      const mockExecute = jest.fn().mockResolvedValue({ result: 'success' });
      (executeTool as any).executeToolImplementation = mockExecute;

      const result = await executeTool({
        serverName: 'test-server',
        toolName: 'test-tool',
        args: { testParam: 'test-value' }
      });

      expect(result.success).toBe(true);
      expect(result.data).toEqual({ result: 'success' });
      expect(mockExecute).toHaveBeenCalledWith(
        'test-server',
        'test-tool',
        { testParam: 'test-value' }
      );
    });

    it('fails when server does not exist', async () => {
      const result = await executeTool({
        serverName: 'non-existent-server',
        toolName: 'test-tool',
        args: { testParam: 'test-value' }
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Server not found');
    });

    it('fails when tool does not exist', async () => {
      const result = await executeTool({
        serverName: 'test-server',
        toolName: 'non-existent-tool',
        args: { testParam: 'test-value' }
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Tool not found');
    });
  });

  describe('createMCPAI', () => {
    it('creates an AI instance with registered servers', () => {
      // Register the mock server
      registerMCPServer(mockServer);

      const ai = createMCPAI();
      
      expect(ai).toBeDefined();
      expect(ai.getRegisteredServers).toBeDefined();
      expect(ai.getRegisteredServers()).toContainEqual(expect.objectContaining({
        name: 'test-server'
      }));
    });

    it('creates an AI instance with custom configuration', () => {
      const config = {
        model: 'test-model',
        temperature: 0.7,
        maxTokens: 1000
      };

      const ai = createMCPAI(config);
      
      expect(ai).toBeDefined();
      expect(ai.config).toEqual(expect.objectContaining(config));
    });
  });
});