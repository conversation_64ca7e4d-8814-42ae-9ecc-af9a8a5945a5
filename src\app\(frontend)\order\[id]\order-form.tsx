'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Snack, User } from '@/payload-types'; // Import Snack and User types

interface OrderFormProps {
  snack: Snack;
  user: User;
}

export default function OrderForm({ snack, user }: OrderFormProps) {
  const [quantity, setQuantity] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const totalPrice = (snack.price * quantity).toFixed(2);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user: user.id,
          items: [
            {
              snack: snack.id,
              quantity,
            },
          ],

          totalAmount: parseFloat(totalPrice),
        }),
      });

      if (response.ok) {
        router.push('/my-orders?success=true');
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Failed to place order');
      }
    } catch (err) {
      setError('Failed to place order. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6" data-oid="31mlqpq">
      <div className="space-y-3" data-oid="-thmigh">
        <label htmlFor="quantity" className="text-sm font-medium" data-oid="726uggg">
          Quantity:
        </label>
        <div className="flex items-center gap-2" data-oid="l7iz2-c">
          <Button
            type="button"
            onClick={() => setQuantity(Math.max(1, quantity - 1))}
            disabled={quantity <= 1}
            variant="outline"
            size="sm"
            data-oid="dade93v"
          >
            -
          </Button>
          <Input
            type="number"
            id="quantity"
            value={quantity}
            onChange={e => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
            min="1"
            className="w-20 text-center"
            data-oid="ij5u2bl"
          />

          <Button
            type="button"
            onClick={() => setQuantity(quantity + 1)}
            variant="outline"
            size="sm"
            data-oid="0ugv2vp"
          >
            +
          </Button>
        </div>
      </div>

      <Separator data-oid="r7051k1" />

      <div className="space-y-2" data-oid="krxcg4b">
        <h3 className="text-lg font-semibold" data-oid=":qg6aan">
          Total: ${totalPrice}
        </h3>
      </div>

      {error && (
        <Alert variant="destructive" data-oid="eq:h-ja">
          <AlertDescription data-oid="es7ndq4">{error}</AlertDescription>
        </Alert>
      )}

      <Button type="submit" disabled={isSubmitting} className="w-full" data-oid="0dijcrt">
        {isSubmitting ? 'Placing Order...' : 'Place Order'}
      </Button>
    </form>
  );
}
