/**
 * Airalo Partners API Integration
 * 
 * Official documentation: https://developers.partners.airalo.com/
 * API Version: v2 (Latest 2025)
 */

interface AiraloConfig {
  clientId: string
  clientSecret: string
  baseUrl: string
  environment: 'sandbox' | 'production'
}

interface AiraloAuthResponse {
  access_token: string
  token_type: string
  expires_in: number
}

interface AiraloPackage {
  id: string
  title: string
  country: string
  operator: string
  data: string
  validity: number
  price: number
  currency: string
  type: string
}

interface AiraloOrder {
  id: string
  package_id: string
  quantity: number
  status: string
  esims: Array<{
    iccid: string
    qr_code: string
    manual_installation: {
      sm_dp_address: string
      activation_code: string
    }
  }>
}

export class AiraloAPI {
  private config: AiraloConfig
  private accessToken: string | null = null
  private tokenExpiry: number | null = null

  constructor() {
    this.config = {
      clientId: process.env.AIRALO_CLIENT_ID || '',
      clientSecret: process.env.AIRALO_CLIENT_SECRET || '',
      baseUrl: process.env.AIRALO_API_URL || 'https://sandbox-partners-api.airalo.com/v2',
      environment: (process.env.AIRALO_ENVIRONMENT as 'sandbox' | 'production') || 'sandbox'
    }

    if (!this.config.clientId || !this.config.clientSecret) {
      throw new Error('Airalo API credentials not configured. Please set AIRALO_CLIENT_ID and AIRALO_CLIENT_SECRET environment variables.')
    }
  }

  /**
   * Authenticate with Airalo API using OAuth 2.0 Client Credentials
   */
  private async authenticate(): Promise<string> {
    if (this.accessToken && this.tokenExpiry && Date.now() < this.tokenExpiry) {
      return this.accessToken
    }

    const response = await fetch(`${this.config.baseUrl}/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        grant_type: 'client_credentials',
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret
      })
    })

    if (!response.ok) {
      throw new Error(`Airalo authentication failed: ${response.status} ${response.statusText}`)
    }

    const data: AiraloAuthResponse = await response.json()
    this.accessToken = data.access_token
    this.tokenExpiry = Date.now() + (data.expires_in * 1000) - 60000 // Refresh 1 minute early

    return this.accessToken
  }

  /**
   * Make authenticated API request
   */
  private async apiRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const token = await this.authenticate()

    const response = await fetch(`${this.config.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers
      }
    })

    if (!response.ok) {
      throw new Error(`Airalo API request failed: ${response.status} ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get available eSIM packages
   */
  async getPackages(filters?: {
    country?: string
    type?: 'local' | 'regional' | 'global'
    limit?: number
    page?: number
  }): Promise<{ data: AiraloPackage[], meta: any }> {
    const params = new URLSearchParams()
    
    if (filters?.country) params.append('filter[country]', filters.country)
    if (filters?.type) params.append('filter[type]', filters.type)
    if (filters?.limit) params.append('limit', filters.limit.toString())
    if (filters?.page) params.append('page', filters.page.toString())

    const queryString = params.toString()
    const endpoint = `/packages${queryString ? `?${queryString}` : ''}`

    return this.apiRequest<{ data: AiraloPackage[], meta: any }>(endpoint)
  }

  /**
   * Get package details by ID
   */
  async getPackage(packageId: string): Promise<{ data: AiraloPackage }> {
    return this.apiRequest<{ data: AiraloPackage }>(`/packages/${packageId}`)
  }

  /**
   * Create an eSIM order
   */
  async createOrder(packageId: string, quantity: number = 1): Promise<{ data: AiraloOrder }> {
    return this.apiRequest<{ data: AiraloOrder }>('/orders', {
      method: 'POST',
      body: JSON.stringify({
        package_id: packageId,
        quantity: quantity
      })
    })
  }

  /**
   * Get order details
   */
  async getOrder(orderId: string): Promise<{ data: AiraloOrder }> {
    return this.apiRequest<{ data: AiraloOrder }>(`/orders/${orderId}`)
  }

  /**
   * Get all orders
   */
  async getOrders(filters?: {
    status?: string
    limit?: number
    page?: number
  }): Promise<{ data: AiraloOrder[], meta: any }> {
    const params = new URLSearchParams()
    
    if (filters?.status) params.append('filter[status]', filters.status)
    if (filters?.limit) params.append('limit', filters.limit.toString())
    if (filters?.page) params.append('page', filters.page.toString())

    const queryString = params.toString()
    const endpoint = `/orders${queryString ? `?${queryString}` : ''}`

    return this.apiRequest<{ data: AiraloOrder[], meta: any }>(endpoint)
  }

  /**
   * Get account balance
   */
  async getBalance(): Promise<{ data: { balance: number, currency: string } }> {
    return this.apiRequest<{ data: { balance: number, currency: string } }>('/balance')
  }

  /**
   * Test API connection
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.getBalance()
      return true
    } catch (error) {
      console.error('Airalo API connection test failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const airaloAPI = new AiraloAPI()
