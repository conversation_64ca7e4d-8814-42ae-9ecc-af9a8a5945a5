// Bundle analyzer configuration for Next.js
// This file is used when running `npm run analyze`

import { withPayload } from '@payloadcms/next';
import withBundleAnalyzer from '@next/bundle-analyzer';

const bundleAnalyzer = withBundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
});

// Combine the bundle analyzer with the Payload CMS configuration
export default bundleAnalyzer(withPayload({
  // Your Next.js configuration
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: ['localhost', 'ggsim.vercel.app', 'ggsim.me'],
    formats: ['image/avif', 'image/webp'],
  },
  experimental: {
    serverComponentsExternalPackages: ['sharp'],
  },
  webpack: (config) => {
    config.experiments = {
      ...config.experiments,
      topLevelAwait: true,
    };
    return config;
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ];
  },
}));