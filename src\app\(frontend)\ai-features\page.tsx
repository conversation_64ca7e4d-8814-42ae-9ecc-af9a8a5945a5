import { Metadata } from 'next';
import AIDetector from '@/components/features/AIDetector';
import MCPServerIntegration from '@/components/features/MCPServerIntegration';

export const metadata: Metadata = {
  title: 'AI Features - GGsim',
  description:
    'Explore the AI capabilities of GGsim, including AI content detection and MCP server integration.',
};

export default function AIFeaturesPage() {
  return (
    <div className="container mx-auto py-8 px-4" data-oid="xfgh:33">
      <h1 className="text-3xl font-bold mb-6" data-oid="huf6seo">
        AI Features
      </h1>

      <p className="mb-8 text-lg" data-oid="21tkqxk">
        GGsim integrates advanced AI capabilities to enhance your experience. Explore our AI content
        detection and MCP server integration features below.
      </p>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8" data-oid="8rgi1hx">
        <AIDetector className="h-full" data-oid="axtka0e" />
        <MCPServerIntegration className="h-full" data-oid="cbc8-ac" />
      </div>

      <div className="mt-12 p-6 bg-gray-50 rounded-lg" data-oid="d7sj3_4">
        <h2 className="text-2xl font-semibold mb-4" data-oid="5nm87oe">
          About Our AI Technology
        </h2>

        <div className="space-y-4" data-oid="dh9cw2e">
          <p data-oid=".nj1kds">
            GGsim leverages cutting-edge AI technologies to provide a seamless and intelligent user
            experience. Our AI features are designed to be transparent, ethical, and user-friendly.
          </p>

          <h3 className="text-xl font-medium" data-oid="fc6pmrb">
            AI Content Detection
          </h3>
          <p data-oid="is5vc1m">
            Our AI content detection system can identify content that was likely generated by AI
            models. This helps maintain content authenticity and can be useful for moderation
            purposes. The detection is based on sophisticated pattern recognition and linguistic
            analysis.
          </p>

          <h3 className="text-xl font-medium" data-oid="yvm_bqb">
            MCP Server Integration
          </h3>
          <p data-oid=":b76:vp">
            Model Context Protocol (MCP) servers enable powerful AI capabilities through
            standardized interfaces. GGsim supports integration with MCP servers, allowing you to
            leverage advanced AI tools and services. This integration enables features like
            intelligent search, content generation, and data analysis.
          </p>

          <h3 className="text-xl font-medium" data-oid="nh4-f5p">
            Privacy & Ethics
          </h3>
          <p data-oid="yjrlceb">
            We prioritize user privacy and ethical AI use. All AI features are designed with privacy
            in mind, and we're transparent about how AI is used in our platform. You're always in
            control of your data and how AI interacts with your content.
          </p>
        </div>
      </div>
    </div>
  );
}
