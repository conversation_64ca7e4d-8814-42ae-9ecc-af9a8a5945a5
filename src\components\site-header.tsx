import React from 'react';
import Link from 'next/link';
import Image from 'next/image'; // Import Image component
import { Button } from '@/components/ui/button';
import { CartButton } from '@/components/cart-button';
import { LogoutButton } from '@/components/logout-button';

export interface SiteHeaderProps {
  variant?: 'full' | 'simple';
  user?: any;
  title?: string;
  subtitle?: string | React.ReactNode;
  className?: string;
}

export function SiteHeader({
  variant = 'simple',
  user,
  title,
  subtitle,
  className = '',
}: SiteHeaderProps) {
  if (variant === 'full') {
    return (
      <header
        className={`sticky top-0 z-50 w-full border-b border-gray-200/60 bg-white/80 backdrop-blur-2xl ${className}`}
        data-oid="sy4l_0."
      >
        <div
          className="container mx-auto flex h-20 items-center justify-between px-4 sm:px-6 lg:px-8"
          data-oid="pq8fo2i"
        >
          <Link href="/" className="flex items-center gap-3 group" data-oid="e6q_boc">
            <div className="relative w-28 h-8" data-oid="fcwbosq">
              {' '}
              {/* Adjust width and height as needed for your logo */}
              <Image
                src="/images/LogoT.png" // Path to your uploaded logo
                alt="ggsim Logo"
                fill
                className="object-contain" // Use object-contain to ensure the whole logo is visible
                priority // Prioritize loading for LCP
                data-oid="npyh-ox"
              />
            </div>
          </Link>

          {/* Navigation and User Actions */}
          <div className="flex items-center gap-4" data-oid=":dygtz2">
            {user ? (
              <>
                <div className="hidden sm:flex items-center gap-4" data-oid="78_soa9">
                  <span className="text-sm text-gray-600" data-oid="ruvgjdc">
                    Welcome, {user.firstName || user.email}
                  </span>
                  <Button asChild variant="ghost" size="sm" data-oid="k20_bgl">
                    <Link href="/my-orders" data-oid="2gz3djt">
                      My Orders
                    </Link>
                  </Button>
                  {user.role === 'admin' && (
                    <Button asChild variant="ghost" size="sm" data-oid="txii83n">
                      <Link href="/admin-dashboard" data-oid="-y9m41a">
                        Admin
                      </Link>
                    </Button>
                  )}
                </div>
                <CartButton data-oid=".li-2sd" />
                <LogoutButton data-oid="swi1.x:" />
              </>
            ) : (
              <div className="flex items-center gap-2" data-oid="q-mtkz3">
                <Button asChild variant="ghost" size="sm" data-oid="kx9rf:b">
                  <Link href="/login" data-oid="qd:56ii">
                    Sign In
                  </Link>
                </Button>
                <Button asChild size="sm" data-oid="2twhvu:">
                  <Link href="/register" data-oid="cbj6f4u">
                    Sign Up
                  </Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </header>
    );
  }

  // Simple variant (for auth pages, etc.)
  return (
    <div className={`text-center ${className}`} data-oid="yl.4.cn">
      <Link href="/" className="text-2xl font-bold text-red-600" data-oid="_aigvhp">
        <div className="relative w-28 h-8 mx-auto" data-oid=".764sk1">
          {' '}
          {/* Adjust width and height as needed for your logo */}
          <Image
            src="/images/LogoT.png" // Path to your uploaded logo
            alt="ggsim Logo"
            fill
            className="object-contain" // Use object-contain to ensure the whole logo is visible
            data-oid="xqdbt.e"
          />
        </div>
      </Link>
      {title && (
        <h2 className="mt-6 text-3xl font-bold text-gray-900" data-oid="78d0x7y">
          {title}
        </h2>
      )}
      {subtitle && (
        <div className="mt-2 text-sm text-gray-600" data-oid="xgzchmr">
          {subtitle}
        </div>
      )}
    </div>
  );
}
