import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import {
  createPaymentIntent,
  createOrRetrieveCustomer,
  dollarsToCents,
  handleStripeError,
} from '@/lib/stripe'

export async function POST(request: NextRequest) {
  try {
    const payloadConfig = await config
    const payload = await getPayload({ config: payloadConfig })

    // Get user from the request
    const { user } = await payload.auth({ headers: request.headers })

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { amount, currency = 'usd', esimPlanId, metadata = {} } = body

    // Validate required fields
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: 'Invalid amount' },
        { status: 400 }
      )
    }

    if (!esimPlanId) {
      return NextResponse.json(
        { error: 'eSIM plan ID is required' },
        { status: 400 }
      )
    }

    // Fetch the eSIM plan to validate and get details
    const esimPlan = await payload.findByID({
      collection: 'esim-plans' as any,
      id: esimPlanId,
    })

    if (!esimPlan || !esimPlan.available) {
      return NextResponse.json(
        { error: 'eSIM plan not found or not available' },
        { status: 404 }
      )
    }

    // Verify the amount matches the plan price
    const expectedAmount = dollarsToCents(esimPlan.price)
    if (dollarsToCents(amount) !== expectedAmount) {
      return NextResponse.json(
        { error: 'Amount does not match plan price' },
        { status: 400 }
      )
    }

    // Create or retrieve Stripe customer
    const customer = await createOrRetrieveCustomer({
      email: user.email,
      name: `${user.firstName} ${user.lastName}`,
      userId: user.id.toString(),
    })

    // Update user with Stripe customer ID if not already set
    if (!(user as any).stripeCustomerId) {
      await payload.update({
        collection: 'users',
        id: user.id,
        data: {
          stripeCustomerId: customer.id,
        } as any,
      })
    }

    // Create payment intent
    const paymentIntent = await createPaymentIntent({
      amount: expectedAmount,
      currency,
      customerId: customer.id,
      metadata: {
        userId: user.id.toString(),
        esimPlanId: esimPlanId.toString(),
        planName: esimPlan.name,
        provider: esimPlan.provider,
        ...metadata,
      },
    })

    // Create a pending order in the database
    const order = await payload.create({
      collection: 'orders',
      data: {
        user: user.id,

        items: [
          {
            snack: esimPlanId,
            quantity: 1,
          },
        ],
        status: 'pending',

        totalAmount: esimPlan.price,
        orderDate: new Date().toISOString(),


      },
    })

    return NextResponse.json({
      success: true,
      paymentIntent: {
        id: paymentIntent.id,
        clientSecret: paymentIntent.client_secret,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        status: paymentIntent.status,
      },
      order: {
        id: order.id,
        status: order.status,
        paymentStatus: (order as any).paymentStatus,
      },
    })
  } catch (error) {
    console.error('Error creating payment intent:', error)

    // Handle Stripe-specific errors
    if ((error as any).type && (error as any).type.startsWith('Stripe')) {
      const stripeError = handleStripeError(error)
      return NextResponse.json(
        { error: stripeError.message, code: stripeError.code },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET endpoint to retrieve payment intent status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const paymentIntentId = searchParams.get('payment_intent_id')

    if (!paymentIntentId) {
      return NextResponse.json(
        { error: 'Payment intent ID is required' },
        { status: 400 }
      )
    }

    const payloadConfig = await config
    const payload = await getPayload({ config: payloadConfig })

    // Get user from the request
    const { user } = await payload.auth({ headers: request.headers })

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Find the order associated with this payment intent
    const orders = await payload.find({
      collection: 'orders',
      where: {
        and: [
          {
            user: {
              equals: user.id,
            },
          },
          {
            stripePaymentIntentId: {
              equals: paymentIntentId,
            },
          },
        ],
      },
      limit: 1,
    })

    if (orders.docs.length === 0) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    const order = orders.docs[0]

    return NextResponse.json({
      success: true,
      order: {
        id: order.id,
        status: order.status,
        paymentStatus: (order as any).paymentStatus,
        totalAmount: order.totalAmount,
        esimData: (order as any).esimData,
      },
    })
  } catch (error) {
    console.error('Error retrieving payment intent:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
