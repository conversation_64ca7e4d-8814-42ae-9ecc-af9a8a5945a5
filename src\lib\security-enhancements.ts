/**
 * Security enhancements for Next.js application
 * 
 * This module provides utilities to enhance the security of the application,
 * including CSP configuration, input sanitization, and protection against common vulnerabilities.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { NextRequest, NextResponse } from 'next/server';

/**
 * Content Security Policy configuration
 */
export const cspConfig = {
  directives: {
    defaultSrc: ["'self'"],
    scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", 'https://vercel.live'],
    styleSrc: ["'self'", "'unsafe-inline'"],
    imgSrc: ["'self'", 'data:', 'https:', 'blob:'],
    fontSrc: ["'self'", 'data:', 'https:'],
    connectSrc: ["'self'", 'https:'],
    frameSrc: ["'self'"],
    objectSrc: ["'none'"],
    baseUri: ["'self'"],
    formAction: ["'self'"],
    frameAncestors: ["'self'"],
  },
};

/**
 * Sanitizes user input to prevent XSS attacks
 * @param input User input string
 * @returns Sanitized string
 */
export function sanitizeInput(input: string): string {
  if (!input) return '';
  
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

/**
 * Validates and sanitizes an object's string properties
 * @param obj Object to sanitize
 * @returns Sanitized object
 */
export function sanitizeObject<T extends Record<string, any>>(obj: T): T {
  const result = { ...obj } as any;

  Object.keys(result).forEach(key => {
    if (typeof result[key] === 'string') {
      result[key] = sanitizeInput(result[key]);
    } else if (typeof result[key] === 'object' && result[key] !== null) {
      result[key] = sanitizeObject(result[key]);
    }
  });

  return result;
}

/**
 * Middleware to apply security headers to API responses
 * @param handler API route handler
 * @returns Enhanced API route handler with security headers
 */
export function withSecurityHeaders(
  handler: (req: NextApiRequest, res: NextApiResponse) => Promise<void>
) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    // Set security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
    
    // Apply CSP in non-development environments
    if (process.env.NODE_ENV !== 'development') {
      const csp = Object.entries(cspConfig.directives)
        .map(([key, values]) => `${key} ${values.join(' ')}`)
        .join('; ');
      
      res.setHeader('Content-Security-Policy', csp);
    }
    
    // Call the original handler
    return handler(req, res);
  };
}

/**
 * Middleware to apply security headers to App Router responses
 * @param request Next.js request
 * @returns Next.js response with security headers
 */
export function applySecurityHeaders(_unusedRequest: NextRequest): NextResponse {
  const response = NextResponse.next();
  
  // Set security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  
  // Apply CSP in non-development environments
  if (process.env.NODE_ENV !== 'development') {
    const csp = Object.entries(cspConfig.directives)
      .map(([key, values]) => `${key} ${values.join(' ')}`)
      .join('; ');
    
    response.headers.set('Content-Security-Policy', csp);
  }
  
  return response;
}

/**
 * Validates a request's CSRF token
 * @param req Next.js API request
 * @param token CSRF token from the request
 * @returns Boolean indicating if the token is valid
 */
export function validateCsrfToken(req: NextApiRequest, token: string): boolean {
  // In a real implementation, this would validate against a stored token
  // This is a placeholder for demonstration purposes
  const storedToken = req.cookies['csrf-token'];
  return storedToken === token;
}
