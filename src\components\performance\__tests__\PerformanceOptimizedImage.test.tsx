import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { PerformanceOptimizedImage } from '../PerformanceOptimizedImage';
import * as monitoring from '@/lib/monitoring';

// Mock the monitoring module
jest.mock('@/lib/monitoring', () => ({
  trackEvent: jest.fn(),
}));

describe('PerformanceOptimizedImage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with correct props', () => {
    render(
      <PerformanceOptimizedImage
        src="/test-image.jpg"
        alt="Test image"
        width={300}
        height={200}
      />
    );

    const image = screen.getByAltText('Test image');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src');
    expect(image).toHaveAttribute('width', '300');
    expect(image).toHaveAttribute('height', '200');
  });

  it('applies custom className', () => {
    render(
      <PerformanceOptimizedImage
        src="/test-image.jpg"
        alt="Test image"
        width={300}
        height={200}
        className="custom-class"
      />
    );

    const image = screen.getByAltText('Test image');
    expect(image).toHaveClass('custom-class');
  });

  it('tracks image load event', () => {
    render(
      <PerformanceOptimizedImage
        src="/test-image.jpg"
        alt="Test image"
        width={300}
        height={200}
      />
    );

    const image = screen.getByAltText('Test image');
    fireEvent.load(image);

    expect(monitoring.trackEvent).toHaveBeenCalledWith('image_loaded', {
      src: '/test-image.jpg',
      alt: 'Test image',
    });
  });

  it('tracks image error event', () => {
    render(
      <PerformanceOptimizedImage
        src="/test-image.jpg"
        alt="Test image"
        width={300}
        height={200}
      />
    );

    const image = screen.getByAltText('Test image');
    fireEvent.error(image);

    expect(monitoring.trackEvent).toHaveBeenCalledWith('image_error', {
      src: '/test-image.jpg',
      alt: 'Test image',
    });
  });

  it('tracks image click event', () => {
    render(
      <PerformanceOptimizedImage
        src="/test-image.jpg"
        alt="Test image"
        width={300}
        height={200}
      />
    );

    const image = screen.getByAltText('Test image');
    fireEvent.click(image);

    expect(monitoring.trackEvent).toHaveBeenCalledWith('image_click', {
      src: '/test-image.jpg',
      alt: 'Test image',
    });
  });

  it('applies priority prop when specified', () => {
    render(
      <PerformanceOptimizedImage
        src="/test-image.jpg"
        alt="Test image"
        width={300}
        height={200}
        priority
      />
    );

    const image = screen.getByAltText('Test image');
    expect(image).toHaveAttribute('fetchpriority', 'high');
  });
});