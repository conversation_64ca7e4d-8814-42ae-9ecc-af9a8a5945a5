'use client';

import React from 'react';
import Image from 'next/image';
import { optimizeImage } from '@/lib/performance-optimization';
import { trackEvent } from '@/lib/monitoring';

interface PerformanceOptimizedImageProps {
  src: string;
  alt: string;
  width: number;
  height: number;
  priority?: boolean;
  className?: string;
  onClick?: () => void;
}

/**
 * A performance-optimized image component that implements best practices
 * for image loading, tracking, and user experience.
 */
export function PerformanceOptimizedImage({
  src,
  alt,
  width,
  height,
  priority = false,
  className = '',
  onClick,
}: PerformanceOptimizedImageProps) {
  // Track image load performance
  const handleLoad = React.useCallback(() => {
    // Track successful image load
    trackEvent('image_loaded', {
      src,
      width,
      height,
      loadTime: performance.now(),
    });
  }, [src, width, height]);

  // Track image load errors
  const handleError = React.useCallback(() => {
    // Track failed image load
    trackEvent('image_error', {
      src,
      width,
      height,
      errorTime: performance.now(),
    });
  }, [src, width, height]);

  // Handle click with tracking
  const handleClick = React.useCallback(() => {
    // Track image click
    trackEvent('image_click', {
      src,
      alt,
    });

    // Call the provided onClick handler if it exists
    if (onClick) {
      onClick();
    }
  }, [src, alt, onClick]);

  // Get optimized image props
  const imageProps = optimizeImage(src, width, height);

  return (
    <div className={`relative overflow-hidden ${className}`}>
      <Image
        {...imageProps}
        src={src}
        alt={alt}
        width={width}
        height={height}
        priority={priority}
        onLoad={handleLoad}
        onError={handleError}
        onClick={handleClick}
        className="transition-opacity duration-300 ease-in-out"
        style={{ objectFit: 'cover' }}
      />
    </div>
  );
}