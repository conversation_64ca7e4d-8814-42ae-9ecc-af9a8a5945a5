import { headers as getHeaders } from 'next/headers.js';
import Image from 'next/image';
import { getPayload } from 'payload';
import React from 'react';
import Link from 'next/link';
import { redirect } from 'next/navigation';

import config from '@/payload.config';
import OrderStatusUpdate from './order-status-update'; // Changed to default import
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { User, Order } from '@/payload-types'; // Import User and Order types

export default async function AdminDashboardPage() {
  const headers = await getHeaders();
  const payloadConfig = await config;
  const payload = await getPayload({ config: payloadConfig });
  const { user } = (await payload.auth({ headers })) as { user: User | null }; // Cast user to User | null

  // Redirect to login if not authenticated or not admin
  if (!user || user.role !== 'admin') {
    // Use typed user.role
    redirect('/');
  }

  // Fetch all orders
  const orders = await payload.find({
    collection: 'orders',
    depth: 3,
    sort: '-orderDate',
    limit: 50,
  });

  // Get order statistics
  const pendingOrders = orders.docs.filter((order: Order) => order.status === 'pending'); // Use Order type
  const completedOrders = orders.docs.filter((order: Order) => order.status === 'completed'); // Use Order type
  const cancelledOrders = orders.docs.filter((order: Order) => order.status === 'cancelled'); // Use Order type

  return (
    <div className="min-h-screen bg-gray-50" data-oid="mf_qstd">
      <div className="container mx-auto px-4 py-8" data-oid="nv4_c0g">
        <div className="mb-8" data-oid="4da2bic">
          <Button asChild variant="ghost" className="mb-4" data-oid="c6.pg8_">
            <Link href="/" data-oid="p.zc5kv">
              ← Back to Home
            </Link>
          </Button>
          <h1 className="text-3xl font-bold text-gray-900" data-oid=":izg.dx">
            Admin Dashboard
          </h1>
        </div>

        {/* Statistics Grid */}
        <div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          data-oid="hyik.:4"
        >
          <Card data-oid="ip_i5mr">
            <CardHeader className="pb-3" data-oid="gf316cp">
              <CardTitle className="text-sm font-medium text-gray-600" data-oid="hl7uh.j">
                Pending Orders
              </CardTitle>
            </CardHeader>
            <CardContent data-oid="284phxd">
              <div className="text-2xl font-bold text-yellow-600" data-oid="zf4-mbc">
                {pendingOrders.length}
              </div>
            </CardContent>
          </Card>
          <Card data-oid="028c.5a">
            <CardHeader className="pb-3" data-oid="8x4m_-7">
              <CardTitle className="text-sm font-medium text-gray-600" data-oid="9i7zqwg">
                Completed Orders
              </CardTitle>
            </CardHeader>
            <CardContent data-oid="8p_5pv5">
              <div className="text-2xl font-bold text-green-600" data-oid="ukqv9ov">
                {completedOrders.length}
              </div>
            </CardContent>
          </Card>
          <Card data-oid="ntx8m:l">
            <CardHeader className="pb-3" data-oid="x5q808y">
              <CardTitle className="text-sm font-medium text-gray-600" data-oid="uyr_s1:">
                Cancelled Orders
              </CardTitle>
            </CardHeader>
            <CardContent data-oid="633-pi1">
              <div className="text-2xl font-bold text-red-600" data-oid="m.9agr3">
                {cancelledOrders.length}
              </div>
            </CardContent>
          </Card>
          <Card data-oid="bn521ej">
            <CardHeader className="pb-3" data-oid="2hbfso.">
              <CardTitle className="text-sm font-medium text-gray-600" data-oid="fnr7u1h">
                Total Orders
              </CardTitle>
            </CardHeader>
            <CardContent data-oid="8k1dwzs">
              <div className="text-2xl font-bold text-blue-600" data-oid="r84h_8y">
                {orders.docs.length}
              </div>
            </CardContent>
          </Card>
        </div>

        <Separator className="my-8" data-oid="4wc.3g5" />

        {/* Orders Section */}
        <div data-oid="xr1osv4">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6" data-oid="av2:bnn">
            All Orders
          </h2>

          {orders.docs.length === 0 ? (
            <Card data-oid="xqxidmq">
              <CardContent className="py-8 text-center" data-oid="uk-s8-s">
                <p className="text-gray-500" data-oid="78735q3">
                  No orders found.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6" data-oid="uc.kka6">
              {orders.docs.map((order: Order) => (
                <Card key={order.id} className="overflow-hidden" data-oid="pnnj76v">
                  <CardHeader data-oid="7gs14rl">
                    <div className="flex items-start justify-between" data-oid="s1tgy9_">
                      <div data-oid="-z_:bt9">
                        <CardTitle className="text-lg" data-oid="w21go_d">
                          Order #{String(order.id).slice(-8)}
                        </CardTitle>
                        <CardDescription className="mt-1" data-oid="ulg0rwh">
                          Customer:{' '}
                          {typeof order.user === 'object'
                            ? `${order.user.firstName} ${order.user.lastName}`
                            : 'Unknown User'}{' '}
                          ({typeof order.user === 'object' ? order.user.email : 'Unknown Email'})
                        </CardDescription>
                        <p className="text-sm text-gray-500 mt-1" data-oid="qin-3gt">
                          Ordered:{' '}
                          {new Date(order.orderDate).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                          })}
                        </p>
                      </div>
                      <div className="flex flex-col items-end gap-3" data-oid="14fo:74">
                        <Badge
                          variant={
                            order.status === 'pending'
                              ? 'secondary'
                              : order.status === 'completed'
                                ? 'default'
                                : 'destructive'
                          }
                          data-oid="ytow_tk"
                        >
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </Badge>
                        <OrderStatusUpdate
                          orderId={order.id}
                          currentStatus={order.status}
                          data-oid="e294004"
                        />
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent data-oid="9-d8:fj">
                    <div className="space-y-3" data-oid="5vxx4pc">
                      {order.items.map((item, index: number) => (
                        <div
                          key={index}
                          className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"
                          data-oid="m4lr_f0"
                        >
                          {item.snack &&
                          typeof item.snack === 'object' &&
                          item.snack.image &&
                          typeof item.snack.image === 'object' &&
                          item.snack.image.url ? ( // Refined image access
                            <div
                              className="relative w-12 h-12 rounded overflow-hidden"
                              data-oid="esr_ut2"
                            >
                              <Image
                                src={item.snack.image.url}
                                alt={item.snack.image.alt || item.snack.name}
                                fill
                                className="object-cover"
                                data-oid="03nlro6"
                              />
                            </div>
                          ) : null}
                          <div className="flex-1" data-oid="6cgljtl">
                            <h4 className="font-medium" data-oid=".wq4.17">
                              {typeof item.snack === 'object' ? item.snack.name : 'Unknown Item'}
                            </h4>
                            <p className="text-sm text-gray-600" data-oid="d16c7kw">
                              Qty: {item.quantity} × $
                              {typeof item.snack === 'object'
                                ? item.snack.price?.toFixed(2)
                                : '0.00'}
                            </p>
                          </div>
                          <div className="text-right font-medium" data-oid="-2hy1m6">
                            $
                            {(
                              (typeof item.snack === 'object' ? item.snack.price : 0) *
                              item.quantity
                            ).toFixed(2)}
                          </div>
                        </div>
                      ))}
                    </div>

                    <Separator className="my-4" data-oid="100o28b" />

                    <div className="text-right" data-oid="x9_p:x7">
                      <span className="text-lg font-bold" data-oid="9ay4tqx">
                        Total: ${order.totalAmount.toFixed(2)}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
