import { Banner } from '@payloadcms/ui/elements/Banner';
import React from 'react';

import { SeedButton } from './seed-button';
import './index.scss';

const baseClass = 'before-dashboard';

const BeforeDashboard: React.FC = () => {
  return (
    <div className={baseClass} data-oid="km.7.ef">
      <Banner className={`${baseClass}__banner`} type="success" data-oid="0t97gmb">
        <h4 data-oid="jdty_k4">Welcome to your dashboard!</h4>
      </Banner>
      Add some default snacks
      <br data-oid="8t5oeby" />
      <SeedButton data-oid="cfgj5l-" />
    </div>
  );
};

export default BeforeDashboard;
