'use client'

import React, { useState } from 'react'
import { Plus, Check } from 'lucide-react'

import { useCart } from '@/lib/cart-context'
import { Button } from '@/components/ui/button'
import { Media } from '@/payload-types' // Import Media types

interface AddToCartButtonProps {
  snack: {
    id: number
    name: string
    price: number
    category: string
    image?: Media | number | null // Allow null for image
  }
}

export const AddToCartButton: React.FC<AddToCartButtonProps> = ({ snack }) => {
  const { addItem } = useCart()
  const [isAdded, setIsAdded] = useState(false)

  const handleAddToCart = () => {
    addItem({
      id: String(snack.id),
      name: snack.name,
      price: snack.price,
      category: snack.category,
      image: typeof snack.image === 'object' && snack.image !== null ? snack.image : undefined, // Ensure image is an object and not null
    })

    setIsAdded(true)

    // Show feedback for 1 second
    setTimeout(() => {
      setIsAdded(false)
    }, 1000)

    // Optional: Open cart after adding item
    // openCart()
  }

  return (
    <Button
      onClick={handleAddToCart}
      disabled={isAdded}
      className={isAdded ? 'bg-green-600 hover:bg-green-600' : ''}
    >
      {isAdded ? (
        <>
          <Check className="h-4 w-4 mr-2" />
          Added!
        </>
      ) : (
        <>
          <Plus className="h-4 w-4 mr-2" />
          Add to Cart
        </>
      )}
    </Button>
  )
}
