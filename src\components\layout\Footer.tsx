/**
 * SEO-Optimized Footer Component
 *
 * Features:
 * - Comprehensive site links for SEO
 * - Social media integration
 * - Newsletter signup
 * - Legal compliance links
 * - Mobile-responsive design
 */
'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { EnvelopeIcon, PhoneIcon, MapPinIcon } from '@heroicons/react/24/outline';

const footerLinks = {
  product: [
    { name: 'Search eSIM Plans', href: '/search' },
    { name: 'How It Works', href: '/how-it-works' },
    { name: 'Coverage Map', href: '/coverage' },
    { name: 'Pricing', href: '/pricing' },
    { name: 'Compare Plans', href: '/compare' },
  ],

  support: [
    { name: 'Help Center', href: '/help' },
    { name: 'Contact Us', href: '/contact' },
    { name: 'Device Compatibility', href: '/compatibility' },
    { name: 'Installation Guide', href: '/installation' },
    { name: 'Troubleshooting', href: '/troubleshooting' },
  ],

  company: [
    { name: 'About Us', href: '/about' },
    { name: 'Blog', href: '/blog' },
    { name: 'Careers', href: '/careers' },
    { name: 'Press', href: '/press' },
    { name: 'Partners', href: '/partners' },
  ],

  legal: [
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
    { name: 'Cookie Policy', href: '/cookies' },
    { name: 'Refund Policy', href: '/refunds' },
    { name: 'GDPR Compliance', href: '/gdpr' },
  ],
};

const socialLinks = [
  { name: 'Twitter', href: 'https://twitter.com/ggsim', icon: '𝕏' },
  { name: 'Facebook', href: 'https://facebook.com/ggsim', icon: '📘' },
  { name: 'Instagram', href: 'https://instagram.com/ggsim', icon: '📷' },
  { name: 'LinkedIn', href: 'https://linkedin.com/company/ggsim', icon: '💼' },
];

export function Footer() {
  return (
    <footer className="bg-gray-900 text-white" data-oid="iol8klp">
      {/* Newsletter Section */}
      <div className="border-b border-gray-800" data-oid="8gx6u-k">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12" data-oid="9iy59x0">
          <div className="max-w-2xl mx-auto text-center" data-oid="-cew5i1">
            <h3 className="text-2xl font-bold mb-4" data-oid="rm-zy1k">
              Stay Connected
            </h3>
            <p className="text-gray-300 mb-6" data-oid="3v5n7-c">
              Get the latest eSIM deals, travel tips, and connectivity updates delivered to your
              inbox.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto" data-oid="nyh2-vc">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                data-oid="xkywsdi"
              />

              <button
                className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-lg transition-colors duration-200"
                data-oid="r100kl1"
              >
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12" data-oid="gq4s6eo">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8" data-oid="05csr:c">
          {/* Company Info */}
          <div className="lg:col-span-1" data-oid="mp58wlt">
            <div className="flex items-center space-x-3 mb-4" data-oid="zd_7l47">
              <div className="relative w-10 h-10" data-oid="cq5kvw4">
                <Image
                  src="/images/LogoT.png"
                  alt="GGsim Logo"
                  fill
                  className="object-contain"
                  data-oid="e_djf9_"
                />
              </div>
              <span className="text-xl font-bold" data-oid="o_91z06">
                GGsim
              </span>
            </div>
            <p className="text-gray-300 mb-6 text-sm leading-relaxed" data-oid="8u4i5uy">
              Your trusted global eSIM marketplace. Connect instantly in 200+ countries with our
              premium eSIM plans from top providers worldwide.
            </p>

            {/* Contact Info */}
            <div className="space-y-2 text-sm text-gray-400" data-oid="-dosyo2">
              <div className="flex items-center space-x-2" data-oid="nzlzext">
                <EnvelopeIcon className="w-4 h-4" data-oid="_-zm0d4" />
                <span data-oid=".y_-uoh"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2" data-oid="6ccrn.v">
                <PhoneIcon className="w-4 h-4" data-oid="aaj71se" />
                <span data-oid="mucl:j3">+****************</span>
              </div>
              <div className="flex items-center space-x-2" data-oid="1d7nm.r">
                <MapPinIcon className="w-4 h-4" data-oid="v.v_xa:" />
                <span data-oid="9m9_u-1">San Francisco, CA</span>
              </div>
            </div>
          </div>

          {/* Product Links */}
          <div data-oid="shtz_hb">
            <h4 className="text-lg font-semibold mb-4" data-oid="763vpz_">
              Product
            </h4>
            <ul className="space-y-2" data-oid="5hhf-u3">
              {footerLinks.product.map(link => (
                <li key={link.name} data-oid="zv1mt8.">
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors duration-200 text-sm"
                    data-oid="xs0rq7r"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support Links */}
          <div data-oid="ad9.rp9">
            <h4 className="text-lg font-semibold mb-4" data-oid="qboqx-p">
              Support
            </h4>
            <ul className="space-y-2" data-oid="o9xnaap">
              {footerLinks.support.map(link => (
                <li key={link.name} data-oid="a8e29qg">
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors duration-200 text-sm"
                    data-oid="6j:v:qr"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company Links */}
          <div data-oid="np1.zu_">
            <h4 className="text-lg font-semibold mb-4" data-oid="qbylbzc">
              Company
            </h4>
            <ul className="space-y-2" data-oid="q8mbru3">
              {footerLinks.company.map(link => (
                <li key={link.name} data-oid="q83wy5t">
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors duration-200 text-sm"
                    data-oid="avyofyi"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal Links */}
          <div data-oid="w1zr.:x">
            <h4 className="text-lg font-semibold mb-4" data-oid="n4ttn6_">
              Legal
            </h4>
            <ul className="space-y-2" data-oid="n9rc-gy">
              {footerLinks.legal.map(link => (
                <li key={link.name} data-oid="eqqurrk">
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors duration-200 text-sm"
                    data-oid="xe.kl8d"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800" data-oid="56dxqm5">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6" data-oid="700.sef">
          <div
            className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0"
            data-oid="_8yvc2:"
          >
            <div className="text-sm text-gray-400" data-oid="byn5ukv">
              © 2024 GGsim. All rights reserved. | Global eSIM marketplace for seamless
              connectivity.
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-4" data-oid="rfsa_dj">
              {socialLinks.map(social => (
                <a
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white transition-colors duration-200"
                  aria-label={`Follow us on ${social.name}`}
                  data-oid="-_ry79i"
                >
                  <span className="text-lg" data-oid="w0li956">
                    {social.icon}
                  </span>
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
