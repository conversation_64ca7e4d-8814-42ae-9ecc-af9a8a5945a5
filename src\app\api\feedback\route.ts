import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { headers } from 'next/headers'

interface FeedbackData {
  type: 'bug' | 'feature' | 'general' | 'love'
  rating: number
  message: string
  page: string
  userAgent: string
  timestamp: string
  userId?: string
  userEmail?: string
}

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

function checkRateLimit(identifier: string, limit: number = 5, windowMs: number = 60000): boolean {
  const now = Date.now()
  const userLimit = rateLimitStore.get(identifier)

  if (!userLimit || now > userLimit.resetTime) {
    rateLimitStore.set(identifier, { count: 1, resetTime: now + windowMs })
    return true
  }

  if (userLimit.count >= limit) {
    return false
  }

  userLimit.count++
  return true
}

export async function POST(request: NextRequest) {
  try {
    // Get user authentication
    const { userId } = await auth()
    
    // Get client IP for rate limiting
    const headersList = await headers()
    const forwardedFor = headersList.get('x-forwarded-for')
    const clientIP = forwardedFor ? forwardedFor.split(',')[0] : 'unknown'
    
    // Rate limiting
    const rateLimitKey = userId || clientIP
    if (!checkRateLimit(rateLimitKey)) {
      return NextResponse.json(
        { error: 'Too many feedback submissions. Please try again later.' },
        { status: 429 }
      )
    }

    // Parse request body
    const feedbackData: FeedbackData = await request.json()

    // Validate required fields
    if (!feedbackData.message || !feedbackData.type) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate feedback type
    const validTypes = ['bug', 'feature', 'general', 'love']
    if (!validTypes.includes(feedbackData.type)) {
      return NextResponse.json(
        { error: 'Invalid feedback type' },
        { status: 400 }
      )
    }

    // Validate rating (if provided)
    if (feedbackData.rating && (feedbackData.rating < 1 || feedbackData.rating > 5)) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5' },
        { status: 400 }
      )
    }

    // Sanitize message
    const sanitizedMessage = feedbackData.message.trim().substring(0, 1000)

    // Create feedback record
    const feedback = {
      id: crypto.randomUUID(),
      type: feedbackData.type,
      rating: feedbackData.rating || null,
      message: sanitizedMessage,
      page: feedbackData.page || 'unknown',
      userAgent: feedbackData.userAgent || 'unknown',
      timestamp: new Date().toISOString(),
      userId: feedbackData.userId || null,
      userEmail: feedbackData.userEmail || null,
      clientIP,
      processed: false,
    }

    // In production, save to database
    // For now, log to console and optionally send to external service
    console.log('Feedback received:', feedback)

    // Send to external service (e.g., Slack, Discord, email)
    await sendFeedbackNotification(feedback)

    // Store in database (implement based on your database choice)
    // await saveFeedbackToDatabase(feedback)

    return NextResponse.json(
      { 
        success: true, 
        message: 'Feedback submitted successfully',
        id: feedback.id 
      },
      { status: 201 }
    )

  } catch (error) {
    console.error('Error processing feedback:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function sendFeedbackNotification(feedback: any) {
  try {
    // Send to Slack webhook if configured
    if (process.env.SLACK_FEEDBACK_WEBHOOK_URL) {
      const slackMessage = {
        text: `New ${feedback.type} feedback received`,
        blocks: [
          {
            type: 'header',
            text: {
              type: 'plain_text',
              text: `🔔 New ${feedback.type.charAt(0).toUpperCase() + feedback.type.slice(1)} Feedback`
            }
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Type:* ${feedback.type}`
              },
              {
                type: 'mrkdwn',
                text: `*Rating:* ${feedback.rating ? '⭐'.repeat(feedback.rating) : 'N/A'}`
              },
              {
                type: 'mrkdwn',
                text: `*Page:* ${feedback.page}`
              },
              {
                type: 'mrkdwn',
                text: `*User:* ${feedback.userEmail || 'Anonymous'}`
              }
            ]
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*Message:*\n${feedback.message}`
            }
          },
          {
            type: 'context',
            elements: [
              {
                type: 'mrkdwn',
                text: `Submitted at ${new Date(feedback.timestamp).toLocaleString()}`
              }
            ]
          }
        ]
      }

      await fetch(process.env.SLACK_FEEDBACK_WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(slackMessage),
      })
    }

    // Send email notification if configured
    if (process.env.FEEDBACK_EMAIL_WEBHOOK_URL) {
      const emailData = {
        to: '<EMAIL>',
        subject: `New ${feedback.type} feedback from ${feedback.userEmail || 'Anonymous'}`,
        html: `
          <h2>New ${feedback.type} feedback received</h2>
          <p><strong>Type:</strong> ${feedback.type}</p>
          <p><strong>Rating:</strong> ${feedback.rating || 'N/A'}</p>
          <p><strong>Page:</strong> ${feedback.page}</p>
          <p><strong>User:</strong> ${feedback.userEmail || 'Anonymous'}</p>
          <p><strong>Message:</strong></p>
          <blockquote>${feedback.message}</blockquote>
          <p><strong>Submitted:</strong> ${new Date(feedback.timestamp).toLocaleString()}</p>
          <p><strong>User Agent:</strong> ${feedback.userAgent}</p>
        `,
      }

      await fetch(process.env.FEEDBACK_EMAIL_WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailData),
      })
    }
  } catch (error) {
    console.error('Error sending feedback notification:', error)
    // Don't throw error to avoid failing the main request
  }
}

// GET endpoint to retrieve feedback (admin only)
export async function GET() {
  try {
    const { sessionClaims } = await auth()
    
    // Check if user is admin
    const userRole = (sessionClaims as any)?.metadata?.role as string
    if (userRole !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      )
    }

    // In production, fetch from database
    // For now, return empty array
    const feedback: any[] = []

    return NextResponse.json({ feedback })
  } catch (error) {
    console.error('Error fetching feedback:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
