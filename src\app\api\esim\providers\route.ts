import { NextResponse } from 'next/server'
import { getProviderManager } from '@/lib/providers/provider-manager'

export async function GET() {
  try {
    const providerManager = getProviderManager()
    
    // Get provider health status
    const providersHealth = await providerManager.getProvidersHealth()
    
    // Get available providers
    const availableProviders = providerManager.getAvailableProviders()
    
    // Get detailed info for each provider
    const providersInfo = availableProviders.map(providerName => {
      const info = providerManager.getProviderInfo(providerName)
      const health = providersHealth.find(h => h.name === providerName)
      
      return {
        name: providerName,
        ...info,
        health: health || {
          name: providerName,
          healthy: false,
          environment: 'unknown',
          lastChecked: new Date(),
        },
      }
    })

    // Calculate overall system health
    const healthyCount = providersHealth.filter(p => p.healthy).length
    const totalCount = providersHealth.length
    const systemHealth = {
      status: healthyCount > 0 ? (healthyCount === totalCount ? 'healthy' : 'degraded') : 'unhealthy',
      healthyProviders: healthyCount,
      totalProviders: totalCount,
      healthPercentage: totalCount > 0 ? Math.round((healthyCount / totalCount) * 100) : 0,
    }

    return NextResponse.json({
      success: true,
      systemHealth,
      providers: providersInfo,
      summary: {
        total: totalCount,
        healthy: healthyCount,
        unhealthy: totalCount - healthyCount,
        lastChecked: new Date().toISOString(),
      },
    })

  } catch (error) {
    console.error('Provider status error:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to get provider status',
        code: 'PROVIDER_STATUS_ERROR',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      },
      { status: 500 }
    )
  }
}

// Force refresh provider health status
export async function POST() {
  try {
    const providerManager = getProviderManager()
    
    // Force a health check on all providers
    const providersHealth = await providerManager.getProvidersHealth()
    
    return NextResponse.json({
      success: true,
      message: 'Provider health status refreshed',
      providers: providersHealth,
      timestamp: new Date().toISOString(),
    })

  } catch (error) {
    console.error('Provider health refresh error:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to refresh provider health status',
        code: 'HEALTH_REFRESH_ERROR',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      },
      { status: 500 }
    )
  }
}
