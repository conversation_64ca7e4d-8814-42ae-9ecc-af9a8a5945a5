'use client';

import React, { useState, useEffect, Suspense } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { SiteHeader } from '@/components/site-header';

function ResetPasswordForm() {
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  // const router = useRouter()
  const searchParams = useSearchParams();
  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      setError('Invalid or missing reset token. Please request a new password reset.');
    }
  }, [token]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    if (!formData.password || !formData.confirmPassword) {
      setError('Both password fields are required');
      setIsSubmitting(false);
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      setIsSubmitting(false);
      return;
    }

    if (formData.password.length < 6) {
      setError('Password must be at least 6 characters long');
      setIsSubmitting(false);
      return;
    }

    if (!token) {
      setError('Invalid or missing reset token');
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await fetch('/api/users/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          password: formData.password,
        }),
      });

      if (response.ok) {
        setSuccess(true);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Failed to reset password. Please try again.');
      }
    } catch (err) {
      setError('Failed to reset password. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (success) {
    return (
      <div
        className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4"
        data-oid="czke:hd"
      >
        <div className="max-w-md w-full space-y-8" data-oid="y-71mr:">
          <SiteHeader title="Password reset successful" data-oid="2d34qgl" />

          {/* Success Message */}
          <Card data-oid="tx98lso">
            <CardHeader data-oid="zuy_._4">
              <CardTitle data-oid="rnks8k2">All set!</CardTitle>
              <CardDescription data-oid="-40xca6">
                Your password has been successfully reset.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4" data-oid="6:91mdk">
              <Alert data-oid="k4ia5i:">
                <AlertDescription data-oid="cql.2vt">
                  You can now sign in with your new password.
                </AlertDescription>
              </Alert>

              <div className="space-y-2" data-oid="tispcjd">
                <Link href="/login" data-oid="9ppw.rc">
                  <Button className="w-full" data-oid="p-jvu-e">
                    Sign in
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          {/* Back to Home */}
          <div className="text-center" data-oid="_0o98__">
            <Link href="/" className="text-sm text-gray-600 hover:text-gray-900" data-oid=":2jqqgk">
              ← Back to home
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4"
      data-oid="scxce:u"
    >
      <div className="max-w-md w-full space-y-8" data-oid="-lf8jug">
        <SiteHeader
          title="Reset your password"
          subtitle="Enter your new password below"
          data-oid="4wybcb3"
        />

        {/* Reset Password Form */}
        <Card data-oid="_woogcw">
          <CardHeader data-oid="gz_g24.">
            <CardTitle data-oid="rv6yu8b">New password</CardTitle>
            <CardDescription data-oid="vhei8rq">
              Choose a strong password for your account
            </CardDescription>
          </CardHeader>
          <CardContent data-oid="7gqu176">
            <form onSubmit={handleSubmit} className="space-y-4" data-oid="emx2xfg">
              {error && (
                <Alert variant="destructive" data-oid="gq8h-hr">
                  <AlertDescription data-oid="uo2_6uw">{error}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2" data-oid="bxarj_q">
                <label
                  htmlFor="password"
                  className="text-sm font-medium text-gray-700"
                  data-oid=":acx718"
                >
                  New Password
                </label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  required
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="••••••••"
                  data-oid="wxg82kd"
                />
              </div>

              <div className="space-y-2" data-oid="57qmk0z">
                <label
                  htmlFor="confirmPassword"
                  className="text-sm font-medium text-gray-700"
                  data-oid="7h7f3vo"
                >
                  Confirm New Password
                </label>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  required
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  placeholder="••••••••"
                  data-oid="9pknvnr"
                />
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={isSubmitting || !token}
                data-oid="xbk.vn1"
              >
                {isSubmitting ? 'Resetting...' : 'Reset password'}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Back to Home */}
        <div className="text-center" data-oid=":0fcf31">
          <Link href="/" className="text-sm text-gray-600 hover:text-gray-900" data-oid="r9u0mu1">
            ← Back to home
          </Link>
        </div>
      </div>
    </div>
  );
}

export default function ResetPasswordPage() {
  return (
    <Suspense
      fallback={
        <div
          className="min-h-screen bg-gray-50 flex items-center justify-center"
          data-oid="5w5xb9."
        >
          Loading...
        </div>
      }
      data-oid="8:dgss3"
    >
      <ResetPasswordForm data-oid="bh-nxij" />
    </Suspense>
  );
}
