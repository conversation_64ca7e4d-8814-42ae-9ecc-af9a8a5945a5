/**
 * Logging Module
 * 
 * This module provides utilities for consistent logging throughout the application.
 * It supports different log levels, structured logging, and can be configured to
 * output logs to different destinations (console, server, file, etc.)
 */

// Log levels in order of severity
export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'critical';

export type LogContext = {
  userId?: string;
  sessionId?: string;
  url?: string;
  component?: string;
  action?: string;
  [key: string]: any;
};

export type LogConfig = {
  minLevel: LogLevel;
  enableConsole: boolean;
  enableServer: boolean;
  serverEndpoint?: string;
  includeTimestamps: boolean;
  redactSensitiveData: boolean;
  sensitiveKeys: string[];
  logSamplingRate: number; // 0-1 value for what percentage of logs to send to server
};

// Default configuration
const defaultConfig: LogConfig = {
  minLevel: 'info',
  enableConsole: true,
  enableServer: process.env.NODE_ENV === 'production',
  serverEndpoint: '/api/logging',
  includeTimestamps: true,
  redactSensitiveData: true,
  sensitiveKeys: ['password', 'token', 'secret', 'key', 'auth', 'credential', 'ssn', 'credit'],
  logSamplingRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0, // Sample 10% in production, 100% in dev
};

let currentConfig = { ...defaultConfig };

// Log level priorities (for filtering)
const LOG_LEVEL_PRIORITY: Record<LogLevel, number> = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3,
  critical: 4,
};

/**
 * Initialize logging with custom configuration
 */
export function initLogging(config: Partial<LogConfig> = {}): void {
  currentConfig = { ...defaultConfig, ...config };
}

/**
 * Main logging function
 */
export function log(
  level: LogLevel,
  message: string,
  context: LogContext = {}
): void {
  // Check if this log level should be processed
  if (LOG_LEVEL_PRIORITY[level] < LOG_LEVEL_PRIORITY[currentConfig.minLevel]) {
    return;
  }
  
  // Check if we should sample this log for server-side logging
  const shouldSendToServer = 
    currentConfig.enableServer && 
    Math.random() <= currentConfig.logSamplingRate;
  
  // Prepare log data
  const timestamp = currentConfig.includeTimestamps ? new Date().toISOString() : undefined;
  
  // Redact sensitive data if configured
  const safeContext = currentConfig.redactSensitiveData 
    ? redactSensitiveData(context) 
    : context;
  
  const logData = {
    level,
    message,
    timestamp,
    context: {
      url: typeof window !== 'undefined' ? window.location.href : '',
      ...safeContext,
    },
  };
  
  // Log to console if enabled
  if (currentConfig.enableConsole) {
    logToConsole(level, message, logData);
  }
  
  // Log to server if enabled and sampled
  if (shouldSendToServer) {
    logToServer(logData);
  }
}

// Convenience methods for different log levels
export const debug = (message: string, context?: LogContext) => log('debug', message, context);
export const info = (message: string, context?: LogContext) => log('info', message, context);
export const warn = (message: string, context?: LogContext) => log('warn', message, context);
export const error = (message: string, context?: LogContext) => log('error', message, context);
export const critical = (message: string, context?: LogContext) => log('critical', message, context);

/**
 * Create a logger instance with predefined context
 */
export function createLogger(defaultContext: LogContext = {}) {
  return {
    debug: (message: string, context?: LogContext) => 
      log('debug', message, { ...defaultContext, ...context }),
    info: (message: string, context?: LogContext) => 
      log('info', message, { ...defaultContext, ...context }),
    warn: (message: string, context?: LogContext) => 
      log('warn', message, { ...defaultContext, ...context }),
    error: (message: string, context?: LogContext) => 
      log('error', message, { ...defaultContext, ...context }),
    critical: (message: string, context?: LogContext) => 
      log('critical', message, { ...defaultContext, ...context }),
  };
}

// Private helper functions

function logToConsole(level: LogLevel, message: string, logData: any): void {
  const timestamp = logData.timestamp ? `[${logData.timestamp}] ` : '';
  const prefix = `${timestamp}[${level.toUpperCase()}]`;
  
  // Use different console methods based on level
  switch (level) {
    case 'debug':
      console.debug(`${prefix} ${message}`, logData.context);
      break;
    case 'info':
      console.info(`${prefix} ${message}`, logData.context);
      break;
    case 'warn':
      console.warn(`${prefix} ${message}`, logData.context);
      break;
    case 'error':
    case 'critical':
      console.error(`${prefix} ${message}`, logData.context);
      break;
    default:
      console.log(`${prefix} ${message}`, logData.context);
  }
}

async function logToServer(logData: any): Promise<void> {
  if (!currentConfig.serverEndpoint) return;
  
  try {
    // Use sendBeacon for non-critical logs if available (doesn't block page unload)
    if (
      typeof navigator !== 'undefined' && 
      navigator.sendBeacon && 
      logData.level !== 'critical' && 
      logData.level !== 'error'
    ) {
      const blob = new Blob([JSON.stringify(logData)], { type: 'application/json' });
      navigator.sendBeacon(currentConfig.serverEndpoint, blob);
    } else {
      // Fall back to fetch for critical logs or when sendBeacon is not available
      await fetch(currentConfig.serverEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(logData),
        // For critical logs, we want to ensure they're sent even during page unload
        keepalive: logData.level === 'critical' || logData.level === 'error',
      });
    }
  } catch (err) {
    // Only log to console to avoid infinite loops
    console.error('Failed to send log to server:', err);
  }
}

function redactSensitiveData(data: any, path: string = ''): any {
  if (data === null || data === undefined) {
    return data;
  }
  
  // Handle different data types
  if (Array.isArray(data)) {
    return data.map((item, index) => redactSensitiveData(item, `${path}[${index}]`));
  }
  
  if (typeof data === 'object') {
    return Object.keys(data).reduce((result, key) => {
      const currentPath = path ? `${path}.${key}` : key;
      
      // Check if this key should be redacted
      const shouldRedact = currentConfig.sensitiveKeys.some(sensitiveKey => 
        key.toLowerCase().includes(sensitiveKey.toLowerCase())
      );
      
      result[key] = shouldRedact 
        ? '[REDACTED]' 
        : redactSensitiveData(data[key], currentPath);
      
      return result;
    }, {} as Record<string, any>);
  }
  
  return data;
}