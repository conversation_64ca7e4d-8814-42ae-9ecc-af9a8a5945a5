/**
 * Error Handling Module
 * 
 * This module provides utilities for consistent error handling, logging,
 * and reporting throughout the application.
 */

// Types for error handling
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

export type ErrorContext = {
  userId?: string;
  sessionId?: string;
  url?: string;
  component?: string;
  action?: string;
  [key: string]: any;
};

export type ErrorConfig = {
  captureUnhandledErrors: boolean;
  logToConsole: boolean;
  logToServer: boolean;
  notifyUser: boolean;
  errorReportingEndpoint?: string;
  errorSamplingRate: number; // 0-1 value for what percentage of errors to report
  ignorePatterns: RegExp[];
};

// Default configuration
const defaultConfig: ErrorConfig = {
  captureUnhandledErrors: true,
  logToConsole: true,
  logToServer: true,
  notifyUser: true,
  errorReportingEndpoint: '/api/error-reporting',
  errorSamplingRate: 1.0, // Report all errors by default
  ignorePatterns: [
    /Failed to load resource/i,
    /ResizeObserver loop/i,
    /Network request failed/i,
  ],
};

let currentConfig = { ...defaultConfig };

/**
 * Initialize error handling with custom configuration
 */
export function initErrorHandling(config: Partial<ErrorConfig> = {}): void {
  currentConfig = { ...defaultConfig, ...config };
  
  if (currentConfig.captureUnhandledErrors && typeof window !== 'undefined') {
    // Set up global error handlers
    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
  }
}

/**
 * Handle and log an error with context
 */
export function handleError(
  error: Error | string,
  severity: ErrorSeverity = 'medium',
  context: ErrorContext = {}
): void {
  const errorObj = typeof error === 'string' ? new Error(error) : error;
  const timestamp = new Date().toISOString();
  
  // Check if we should ignore this error based on patterns
  if (shouldIgnoreError(errorObj.message)) {
    return;
  }
  
  // Check if we should sample this error
  if (Math.random() > currentConfig.errorSamplingRate) {
    return;
  }
  
  // Prepare error data
  const errorData = {
    message: errorObj.message,
    stack: errorObj.stack,
    severity,
    timestamp,
    context: {
      url: typeof window !== 'undefined' ? window.location.href : '',
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
      ...context,
    },
  };
  
  // Log to console if enabled
  if (currentConfig.logToConsole) {
    logErrorToConsole(errorData);
  }
  
  // Log to server if enabled
  if (currentConfig.logToServer) {
    logErrorToServer(errorData);
  }
  
  // Notify user if enabled and error is high severity
  if (currentConfig.notifyUser && (severity === 'high' || severity === 'critical')) {
    notifyUserOfError(errorData);
  }
}

/**
 * Create a boundary error handler for React components
 */
export function createErrorBoundaryHandler(
  componentName: string,
  fallback: React.ReactNode
) {
  return {
    onError: (error: Error, info: { componentStack: string }) => {
      handleError(error, 'high', {
        component: componentName,
        componentStack: info.componentStack,
      });
    },
    fallback,
  };
}

/**
 * Create an API route error handler
 */
export function createApiErrorHandler() {
  return (err: any, req: any, res: any, _unusedNext: any) => {
    const statusCode = err.statusCode || 500;
    const message = err.message || 'Internal Server Error';
    
    handleError(err, statusCode >= 500 ? 'high' : 'medium', {
      url: req.url,
      method: req.method,
      query: req.query,
      headers: req.headers,
      userId: req.user?.id,
    });
    
    res.status(statusCode).json({
      error: {
        message,
        ...(process.env.NODE_ENV !== 'production' ? { stack: err.stack } : {}),
      },
    });
  };
}

// Private helper functions

function handleGlobalError(event: ErrorEvent): void {
  handleError(event.error || event.message, 'medium', {
    action: 'global_error',
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
  });
}

function handleUnhandledRejection(event: PromiseRejectionEvent): void {
  handleError(
    event.reason instanceof Error ? event.reason : new Error(String(event.reason)),
    'medium',
    { action: 'unhandled_rejection' }
  );
}

function shouldIgnoreError(message: string): boolean {
  return currentConfig.ignorePatterns.some(pattern => pattern.test(message));
}

function logErrorToConsole(errorData: any): void {
  const { severity, message } = errorData;
  
  // Use different console methods based on severity
  switch (severity) {
    case 'critical':
    case 'high':
      console.error(`[ERROR] ${message}`, { ...errorData });
      break;
    case 'medium':
      console.warn(`[WARNING] ${message}`, { ...errorData });
      break;
    case 'low':
      console.info(`[INFO] ${message}`, { ...errorData });
      break;
    default:
      console.log(`[LOG] ${message}`, { ...errorData });
  }
}

async function logErrorToServer(errorData: any): Promise<void> {
  if (!currentConfig.errorReportingEndpoint) return;
  
  try {
    await fetch(currentConfig.errorReportingEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(errorData),
    });
  } catch (err) {
    // Don't recursively call handleError here to avoid infinite loops
    console.error('Failed to send error to server:', err);
  }
}

function notifyUserOfError(_unusedErrorData: any): void {
  // In a real implementation, this would show a toast or notification
  // For now, we'll just log to console
  console.log('[USER NOTIFICATION] An error occurred. Our team has been notified.');
}
