import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { User } from '@/payload-types' // Import User type
import { withErrorHandling, createApiError } from '@/lib/error-handler'
import { logger } from '@/lib/logger'

export const POST = withErrorHandling(async (request: NextRequest) => {
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })

  // Get user from the request
  const { user } = await payload.auth({ headers: request.headers }) as { user: User | null }

  if (!user) {
    throw createApiError({
      message: 'Unauthorized',
      statusCode: 401,
      code: 'UNAUTHORIZED'
    })
  }

  const body = await request.json()
  const { items, totalAmount } = body

  if (!items || !Array.isArray(items) || items.length === 0) {
    throw createApiError({
      message: 'Invalid items',
      statusCode: 400,
      code: 'INVALID_ITEMS'
    })
  }

  if (!totalAmount || totalAmount <= 0) {
    throw createApiError({
      message: 'Invalid total amount',
      statusCode: 400,
      code: 'INVALID_AMOUNT'
    })
  }

  // Validate items exist and are available
  for (const item of items) {
    const snack = await payload.findByID({
      collection: 'snacks',
      id: item.snack,
    })

    if (!snack || !snack.available) {
      throw createApiError({
        message: `Snack ${item.snack} is not available`,
        statusCode: 400,
        code: 'SNACK_UNAVAILABLE',
        details: { snackId: item.snack }
      })
    }
  }

  logger.info('Creating new order', {
    context: 'Orders',
    data: { userId: user.id, itemCount: items.length, totalAmount }
  })

  // Create the order
  const order = await payload.create({
    collection: 'orders',
    data: {
      user: user.id,
      items,
      totalAmount,
      status: 'pending',
      orderDate: new Date().toISOString(),
    },
  })

  return NextResponse.json({ success: true, doc: order })
})

export const GET = withErrorHandling(async (request: NextRequest) => {
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })

  // Get user from the request
  const { user } = await payload.auth({ headers: request.headers }) as { user: User | null }

  if (!user) {
    throw createApiError({
      message: 'Unauthorized',
      statusCode: 401,
      code: 'UNAUTHORIZED'
    })
  }

  logger.debug('Fetching orders for user', {
    context: 'Orders',
    data: { userId: user.id }
  })

  // Get user's orders
  const orders = await payload.find({
    collection: 'orders',
    where: {
      user: {
        equals: user.id,
      },
    },
    depth: 2,
    sort: '-orderDate',
  })

  return NextResponse.json({ orders: orders.docs })
})
