/**
 * Mobile-First Responsive Navbar Component
 *
 * Features:
 * - Mobile hamburger menu with smooth animations
 * - Desktop horizontal navigation
 * - User authentication state handling
 * - Search functionality
 * - Accessibility compliant
 */
'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { useUser, SignInButton, UserButton } from '@clerk/nextjs';
import {
  Bars3Icon,
  XMarkIcon,
  MagnifyingGlassIcon,
  GlobeAltIcon,
  DevicePhoneMobileIcon,
  QuestionMarkCircleIcon,
  UserCircleIcon,
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';

const navigation = [
  { name: 'Home', href: '/', icon: GlobeAltIcon },
  { name: 'Search Plans', href: '/search', icon: MagnifyingGlassIcon },
  { name: 'How It Works', href: '/how-it-works', icon: QuestionMarkCircleIcon },
  { name: 'My Orders', href: '/my-orders', icon: DevicePhoneMobileIcon, requiresAuth: true },
];

export function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const pathname = usePathname();
  const { isSignedIn, isLoaded } = useUser();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu on route change
  useEffect(() => {
    setIsOpen(false);
  }, [pathname]);

  const filteredNavigation = navigation.filter(item => !item.requiresAuth || isSignedIn);

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200'
          : 'bg-white/80 backdrop-blur-sm'
      }`}
      data-oid="gumw2_s"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8" data-oid="41_4z7o">
        <div className="flex items-center justify-between h-16" data-oid="pak1h_0">
          {/* Logo */}
          <Link
            href="/"
            className="flex items-center space-x-3 group w-[110px] h-[53px]"
            data-oid="t5hl4bl"
          >
            <div
              className="relative group-hover:scale-105 transition-transform duration-200 h-[93px] w-[109px]"
              data-oid="u2i.4ty"
            >
              <img
                className="w-[108px] h-[103px]"
                src="/images/LogoT.png"
                alt="LogoT.png"
                data-oid="p-9npqu"
              />
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div
            className="hidden md:flex items-center space-x-8 text-center relative top-auto right-auto bottom-auto left-auto"
            data-oid="f4xnmyl"
          >
            {filteredNavigation.map(item => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`relative px-3 py-2 text-sm font-medium transition-colors duration-200 ${
                    isActive ? 'text-green-600' : 'text-gray-700 hover:text-green-600'
                  }`}
                  data-oid="81ffepv"
                >
                  {item.name}
                  {isActive && (
                    <motion.div
                      layoutId="activeTab"
                      className="absolute bottom-0 left-0 right-0 h-0.5 bg-green-600 rounded-full"
                      initial={false}
                      transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                      data-oid="qstwzlm"
                    />
                  )}
                </Link>
              );
            })}
          </div>

          {/* Desktop Auth & Search */}
          <div className="hidden md:flex items-center space-x-4" data-oid="-rvwmmd">
            <Button
              variant="outline"
              size="sm"
              className="border-gray-300 text-gray-700 hover:border-green-500 hover:text-green-600"
              data-oid="0p4mtrt"
            >
              <MagnifyingGlassIcon className="w-4 h-4 mr-2" data-oid="ko-p72i" />
              Search
            </Button>

            {isLoaded && (
              <>
                {isSignedIn ? (
                  <UserButton
                    appearance={{
                      elements: {
                        avatarBox: 'w-8 h-8',
                      },
                    }}
                    data-oid="r4q39s-"
                  />
                ) : (
                  <SignInButton mode="modal" data-oid="am92br-">
                    <Button
                      size="sm"
                      className="bg-green-600 hover:bg-green-700 text-white"
                      data-oid="f.s1n-t"
                    >
                      Sign In
                    </Button>
                  </SignInButton>
                )}
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="md:hidden p-2 rounded-lg text-gray-700 hover:text-green-600 hover:bg-gray-100 transition-colors duration-200"
            aria-label="Toggle navigation menu"
            data-oid="7bbbqju"
          >
            {isOpen ? (
              <XMarkIcon className="w-6 h-6" data-oid="vs2gkw8" />
            ) : (
              <Bars3Icon className="w-6 h-6" data-oid="ujdbpnm" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      <AnimatePresence data-oid="6x7c5he">
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="md:hidden bg-white border-t border-gray-200 shadow-lg"
            data-oid="d:sppe0"
          >
            <div className="px-4 py-4 space-y-2" data-oid="n50wof2">
              {filteredNavigation.map(item => {
                const isActive = pathname === item.href;
                const Icon = item.icon;
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`flex items-center space-x-3 px-3 py-3 rounded-lg text-base font-medium transition-colors duration-200 ${
                      isActive
                        ? 'bg-green-50 text-green-600 border border-green-200'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-green-600'
                    }`}
                    data-oid="j2id1bi"
                  >
                    <Icon className="w-5 h-5" data-oid="bgbmn4." />
                    <span data-oid="w9xy85e">{item.name}</span>
                  </Link>
                );
              })}

              {/* Mobile Search */}
              <button
                className="flex items-center space-x-3 px-3 py-3 rounded-lg text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-green-600 transition-colors duration-200 w-full"
                data-oid="x1b4lc5"
              >
                <MagnifyingGlassIcon className="w-5 h-5" data-oid="-ga2stw" />
                <span data-oid="1s5tjke">Search Plans</span>
              </button>

              {/* Mobile Auth */}
              {isLoaded && (
                <div className="pt-4 border-t border-gray-200" data-oid="l6l:kpf">
                  {isSignedIn ? (
                    <div className="flex items-center space-x-3 px-3 py-3" data-oid="rf9ibk5">
                      <UserCircleIcon className="w-5 h-5 text-gray-500" data-oid="mllfwlt" />
                      <span className="text-gray-700" data-oid=":nz:nc:">
                        Account
                      </span>
                    </div>
                  ) : (
                    <SignInButton mode="modal" data-oid="j8xgg9l">
                      <Button
                        className="w-full bg-green-600 hover:bg-green-700 text-white"
                        data-oid="5r.3043"
                      >
                        Sign In
                      </Button>
                    </SignInButton>
                  )}
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
}
