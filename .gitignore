# dependencies
node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE
.idea
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json

# Payload CMS
dist
build

# Logs
logs
*.log

# Bundle analyzer
/analyze

# Sentry
.sentryclirc

# Sitemap
public/sitemap*.xml
public/robots.txt

# Security & Authentication
*.key
*.crt
*.p12
*.pfx
.clerk/
secrets/
.secrets/

# Testing & Coverage
.jest/
test-results/
playwright-report/
e2e/test-results/
e2e/playwright-report/
__tests__/__snapshots__/
.nyc_output
*.lcov

# Performance & Monitoring
.turbo/
.lighthouseci/
.next/trace
.analytics/
.sentry/

# Feature Flags & Config
.flags/
.vercel/
local.config.js
local.config.ts

# Database & Uploads
*.db
*.sqlite
*.sqlite3
uploads/
public/uploads/
backups/
.backups/

# Generated Files
src/payload-types.ts
src/generated/
docs/api/generated/

# Cache & Temporary
.cache/
.parcel-cache/
.eslintcache
tmp/
temp/
*.backup
*.bak
*.tmp
