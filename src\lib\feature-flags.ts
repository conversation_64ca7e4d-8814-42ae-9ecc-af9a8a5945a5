import React from 'react'

// Mock flag function since @vercel/flags is deprecated
const flag = (config: { key: string; decide: () => boolean }) => {
  return async () => config.decide()
}

/**
 * Feature flags for the GGsim platform
 * These flags control the availability of features during beta testing
 */

// Core feature flags
export const showHeroSection = flag({
  key: 'show-hero-section',
  decide: () => true, // Always show hero section
})

export const enableSearch = flag({
  key: 'enable-search',
  decide: () => process.env.NEXT_PUBLIC_ENVIRONMENT !== 'development',
})

export const enableCheckout = flag({
  key: 'enable-checkout',
  decide: () => false, // Disabled during initial beta
})

export const enableUserDashboard = flag({
  key: 'enable-user-dashboard',
  decide: () => true,
})

// Beta testing features
export const enableFeedbackWidget = flag({
  key: 'enable-feedback-widget',
  decide: () => process.env.NEXT_PUBLIC_ENVIRONMENT === 'production',
})

export const enableAnalytics = flag({
  key: 'enable-analytics',
  decide: () => process.env.NEXT_PUBLIC_ENVIRONMENT === 'production',
})

export const enableErrorReporting = flag({
  key: 'enable-error-reporting',
  decide: () => true,
})

// Admin features
export const enableAdminDashboard = flag({
  key: 'enable-admin-dashboard',
  decide: () => true,
})

export const enableUserManagement = flag({
  key: 'enable-user-management',
  decide: () => true,
})

export const enableBetaMetrics = flag({
  key: 'enable-beta-metrics',
  decide: () => true,
})

// Developer features
export const enableDebugMode = flag({
  key: 'enable-debug-mode',
  decide: () => process.env.NODE_ENV === 'development',
})

export const enablePerformanceMonitoring = flag({
  key: 'enable-performance-monitoring',
  decide: () => true,
})

export const enableFeatureFlagUI = flag({
  key: 'enable-feature-flag-ui',
  decide: () => process.env.NODE_ENV === 'development',
})

// Experimental features
export const enableNewHeroDesign = flag({
  key: 'enable-new-hero-design',
  decide: () => false, // A/B testing flag
})

export const enableAdvancedFilters = flag({
  key: 'enable-advanced-filters',
  decide: () => false, // Coming soon
})

export const enablePushNotifications = flag({
  key: 'enable-push-notifications',
  decide: () => false, // Future feature
})

/**
 * Feature flag utilities
 */

// Type for all available feature flags
export type FeatureFlag = 
  | 'show-hero-section'
  | 'enable-search'
  | 'enable-checkout'
  | 'enable-user-dashboard'
  | 'enable-feedback-widget'
  | 'enable-analytics'
  | 'enable-error-reporting'
  | 'enable-admin-dashboard'
  | 'enable-user-management'
  | 'enable-beta-metrics'
  | 'enable-debug-mode'
  | 'enable-performance-monitoring'
  | 'enable-feature-flag-ui'
  | 'enable-new-hero-design'
  | 'enable-advanced-filters'
  | 'enable-push-notifications'

// Helper function to check if a feature is enabled
export async function isFeatureEnabled(flagKey: FeatureFlag): Promise<boolean> {
  try {
    switch (flagKey) {
      case 'show-hero-section':
        return await showHeroSection()
      case 'enable-search':
        return await enableSearch()
      case 'enable-checkout':
        return await enableCheckout()
      case 'enable-user-dashboard':
        return await enableUserDashboard()
      case 'enable-feedback-widget':
        return await enableFeedbackWidget()
      case 'enable-analytics':
        return await enableAnalytics()
      case 'enable-error-reporting':
        return await enableErrorReporting()
      case 'enable-admin-dashboard':
        return await enableAdminDashboard()
      case 'enable-user-management':
        return await enableUserManagement()
      case 'enable-beta-metrics':
        return await enableBetaMetrics()
      case 'enable-debug-mode':
        return await enableDebugMode()
      case 'enable-performance-monitoring':
        return await enablePerformanceMonitoring()
      case 'enable-feature-flag-ui':
        return await enableFeatureFlagUI()
      case 'enable-new-hero-design':
        return await enableNewHeroDesign()
      case 'enable-advanced-filters':
        return await enableAdvancedFilters()
      case 'enable-push-notifications':
        return await enablePushNotifications()
      default:
        return false
    }
  } catch (error) {
    console.error(`Error checking feature flag ${flagKey}:`, error)
    return false
  }
}

// Client-side feature flag hook
export function useFeatureFlag(flagKey: FeatureFlag) {
  const [isEnabled, setIsEnabled] = React.useState(false)
  const [isLoading, setIsLoading] = React.useState(true)

  React.useEffect(() => {
    isFeatureEnabled(flagKey)
      .then(setIsEnabled)
      .catch(() => setIsEnabled(false))
      .finally(() => setIsLoading(false))
  }, [flagKey])

  return { isEnabled, isLoading }
}

// Feature flag context for React components
export const FeatureFlagContext = React.createContext<{
  flags: Record<FeatureFlag, boolean>
  isLoading: boolean
}>({
  flags: {} as Record<FeatureFlag, boolean>,
  isLoading: true,
})

// Provider component for feature flags
export function FeatureFlagProvider({ children }: { children: React.ReactNode }) {
  const [flags, setFlags] = React.useState<Record<FeatureFlag, boolean>>({} as Record<FeatureFlag, boolean>)
  const [isLoading, setIsLoading] = React.useState(true)

  React.useEffect(() => {
    const loadFlags = async () => {
      const flagKeys: FeatureFlag[] = [
        'show-hero-section',
        'enable-search',
        'enable-checkout',
        'enable-user-dashboard',
        'enable-feedback-widget',
        'enable-analytics',
        'enable-error-reporting',
        'enable-admin-dashboard',
        'enable-user-management',
        'enable-beta-metrics',
        'enable-debug-mode',
        'enable-performance-monitoring',
        'enable-feature-flag-ui',
        'enable-new-hero-design',
        'enable-advanced-filters',
        'enable-push-notifications',
      ]

      const flagResults = await Promise.all(
        flagKeys.map(async (key) => [key, await isFeatureEnabled(key)] as const)
      )

      const flagsObject = Object.fromEntries(flagResults) as Record<FeatureFlag, boolean>
      setFlags(flagsObject)
      setIsLoading(false)
    }

    loadFlags()
  }, [])

  return React.createElement(
    FeatureFlagContext.Provider,
    { value: { flags, isLoading } },
    children
  )
}

// Hook to use feature flags from context
export function useFeatureFlags() {
  return React.useContext(FeatureFlagContext)
}
