import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types for TypeScript
export interface User {
  id: string
  email: string
  role: 'beta-tester' | 'developer' | 'admin'
  created_at: string
  updated_at: string
  metadata?: Record<string, any>
}

export interface Feedback {
  id: string
  user_id?: string
  user_email?: string
  type: 'bug' | 'feature' | 'general' | 'love'
  rating?: number
  message: string
  page: string
  user_agent: string
  timestamp: string
  processed: boolean
  created_at: string
}

export interface eSIMPlan {
  id: string
  provider: string
  country: string
  region: string
  data_amount: string
  validity_days: number
  price: number
  currency: string
  features: string[]
  created_at: string
  updated_at: string
}

export interface Order {
  id: string
  user_id: string
  plan_id: string
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  amount: number
  currency: string
  stripe_payment_intent_id?: string
  created_at: string
  updated_at: string
}

// Helper functions for database operations
export const dbHelpers = {
  // User operations
  async createUser(userData: Omit<User, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('users')
      .insert([userData])
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async getUserByEmail(email: string) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single()
    
    if (error && error.code !== 'PGRST116') throw error
    return data
  },

  async updateUserRole(userId: string, role: User['role']) {
    const { data, error } = await supabase
      .from('users')
      .update({ role, updated_at: new Date().toISOString() })
      .eq('id', userId)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Feedback operations
  async createFeedback(feedbackData: Omit<Feedback, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('feedback')
      .insert([{ ...feedbackData, created_at: new Date().toISOString() }])
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async getFeedback(limit = 50, offset = 0) {
    const { data, error } = await supabase
      .from('feedback')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)
    
    if (error) throw error
    return data
  },

  async markFeedbackProcessed(feedbackId: string) {
    const { data, error } = await supabase
      .from('feedback')
      .update({ processed: true })
      .eq('id', feedbackId)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // eSIM Plan operations
  async getPlans(filters?: { country?: string; region?: string }) {
    let query = supabase.from('esim_plans').select('*')
    
    if (filters?.country) {
      query = query.eq('country', filters.country)
    }
    if (filters?.region) {
      query = query.eq('region', filters.region)
    }
    
    const { data, error } = await query.order('price', { ascending: true })
    
    if (error) throw error
    return data
  },

  async getPlanById(planId: string) {
    const { data, error } = await supabase
      .from('esim_plans')
      .select('*')
      .eq('id', planId)
      .single()
    
    if (error) throw error
    return data
  },

  // Order operations
  async createOrder(orderData: Omit<Order, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('orders')
      .insert([{
        ...orderData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async updateOrderStatus(orderId: string, status: Order['status'], stripePaymentIntentId?: string) {
    const updateData: any = { 
      status, 
      updated_at: new Date().toISOString() 
    }
    
    if (stripePaymentIntentId) {
      updateData.stripe_payment_intent_id = stripePaymentIntentId
    }
    
    const { data, error } = await supabase
      .from('orders')
      .update(updateData)
      .eq('id', orderId)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async getUserOrders(userId: string) {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        esim_plans (*)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  }
}
