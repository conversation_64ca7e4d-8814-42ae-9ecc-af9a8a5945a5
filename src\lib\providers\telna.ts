import { AxiosRequestConfig } from 'axios'
import {
  BaseESIMProvider,
  ESIMPlan,
  ESIMPurchaseRequest,
  ESIMPurchaseResponse,
  PlanQuery,
  ProviderConfig,
} from './base-provider'

// Telna specific interfaces
interface TelnaConfig extends ProviderConfig {
  apiKey: string
}

interface TelnaPlan {
  sku: string
  name: string
  country: string
  iso_code: string
  region: string
  data_gb: number
  duration_days: number
  price: number
  currency: string
  carrier: string
  technology: string
  features: string[]
  supported_countries: string[]
}

interface TelnaOrderRequest {
  sku: string
  customer: {
    email: string
    name?: string
  }
  reference_id?: string
}

interface TelnaOrderResponse {
  order_id: string
  state: string
  esim: {
    qr_code: string
    iccid: string
    lpa_string: string
  }
  expiration: string
  instructions: string
  carrier_info: {
    name: string
    apn: string
    technology: string
  }
}

export class TelnaProvider extends BaseESIMProvider {
  constructor(config: TelnaConfig) {
    super(config, 'Telna')
  }

  addAuthHeaders(config: AxiosRequestConfig): AxiosRequestConfig {
    return {
      ...config,
      headers: {
        ...config.headers,
        'Authorization': `Token ${this.config.apiKey}`,
        'Content-Type': 'application/json',
      },
    }
  }

  async fetchPlans(query: PlanQuery): Promise<ESIMPlan[]> {
    this.validatePlanQuery(query)

    try {
      const params: any = {}
      
      if (query.country) params.country = query.country
      if (query.countryCode) params.iso_code = query.countryCode
      if (query.region) params.region = query.region
      if (query.dataAmount) params.min_data_gb = query.dataAmount
      if (query.maxPrice) params.max_price = query.maxPrice

      const response = await this.retryRequest(async () => {
        return await this.makeRequest<{ plans: TelnaPlan[] }>(
          'GET',
          '/v2/plans',
          undefined,
          true
        )
      })

      return response.plans.map(plan => this.transformPlan(plan))
    } catch (error) {
      console.error('Telna fetchPlans error:', error)
      throw error
    }
  }

  async purchasePlan(request: ESIMPurchaseRequest): Promise<ESIMPurchaseResponse> {
    try {
      const orderRequest: TelnaOrderRequest = {
        sku: request.planId,
        customer: {
          email: request.customerEmail,
          name: request.customerName,
        },
        reference_id: request.metadata?.orderId,
      }

      const response = await this.retryRequest(async () => {
        return await this.makeRequest<TelnaOrderResponse>(
          'POST',
          '/v2/orders',
          orderRequest,
          false
        )
      })

      return this.transformOrderResponse(response)
    } catch (error) {
      console.error('Telna purchasePlan error:', error)
      throw error
    }
  }

  async getOrderStatus(orderId: string): Promise<ESIMPurchaseResponse> {
    try {
      const response = await this.retryRequest(async () => {
        return await this.makeRequest<TelnaOrderResponse>(
          'GET',
          `/v2/orders/${orderId}`,
          undefined,
          false
        )
      })

      return this.transformOrderResponse(response)
    } catch (error) {
      console.error('Telna getOrderStatus error:', error)
      throw error
    }
  }

  // Transform Telna plan format to our standard format
  private transformPlan(plan: TelnaPlan): ESIMPlan {
    return {
      id: plan.sku,
      name: plan.name,
      country: plan.country,
      countryCode: plan.iso_code,
      region: plan.region,
      dataAmount: plan.data_gb,
      validity: plan.duration_days,
      price: this.formatPrice(plan.price, plan.currency),
      currency: 'USD', // Normalized to USD
      provider: 'telna',
      features: plan.features || [],
      coverage: plan.supported_countries || [plan.country],
      networkInfo: {
        operator: plan.carrier,
        networkType: plan.technology,
      },
    }
  }

  // Transform Telna order response to our standard format
  private transformOrderResponse(response: TelnaOrderResponse): ESIMPurchaseResponse {
    return {
      orderId: response.order_id,
      activationCode: response.esim.qr_code || response.esim.lpa_string,
      iccid: response.esim.iccid,
      status: this.mapStatus(response.state),
      expiryDate: response.expiration,
      activationInstructions: response.instructions,
      networkInfo: response.carrier_info ? {
        operator: response.carrier_info.name,
        apn: response.carrier_info.apn,
        networkType: response.carrier_info.technology,
      } : undefined,
    }
  }

  // Map Telna status to our standard status
  private mapStatus(status: string): 'pending' | 'ready' | 'activated' {
    switch (status.toLowerCase()) {
      case 'pending':
      case 'processing':
      case 'created':
        return 'pending'
      case 'completed':
      case 'ready':
      case 'delivered':
      case 'provisioned':
        return 'ready'
      case 'activated':
      case 'active':
      case 'installed':
        return 'activated'
      default:
        return 'pending'
    }
  }

  // Telna specific health check
  async healthCheck(): Promise<boolean> {
    try {
      // Try to fetch account info as a health check
      await this.makeRequest('GET', '/v2/account', undefined, false)
      return true
    } catch (error) {
      console.error('Telna health check failed:', error)
      return false
    }
  }

  // Get available countries from Telna
  async getAvailableCountries(): Promise<Array<{ code: string; name: string; region: string }>> {
    try {
      const response = await this.makeRequest<{ 
        countries: Array<{ 
          iso_code: string
          name: string
          region: string 
        }> 
      }>(
        'GET',
        '/v2/countries',
        undefined,
        true
      )

      return response.countries.map(country => ({
        code: country.iso_code,
        name: country.name,
        region: country.region,
      }))
    } catch (error) {
      console.error('Telna getAvailableCountries error:', error)
      return []
    }
  }

  // Get plan details by SKU
  async getPlanDetails(sku: string): Promise<ESIMPlan | null> {
    try {
      const response = await this.makeRequest<TelnaPlan>(
        'GET',
        `/v2/plans/${sku}`,
        undefined,
        true
      )

      return this.transformPlan(response)
    } catch (error) {
      console.error('Telna getPlanDetails error:', error)
      return null
    }
  }

  // Get account information
  async getAccountInfo(): Promise<{ balance: number; currency: string; credit_limit: number } | null> {
    try {
      const response = await this.makeRequest<{
        balance: number
        currency: string
        credit_limit: number
      }>(
        'GET',
        '/v2/account',
        undefined,
        false
      )

      return {
        balance: response.balance,
        currency: response.currency,
        credit_limit: response.credit_limit,
      }
    } catch (error) {
      console.error('Telna getAccountInfo error:', error)
      return null
    }
  }

  // Get usage data for an activated eSIM
  async getUsageData(iccid: string): Promise<{ used: number; remaining: number; total: number } | null> {
    try {
      const response = await this.makeRequest<{
        usage: {
          data_used_gb: number
          data_remaining_gb: number
          data_total_gb: number
        }
      }>(
        'GET',
        `/v2/esim/${iccid}/usage`,
        undefined,
        false
      )

      return {
        used: response.usage.data_used_gb,
        remaining: response.usage.data_remaining_gb,
        total: response.usage.data_total_gb,
      }
    } catch (error) {
      console.error('Telna getUsageData error:', error)
      return null
    }
  }

  // Get eSIM status
  async getESIMStatus(iccid: string): Promise<{ status: string; last_activity: string } | null> {
    try {
      const response = await this.makeRequest<{
        status: string
        last_activity: string
      }>(
        'GET',
        `/v2/esim/${iccid}/status`,
        undefined,
        false
      )

      return {
        status: response.status,
        last_activity: response.last_activity,
      }
    } catch (error) {
      console.error('Telna getESIMStatus error:', error)
      return null
    }
  }

  // Terminate an eSIM
  async terminateESIM(iccid: string): Promise<boolean> {
    try {
      await this.makeRequest(
        'POST',
        `/v2/esim/${iccid}/terminate`,
        undefined,
        false
      )
      return true
    } catch (error) {
      console.error('Telna terminateESIM error:', error)
      return false
    }
  }

  // Get order history
  async getOrderHistory(limit: number = 50): Promise<TelnaOrderResponse[]> {
    try {
      const response = await this.makeRequest<{ orders: TelnaOrderResponse[] }>(
        'GET',
        `/v2/orders?limit=${limit}`,
        undefined,
        false
      )

      return response.orders
    } catch (error) {
      console.error('Telna getOrderHistory error:', error)
      return []
    }
  }
}

// Factory function to create Telna provider instance
export function createTelnaProvider(): TelnaProvider {
  const config: TelnaConfig = {
    apiKey: process.env.TELNA_API_KEY || '',
    apiUrl: process.env.TELNA_API_URL || 'https://sandbox.telna.com/api',
    environment: (process.env.TELNA_ENVIRONMENT as 'sandbox' | 'production') || 'sandbox',
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 1000,
  }

  if (!config.apiKey) {
    throw new Error('TELNA_API_KEY environment variable is required')
  }

  return new TelnaProvider(config)
}
