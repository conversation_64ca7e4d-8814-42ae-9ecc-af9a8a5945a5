import { headers as getHeaders } from 'next/headers.js'
import Image from 'next/image'
import { getPayload } from 'payload'
import React from 'react'
import config from '@/payload.config'
import { notFound } from 'next/navigation'
import { AddToCartButton } from '@/components/add-to-cart-button'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { Badge } from '@/components/ui/badge'
import { Snack, User } from '@/payload-types' // Import Snack and User types

async function getSnack(id: string, payload: any) {
  const snack = await payload.findByID({
    collection: 'snacks',
    id,
    depth: 2,
  }) as Snack | null // Cast snack to Snack | null
  return snack
}

export default async function SnackPage({ params }: { params: { id: string } }) {
  const { id } = params
  const headers = await getHeaders()
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })
  const { user } = await payload.auth({ headers }) as { user: User | null } // Cast user to User | null

  const snack = await getSnack(id, payload)

  if (!snack) {
    return notFound()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="container mx-auto px-4 py-8">
        <div className="grid md:grid-cols-2 gap-8 lg:gap-12">
          <div>
            {(snack.image && typeof snack.image === 'object' && snack.image.url) || snack.imageUrl ? ( // Simplified image access
              <div className="aspect-square relative rounded-lg overflow-hidden border">
                <Image
                  src={
                    (snack.image && typeof snack.image === 'object' && snack.image.url)
                      ? snack.image.url
                      : snack.imageUrl || '' // Ensure src is always a string
                  }
                  alt={
                    (snack.image && typeof snack.image === 'object' ? snack.image.alt : undefined) || snack.name // Simplified image access
                  }
                  fill
                  className="object-cover"
                />
              </div>
            ) : null}
          </div>
          <div className="flex flex-col justify-center">
            <h1 className="text-4xl font-bold text-gray-900">{snack.name}</h1>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="secondary">{snack.category}</Badge>
            </div>
            <p className="text-lg text-gray-700 mt-4">{snack.description}</p>
            <div className="mt-6">
              <span className="text-4xl font-bold text-green-600">${snack.price.toFixed(2)}</span>
            </div>
            <div className="mt-8">
              {user ? (
                <AddToCartButton snack={snack} />
              ) : (
                <Button asChild>
                  <Link href="/login">Login to Order</Link>
                </Button>
              )}
            </div>
            <div className="mt-4">
              <Button asChild variant="link">
                <Link href="/"> &larr; Back to all snacks</Link>
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
