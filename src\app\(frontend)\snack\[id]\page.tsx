import { headers as getHeaders } from 'next/headers.js';
import Image from 'next/image';
import { getPayload } from 'payload';
import React from 'react';
import config from '@/payload.config';
import { notFound } from 'next/navigation';
import { AddToCartButton } from '@/components/add-to-cart-button';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Snack, User } from '@/payload-types'; // Import Snack and User types

async function getSnack(id: string, payload: any) {
  const snack = (await payload.findByID({
    collection: 'snacks',
    id,
    depth: 2,
  })) as Snack | null; // Cast snack to Snack | null
  return snack;
}

export default async function SnackPage({ params }: { params: { id: string } }) {
  const { id } = params;
  const headers = await getHeaders();
  const payloadConfig = await config;
  const payload = await getPayload({ config: payloadConfig });
  const { user } = (await payload.auth({ headers })) as { user: User | null }; // Cast user to User | null

  const snack = await getSnack(id, payload);

  if (!snack) {
    return notFound();
  }

  return (
    <div className="min-h-screen bg-gray-50" data-oid="v.n1jrb">
      <main className="container mx-auto px-4 py-8" data-oid="ck9_i15">
        <div className="grid md:grid-cols-2 gap-8 lg:gap-12" data-oid="qdztg6o">
          <div data-oid="xgk4p_2">
            {(snack.image && typeof snack.image === 'object' && snack.image.url) ||
            snack.imageUrl ? ( // Simplified image access
              <div
                className="aspect-square relative rounded-lg overflow-hidden border"
                data-oid="pgdcnuz"
              >
                <Image
                  src={
                    snack.image && typeof snack.image === 'object' && snack.image.url
                      ? snack.image.url
                      : snack.imageUrl || '' // Ensure src is always a string
                  }
                  alt={
                    (snack.image && typeof snack.image === 'object'
                      ? snack.image.alt
                      : undefined) || snack.name // Simplified image access
                  }
                  fill
                  className="object-cover"
                  data-oid="gzz7hfa"
                />
              </div>
            ) : null}
          </div>
          <div className="flex flex-col justify-center" data-oid="q-woh7d">
            <h1 className="text-4xl font-bold text-gray-900" data-oid="9828_ui">
              {snack.name}
            </h1>
            <div className="flex items-center gap-2 mt-2" data-oid="y7p87ia">
              <Badge variant="secondary" data-oid="7-5dsi.">
                {snack.category}
              </Badge>
            </div>
            <p className="text-lg text-gray-700 mt-4" data-oid=".pybxch">
              {snack.description}
            </p>
            <div className="mt-6" data-oid="99eve8.">
              <span className="text-4xl font-bold text-green-600" data-oid="0j.q-a.">
                ${snack.price.toFixed(2)}
              </span>
            </div>
            <div className="mt-8" data-oid="p-tq-a7">
              {user ? (
                <AddToCartButton snack={snack} data-oid="gn58h8-" />
              ) : (
                <Button asChild data-oid="puxcy.g">
                  <Link href="/login" data-oid="fky1eiz">
                    Login to Order
                  </Link>
                </Button>
              )}
            </div>
            <div className="mt-4" data-oid="3swamie">
              <Button asChild variant="link" data-oid="hpscz7q">
                <Link href="/" data-oid="_ljrsjh">
                  {' '}
                  &larr; Back to all snacks
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
