import { AxiosRequestConfig } from 'axios'
import {
  BaseESIMProvider,
  ESIMPlan,
  ESIMPurchaseRequest,
  ESIMPurchaseResponse,
  PlanQuery,
  ProviderConfig,
} from './base-provider'

// 1GLOBAL specific interfaces
interface OneGlobalConfig extends ProviderConfig {
  apiKey: string
}

interface OneGlobalPlan {
  plan_id: string
  plan_name: string
  destination: string
  destination_code: string
  region_name: string
  data_mb: number
  validity_days: number
  price_usd: number
  operator_name: string
  network_technology: string
  features: string[]
  coverage_countries: string[]
}

interface OneGlobalOrderRequest {
  plan_id: string
  email: string
  first_name?: string
  last_name?: string
  external_reference?: string
}

interface OneGlobalOrderResponse {
  transaction_id: string
  status: string
  esim_profile: {
    qr_code_data: string
    iccid: string
    activation_code: string
  }
  validity: {
    start_date: string
    end_date: string
  }
  activation_instructions: string
  network_configuration: {
    operator: string
    apn_settings: string
    network_type: string
  }
}

export class OneGlobalProvider extends BaseESIMProvider {
  constructor(config: OneGlobalConfig) {
    super(config, '1GLOBAL')
  }

  addAuthHeaders(config: AxiosRequestConfig): AxiosRequestConfig {
    return {
      ...config,
      headers: {
        ...config.headers,
        'X-API-Key': this.config.apiKey,
        'Accept': 'application/json',
      },
    }
  }

  async fetchPlans(query: PlanQuery): Promise<ESIMPlan[]> {
    this.validatePlanQuery(query)

    try {
      const params: any = {}
      
      if (query.country) params.destination = query.country
      if (query.countryCode) params.destination_code = query.countryCode
      if (query.region) params.region = query.region
      if (query.dataAmount) params.min_data_mb = query.dataAmount * 1024 // Convert GB to MB
      if (query.maxPrice) params.max_price_usd = query.maxPrice

      const response = await this.retryRequest(async () => {
        return await this.makeRequest<{ data: OneGlobalPlan[] }>(
          'GET',
          '/plans',
          undefined,
          true
        )
      })

      return response.data.map(plan => this.transformPlan(plan))
    } catch (error) {
      console.error('1GLOBAL fetchPlans error:', error)
      throw error
    }
  }

  async purchasePlan(request: ESIMPurchaseRequest): Promise<ESIMPurchaseResponse> {
    try {
      const orderRequest: OneGlobalOrderRequest = {
        plan_id: request.planId,
        email: request.customerEmail,
        first_name: request.customerName?.split(' ')[0],
        last_name: request.customerName?.split(' ').slice(1).join(' '),
        external_reference: request.metadata?.orderId,
      }

      const response = await this.retryRequest(async () => {
        return await this.makeRequest<OneGlobalOrderResponse>(
          'POST',
          '/orders',
          orderRequest,
          false
        )
      })

      return this.transformOrderResponse(response)
    } catch (error) {
      console.error('1GLOBAL purchasePlan error:', error)
      throw error
    }
  }

  async getOrderStatus(orderId: string): Promise<ESIMPurchaseResponse> {
    try {
      const response = await this.retryRequest(async () => {
        return await this.makeRequest<OneGlobalOrderResponse>(
          'GET',
          `/orders/${orderId}`,
          undefined,
          false
        )
      })

      return this.transformOrderResponse(response)
    } catch (error) {
      console.error('1GLOBAL getOrderStatus error:', error)
      throw error
    }
  }

  // Transform 1GLOBAL plan format to our standard format
  private transformPlan(plan: OneGlobalPlan): ESIMPlan {
    return {
      id: plan.plan_id,
      name: plan.plan_name,
      country: plan.destination,
      countryCode: plan.destination_code,
      region: plan.region_name,
      dataAmount: plan.data_mb / 1024, // Convert MB to GB
      validity: plan.validity_days,
      price: this.formatPrice(plan.price_usd),
      currency: 'USD',
      provider: 'one-global',
      features: plan.features || [],
      coverage: plan.coverage_countries || [plan.destination],
      networkInfo: {
        operator: plan.operator_name,
        networkType: plan.network_technology,
      },
    }
  }

  // Transform 1GLOBAL order response to our standard format
  private transformOrderResponse(response: OneGlobalOrderResponse): ESIMPurchaseResponse {
    return {
      orderId: response.transaction_id,
      activationCode: response.esim_profile.qr_code_data || response.esim_profile.activation_code,
      iccid: response.esim_profile.iccid,
      status: this.mapStatus(response.status),
      expiryDate: response.validity.end_date,
      activationInstructions: response.activation_instructions,
      networkInfo: response.network_configuration ? {
        operator: response.network_configuration.operator,
        apn: response.network_configuration.apn_settings,
        networkType: response.network_configuration.network_type,
      } : undefined,
    }
  }

  // Map 1GLOBAL status to our standard status
  private mapStatus(status: string): 'pending' | 'ready' | 'activated' {
    switch (status.toLowerCase()) {
      case 'pending':
      case 'processing':
      case 'created':
        return 'pending'
      case 'completed':
      case 'ready':
      case 'delivered':
      case 'provisioned':
        return 'ready'
      case 'activated':
      case 'active':
      case 'in_use':
        return 'activated'
      default:
        return 'pending'
    }
  }

  // 1GLOBAL specific health check
  async healthCheck(): Promise<boolean> {
    try {
      // Try to fetch account info as a health check
      await this.makeRequest('GET', '/account/status', undefined, false)
      return true
    } catch (error) {
      console.error('1GLOBAL health check failed:', error)
      return false
    }
  }

  // Get available destinations from 1GLOBAL
  async getAvailableDestinations(): Promise<Array<{ code: string; name: string; region: string }>> {
    try {
      const response = await this.makeRequest<{ 
        destinations: Array<{ 
          destination_code: string
          destination_name: string
          region: string 
        }> 
      }>(
        'GET',
        '/destinations',
        undefined,
        true
      )

      return response.destinations.map(dest => ({
        code: dest.destination_code,
        name: dest.destination_name,
        region: dest.region,
      }))
    } catch (error) {
      console.error('1GLOBAL getAvailableDestinations error:', error)
      return []
    }
  }

  // Get plan details by ID
  async getPlanDetails(planId: string): Promise<ESIMPlan | null> {
    try {
      const response = await this.makeRequest<OneGlobalPlan>(
        'GET',
        `/plans/${planId}`,
        undefined,
        true
      )

      return this.transformPlan(response)
    } catch (error) {
      console.error('1GLOBAL getPlanDetails error:', error)
      return null
    }
  }

  // Get account balance
  async getAccountBalance(): Promise<{ balance: number; currency: string } | null> {
    try {
      const response = await this.makeRequest<{
        balance_usd: number
        currency: string
      }>(
        'GET',
        '/account/balance',
        undefined,
        false
      )

      return {
        balance: response.balance_usd,
        currency: response.currency,
      }
    } catch (error) {
      console.error('1GLOBAL getAccountBalance error:', error)
      return null
    }
  }

  // Get usage data for an activated eSIM
  async getUsageData(iccid: string): Promise<{ used: number; remaining: number; total: number } | null> {
    try {
      const response = await this.makeRequest<{
        data_usage: {
          used_mb: number
          remaining_mb: number
          total_mb: number
        }
      }>(
        'GET',
        `/esim/${iccid}/usage`,
        undefined,
        false
      )

      return {
        used: response.data_usage.used_mb / 1024, // Convert to GB
        remaining: response.data_usage.remaining_mb / 1024,
        total: response.data_usage.total_mb / 1024,
      }
    } catch (error) {
      console.error('1GLOBAL getUsageData error:', error)
      return null
    }
  }

  // Suspend an eSIM
  async suspendESIM(iccid: string): Promise<boolean> {
    try {
      await this.makeRequest(
        'POST',
        `/esim/${iccid}/suspend`,
        undefined,
        false
      )
      return true
    } catch (error) {
      console.error('1GLOBAL suspendESIM error:', error)
      return false
    }
  }

  // Resume a suspended eSIM
  async resumeESIM(iccid: string): Promise<boolean> {
    try {
      await this.makeRequest(
        'POST',
        `/esim/${iccid}/resume`,
        undefined,
        false
      )
      return true
    } catch (error) {
      console.error('1GLOBAL resumeESIM error:', error)
      return false
    }
  }
}

// Factory function to create 1GLOBAL provider instance
export function createOneGlobalProvider(): OneGlobalProvider {
  const config: OneGlobalConfig = {
    apiKey: process.env.ONE_GLOBAL_API_KEY || '',
    apiUrl: process.env.ONE_GLOBAL_API_URL || 'https://sandbox.1global.com/api/v1',
    environment: (process.env.ONE_GLOBAL_ENVIRONMENT as 'sandbox' | 'production') || 'sandbox',
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 1000,
  }

  if (!config.apiKey) {
    throw new Error('ONE_GLOBAL_API_KEY environment variable is required')
  }

  return new OneGlobalProvider(config)
}
