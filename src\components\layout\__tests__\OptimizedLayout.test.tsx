import React from 'react';
import { render } from '@testing-library/react';
import { OptimizedLayout } from '../OptimizedLayout';
import * as performanceOptimization from '@/lib/performance-optimization';
import * as monitoring from '@/lib/monitoring';

// Mock the performance optimization and monitoring modules
jest.mock('@/lib/performance-optimization', () => ({
  usePrefetchResources: jest.fn(),
}));

jest.mock('@/lib/monitoring', () => ({
  usePageViewTracking: jest.fn(),
}));

// Mock window.performance
Object.defineProperty(window, 'performance', {
  value: {
    mark: jest.fn(),
    measure: jest.fn(),
    getEntriesByType: jest.fn().mockReturnValue([]),
    getEntriesByName: jest.fn().mockReturnValue([]),
    clearMarks: jest.fn(),
    clearMeasures: jest.fn(),
  },
  writable: true,
});

describe('OptimizedLayout', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders children correctly', () => {
    const { getByText } = render(
      <OptimizedLayout data-oid="x5cukuj">
        <div data-oid="yl6j2z:">Test Content</div>
      </OptimizedLayout>
    );

    expect(getByText('Test Content')).toBeInTheDocument();
  });

  it('calls usePrefetchResources on mount', () => {
    render(
      <OptimizedLayout data-oid="gq1nzd7">
        <div data-oid="wrglzgn">Test Content</div>
      </OptimizedLayout>
    );

    expect(performanceOptimization.usePrefetchResources).toHaveBeenCalled();
  });

  it('calls usePageViewTracking on mount', () => {
    render(
      <OptimizedLayout data-oid="r1kvsp4">
        <div data-oid="42e8whp">Test Content</div>
      </OptimizedLayout>
    );

    expect(monitoring.usePageViewTracking).toHaveBeenCalled();
  });

  it('adds performance marks for layout rendering', () => {
    render(
      <OptimizedLayout data-oid="5.yd_oe">
        <div data-oid="xjiw4--">Test Content</div>
      </OptimizedLayout>
    );

    expect(window.performance.mark).toHaveBeenCalledWith('layout-render-start');
    expect(window.performance.mark).toHaveBeenCalledWith('layout-render-end');
    expect(window.performance.measure).toHaveBeenCalledWith(
      'layout-render-duration',
      'layout-render-start',
      'layout-render-end'
    );
  });

  it('applies custom className when provided', () => {
    const { container } = render(
      <OptimizedLayout className="custom-class" data-oid="lod4l9.">
        <div data-oid="o-3o5yz">Test Content</div>
      </OptimizedLayout>
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });
});
