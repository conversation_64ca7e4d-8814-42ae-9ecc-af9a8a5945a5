/**
 * Home Page Client Component
 *
 * Enhanced with modern dark hero section featuring:
 * - Animated grain filter and vignette effects
 * - Sequential signal bars animation
 * - Green color scheme throughout
 */
'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { motion, useReducedMotion } from 'framer-motion'
import { ArrowRightIcon, GlobeAltIcon, DevicePhoneMobileIcon, BoltIcon } from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/button'
import { MainLayout } from '@/components/layout/MainLayout'
import { InlineSignalBars } from '@/components/hero/InlineSignalBars'
import { GlobalCoverage } from '@/components/hero/GlobalCoverage'
import { FloatingElements } from '@/components/hero/FloatingElements'

export function HomePageClient() {
  const router = useRouter()
  const shouldReduceMotion = useReducedMotion()
  const [isVisible, setIsVisible] = useState(false)

  // Initialize animation visibility
  useEffect(() => {
    setIsVisible(true)
  }, [])

  // Animation variants for smooth entrance effects
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: shouldReduceMotion ? 0 : 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: shouldReduceMotion ? 0 : 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.4, 0, 0.2, 1] as any,
      },
    },
  }

  // Key benefits displayed below the headline
  const benefits = [
    {
      icon: BoltIcon,
      text: "Instant Activation",
    },
    {
      icon: GlobeAltIcon,
      text: "Global Coverage",
    },
    {
      icon: DevicePhoneMobileIcon,
      text: "No Physical SIM",
    },
  ]

  // Event handlers for CTA buttons
  const handleSearchPlans = () => {
    router.push('/search')
  }

  const handleHowItWorks = () => {
    router.push('/how-it-works')
  }

  return (
    <MainLayout>
      <div className="min-h-screen">
      <section className="relative min-h-screen bg-white overflow-hidden">
        {/* Enhanced Animated Background with Grain and Vignette */}
        <div className="absolute inset-0">
          <FloatingElements />
          <div className="absolute inset-0 bg-gradient-to-r from-green-500/8 via-green-400/4 to-green-500/8" />
        </div>

        {/* Main Content Container */}
        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <motion.div
            className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center min-h-[80vh]"
            variants={containerVariants}
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
          >
            {/* Left Content - Main Text and CTAs */}
            <div className="text-center lg:text-left">
              {/* Badge */}
              <motion.div variants={itemVariants} className="mb-6">
                <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-green-100 text-green-800 border border-green-200 shadow-sm">
                  <BoltIcon className="w-4 h-4 mr-2" />
                  Instant eSIM Activation
                </span>
              </motion.div>

              {/* Main Headline with Animated Signal Bars */}
              <motion.h1
                variants={itemVariants}
                className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight"
              >
                Find the Perfect eSIM for Your{' '}
                <span className="bg-gradient-to-r from-green-600 to-green-500 bg-clip-text text-transparent">
                  Journey
                </span>
                {/* Animated Signal Bars - Sequential filling animation */}
                <InlineSignalBars />
              </motion.h1>

              <motion.p
                variants={itemVariants}
                className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto lg:mx-0"
              >
                Get connected instantly with our global eSIM plans. No physical SIM cards, no roaming fees, no hassle.
              </motion.p>

              {/* Benefits */}
              <motion.div
                variants={itemVariants}
                className="flex flex-wrap justify-center lg:justify-start gap-6 mb-10"
              >
                {benefits.map((benefit, index) => (
                  <div
                    key={index}
                    className="flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200"
                  >
                    <benefit.icon className="w-5 h-5 mr-2 text-green-600" />
                    <span className="text-sm font-medium">{benefit.text}</span>
                  </div>
                ))}
              </motion.div>

              {/* CTA Buttons */}
              <motion.div
                variants={itemVariants}
                className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
              >
                <Button
                  onClick={handleSearchPlans}
                  size="lg"
                  className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 group"
                >
                  Search Plans
                  <ArrowRightIcon className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
                </Button>

                <Button
                  onClick={handleHowItWorks}
                  variant="outline"
                  size="lg"
                  className="border-gray-300 text-gray-700 hover:text-gray-900 hover:border-gray-400 hover:bg-gray-50 font-semibold px-8 py-4 rounded-xl transition-all duration-200"
                >
                  How It Works
                </Button>
              </motion.div>

              {/* Trust Signals */}
              <motion.div
                variants={itemVariants}
                className="mt-12 pt-8 border-t border-gray-200"
              >
                <p className="text-sm text-gray-500 mb-4">Trusted by travelers worldwide</p>
                <div className="flex flex-wrap justify-center lg:justify-start gap-6 text-sm text-gray-600">
                  <span className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                    Secure & Encrypted
                  </span>
                  <span className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                    Instant Activation
                  </span>
                  <span className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                    24/7 Support
                  </span>
                </div>
              </motion.div>
            </div>

            {/* Right Visual Content - Stats and Coverage */}
            <motion.div
              variants={itemVariants}
              className="relative flex items-center justify-center"
            >
              <div className="relative w-full max-w-2xl mx-auto">
                {/* Enhanced Global Coverage Visualization - Larger and without container */}
                <div className="mb-8">
                  <GlobalCoverage />
                </div>

                {/* Stats Container */}
                <div className="relative bg-gradient-to-br from-gray-900/80 to-black/80 backdrop-blur-sm rounded-3xl p-6 border border-gray-800/50 shadow-2xl">

                  {/* Key Statistics */}
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="bg-gray-900/50 rounded-xl p-4 border border-gray-800/30 hover:border-green-500/30 transition-colors duration-300">
                      <div className="text-2xl font-bold text-green-400">200+</div>
                      <div className="text-xs text-gray-400">Countries</div>
                    </div>
                    <div className="bg-gray-900/50 rounded-xl p-4 border border-gray-800/30 hover:border-green-500/30 transition-colors duration-300">
                      <div className="text-2xl font-bold text-green-400">3</div>
                      <div className="text-xs text-gray-400">Providers</div>
                    </div>
                    <div className="bg-gray-900/50 rounded-xl p-4 border border-gray-800/30 hover:border-green-500/30 transition-colors duration-300">
                      <div className="text-2xl font-bold text-green-400">24/7</div>
                      <div className="text-xs text-gray-400">Support</div>
                    </div>
                  </div>
                </div>

                {/* Decorative Floating Elements */}
                <div className="absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-br from-green-500/20 to-green-400/20 rounded-full blur-xl animate-pulse" />
                <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-br from-green-400/20 to-green-500/20 rounded-full blur-xl animate-pulse" style={{ animationDelay: '1s' }} />
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Bottom Gradient for smooth transition from white to dark */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-900 via-gray-700/60 to-transparent" />
      </section>

      {/* Features Section */}
      <section className="relative py-24 bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6">
              Why Choose Our{' '}
              <span className="bg-gradient-to-r from-green-400 to-green-300 bg-clip-text text-transparent">
                eSIM Service?
              </span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Experience the future of mobile connectivity with instant activation, global coverage, and unmatched reliability.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
            {[
              {
                icon: '⚡',
                title: 'Instant Activation',
                description: 'Get connected in seconds with QR code activation. No waiting, no physical SIM cards needed.',
                color: 'from-green-500 to-green-400'
              },
              {
                icon: '🌍',
                title: 'Global Coverage',
                description: 'Stay connected in 200+ countries worldwide with our extensive network of trusted providers.',
                color: 'from-green-400 to-green-300'
              },
              {
                icon: '🔒',
                title: 'Secure & Reliable',
                description: 'Bank-level security with 99.9% uptime guarantee. Your data and connection are always protected.',
                color: 'from-green-600 to-green-500'
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                className="relative group"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
              >
                <div className="relative bg-gradient-to-br from-gray-900/80 to-black/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-800/50 hover:border-green-500/30 transition-all duration-300 group-hover:transform group-hover:scale-105">
                  {/* Feature Icon */}
                  <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center text-2xl mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    {feature.icon}
                  </div>

                  <h3 className="text-xl font-bold text-white mb-4 group-hover:text-green-300 transition-colors duration-300">
                    {feature.title}
                  </h3>

                  <p className="text-gray-300 leading-relaxed">
                    {feature.description}
                  </p>

                  {/* Hover glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 to-green-400/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="relative py-24 bg-gradient-to-b from-gray-800 via-gray-900 to-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6">
              How It{' '}
              <span className="bg-gradient-to-r from-green-400 to-green-300 bg-clip-text text-transparent">
                Works
              </span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Get connected in three simple steps. It's that easy.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
            {[
              {
                step: '01',
                title: 'Choose Your Plan',
                description: 'Browse our extensive catalog of eSIM plans from trusted global providers. Filter by country, data allowance, and duration.',
                icon: '📱'
              },
              {
                step: '02',
                title: 'Instant Purchase',
                description: 'Complete your secure purchase in seconds. Receive your eSIM QR code immediately via email.',
                icon: '💳'
              },
              {
                step: '03',
                title: 'Scan & Connect',
                description: 'Simply scan the QR code with your device camera. Your eSIM activates instantly and you\'re connected.',
                icon: '📶'
              }
            ].map((step, index) => (
              <motion.div
                key={index}
                className="relative text-center"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
              >
                {/* Step Number */}
                <div className="relative mb-8">
                  <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-green-400 rounded-full flex items-center justify-center text-2xl font-bold text-white mx-auto mb-4 shadow-lg">
                    {step.icon}
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-400 rounded-full flex items-center justify-center text-sm font-bold text-black">
                    {step.step}
                  </div>
                </div>

                <h3 className="text-xl font-bold text-white mb-4">
                  {step.title}
                </h3>

                <p className="text-gray-300 leading-relaxed">
                  {step.description}
                </p>

                {/* Connection line (except for last item) */}
                {index < 2 && (
                  <div className="hidden md:block absolute top-10 left-full w-full h-0.5 bg-gradient-to-r from-green-500 to-green-400 opacity-30" />
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-24 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6">
              Ready to Get{' '}
              <span className="bg-gradient-to-r from-green-400 to-green-300 bg-clip-text text-transparent">
                Connected?
              </span>
            </h2>
            <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
              Join thousands of travelers who trust our eSIM service for reliable global connectivity.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button
                onClick={handleSearchPlans}
                size="lg"
                className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold px-12 py-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 group text-lg"
              >
                Browse eSIM Plans
                <ArrowRightIcon className="w-6 h-6 ml-3 group-hover:translate-x-1 transition-transform duration-200" />
              </Button>

              <Button
                onClick={handleHowItWorks}
                variant="outline"
                size="lg"
                className="border-gray-600 text-gray-300 hover:text-white hover:border-gray-500 font-semibold px-12 py-6 rounded-xl transition-all duration-200 text-lg"
              >
                Learn More
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
      </div>
    </MainLayout>
  )
}
