import { detectAIContent, isLikelyAIGenerated, getDetectionExplanation } from '../ai-detection';

describe('AI Detection Utilities', () => {
  describe('detectAIContent', () => {
    it('should return a detection result with the expected properties', async () => {
      const testContent = 'This is a test content to analyze for AI detection.';
      const result = await detectAIContent(testContent);
      
      expect(result).toHaveProperty('score');
      expect(result).toHaveProperty('isAIGenerated');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('explanation');
      expect(typeof result.score).toBe('number');
      expect(typeof result.isAIGenerated).toBe('boolean');
      expect(typeof result.confidence).toBe('string');
      expect(typeof result.explanation).toBe('string');
    });

    it('should handle empty content gracefully', async () => {
      const result = await detectAIContent('');
      
      expect(result.score).toBe(0);
      expect(result.isAIGenerated).toBe(false);
      expect(result.confidence).toBe('high');
      expect(result.explanation).toContain('empty');
    });

    it('should detect highly structured content as potentially AI-generated', async () => {
      const aiLikeContent = 'Here are 5 key points to consider:\n1. First point with detailed explanation\n2. Second point with methodical analysis\n3. Third point with logical progression\n4. Fourth point with systematic approach\n5. Fifth point with comprehensive conclusion';
      
      const result = await detectAIContent(aiLikeContent);
      
      expect(result.score).toBeGreaterThan(0.5);
    });
  });

  describe('isLikelyAIGenerated', () => {
    it('should return true for high scores', () => {
      expect(isLikelyAIGenerated(0.8)).toBe(true);
      expect(isLikelyAIGenerated(0.7)).toBe(true);
    });

    it('should return false for low scores', () => {
      expect(isLikelyAIGenerated(0.3)).toBe(false);
      expect(isLikelyAIGenerated(0.1)).toBe(false);
    });

    it('should handle edge cases', () => {
      expect(isLikelyAIGenerated(0.6)).toBe(true); // Default threshold is 0.6
      expect(isLikelyAIGenerated(0.59)).toBe(false);
      expect(isLikelyAIGenerated(0)).toBe(false);
      expect(isLikelyAIGenerated(1)).toBe(true);
    });
  });

  describe('getDetectionExplanation', () => {
    it('should provide appropriate explanation for high scores', () => {
      const explanation = getDetectionExplanation(0.9);
      expect(explanation).toContain('high');
      expect(explanation).toContain('AI');
    });

    it('should provide appropriate explanation for medium scores', () => {
      const explanation = getDetectionExplanation(0.5);
      expect(explanation).toContain('moderate');
      expect(explanation).toContain('mix');
    });

    it('should provide appropriate explanation for low scores', () => {
      const explanation = getDetectionExplanation(0.2);
      expect(explanation).toContain('low');
      expect(explanation).toContain('human');
    });
  });
});