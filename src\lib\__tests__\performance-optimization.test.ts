import { optimizeImageProps, getPrefetchResources } from '../performance-optimization';

describe('Performance Optimization Utilities', () => {
  describe('optimizeImageProps', () => {
    it('should add loading="lazy" by default', () => {
      const props = optimizeImageProps({
        src: '/test-image.jpg',
        alt: 'Test Image',
        width: 100,
        height: 100,
      });

      expect(props.loading).toBe('lazy');
    });

    it('should not override existing loading prop', () => {
      const props = optimizeImageProps({
        src: '/test-image.jpg',
        alt: 'Test Image',
        width: 100,
        height: 100,
        loading: 'eager',
      });

      expect(props.loading).toBe('eager');
    });

    it('should add placeholder="blur" and blurDataURL for local images', () => {
      const props = optimizeImageProps({
        src: '/test-image.jpg',
        alt: 'Test Image',
        width: 100,
        height: 100,
        generateBlurPlaceholder: true,
      });

      expect(props.placeholder).toBe('blur');
      expect(props.blurDataURL).toBeDefined();
    });

    it('should not add blur placeholder when generateBlurPlaceholder is false', () => {
      const props = optimizeImageProps({
        src: '/test-image.jpg',
        alt: 'Test Image',
        width: 100,
        height: 100,
        generateBlurPlaceholder: false,
      });

      expect(props.placeholder).toBeUndefined();
      expect(props.blurDataURL).toBeUndefined();
    });

    it('should add priority for above-the-fold images', () => {
      const props = optimizeImageProps({
        src: '/test-image.jpg',
        alt: 'Test Image',
        width: 100,
        height: 100,
        priority: true,
      });

      expect(props.priority).toBe(true);
      expect(props.loading).toBeUndefined(); // loading is not used with priority
    });
  });

  describe('getPrefetchResources', () => {
    it('should return configured prefetch resources', () => {
      const resources = getPrefetchResources();
      
      expect(Array.isArray(resources)).toBe(true);
      // Check that each resource has the expected structure
      resources.forEach(resource => {
        expect(resource).toHaveProperty('path');
        expect(typeof resource.path).toBe('string');
        
        if (resource.type) {
          expect(['style', 'script', 'font', 'image']).toContain(resource.type);
        }
      });
    });
  });
});