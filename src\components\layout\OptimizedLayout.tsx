'use client';

import React, { useEffect } from 'react';
import { usePrefetchResources } from '@/lib/performance-optimization';
import { usePageViewTracking } from '@/lib/monitoring';

interface OptimizedLayoutProps {
  children: React.ReactNode;
}

/**
 * A layout component that implements performance optimizations and monitoring
 * for all pages that use it.
 */
export function OptimizedLayout({ children }: OptimizedLayoutProps) {
  // Prefetch critical resources
  usePrefetchResources();

  // Track page views
  usePageViewTracking();

  // Add performance mark for component mount
  useEffect(() => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      // Mark the layout render time
      performance.mark('layout-rendered');

      // Measure time from navigation to layout render
      if (performance.getEntriesByName('navigation-start').length > 0) {
        performance.measure('navigation-to-layout', 'navigation-start', 'layout-rendered');
      }
    }

    return () => {
      // Clean up performance marks when component unmounts
      if (typeof window !== 'undefined' && 'performance' in window) {
        performance.clearMarks('layout-rendered');
        performance.clearMeasures('navigation-to-layout');
      }
    };
  }, []);

  return (
    <div className="optimized-layout" data-oid="zqbj-eo">
      {/* This div could include global UI elements like headers, footers, etc. */}
      {children}
    </div>
  );
}
