import { SignIn } from '@clerk/nextjs'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Sign In - GGsim',
  description: 'Sign in to access your GGsim account and manage your eSIM plans.',
}

export default function SignInPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome to GGsim Beta
          </h1>
          <p className="text-gray-600 mb-8">
            Sign in to access the beta testing platform
          </p>
        </div>
        
        <div className="bg-white p-8 rounded-lg shadow-lg">
          <SignIn 
            appearance={{
              elements: {
                formButtonPrimary: 'bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md transition-colors',
                card: 'shadow-none border-0',
                headerTitle: 'text-2xl font-semibold text-gray-900',
                headerSubtitle: 'text-gray-600',
                socialButtonsBlockButton: 'border border-gray-300 hover:bg-gray-50',
                formFieldInput: 'border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                footerActionLink: 'text-blue-500 hover:text-blue-600',
              },
            }}
            redirectUrl="/dashboard"
            signUpUrl="/sign-up"
          />
        </div>
        
        <div className="text-center">
          <p className="text-sm text-gray-500">
            Beta access is limited to approved email addresses only.
          </p>
        </div>
      </div>
    </div>
  )
}
