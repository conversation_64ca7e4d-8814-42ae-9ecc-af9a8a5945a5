import { headers as getHeaders } from 'next/headers.js';
import Image from 'next/image';
import { getPayload } from 'payload';
import React from 'react';
import Link from 'next/link';
import { redirect } from 'next/navigation';

import config from '@/payload.config';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { User, Order } from '@/payload-types'; // Import User and Order types

export default async function MyOrdersPage({
  searchParams,
}: {
  searchParams: { success?: string };
}) {
  const { success } = searchParams;
  const headers = await getHeaders();
  const payloadConfig = await config;
  const payload = await getPayload({ config: payloadConfig });
  const { user } = (await payload.auth({ headers })) as { user: User | null }; // Cast user to User | null

  // Redirect to login if not authenticated
  if (!user) {
    redirect('/login');
  }

  // Fetch user's orders
  const orders = await payload.find({
    collection: 'orders',
    where: {
      user: {
        equals: user.id,
      },
    },
    depth: 3,
    sort: '-orderDate',
  });

  return (
    <div className="min-h-screen bg-gray-50" data-oid="0zf1cek">
      <div className="container mx-auto px-4 py-8" data-oid="m54r91t">
        <Button asChild variant="ghost" className="mb-6" data-oid="l0zd-9r">
          <Link href="/" data-oid=":b60xtr">
            ← Back to Home
          </Link>
        </Button>

        <h1 className="text-3xl font-bold text-gray-900 mb-8" data-oid="8unhwrx">
          My Orders
        </h1>

        {success && (
          <Alert className="mb-6" data-oid="aogj.4x">
            <AlertDescription data-oid="-tr6agq">Order placed successfully! 🎉</AlertDescription>
          </Alert>
        )}

        {orders.docs.length === 0 ? (
          <Card data-oid="swwcs_r">
            <CardContent className="py-12 text-center" data-oid="g2ff86v">
              <p className="text-gray-500 mb-6" data-oid="d14vih8">
                You haven&apos;t placed any orders yet.
              </p>
              <Button asChild data-oid="d4v8h49">
                <Link href="/" data-oid="8277sfs">
                  Browse Snacks
                </Link>
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6" data-oid="ikbg0o5">
            {orders.docs.map((order: Order) => (
              <Card key={order.id} className="overflow-hidden" data-oid="bxnm-o4">
                <CardHeader data-oid="ht8ymiu">
                  <div className="flex items-center justify-between" data-oid="6i-2gns">
                    <CardTitle className="text-lg" data-oid="b19-vaa">
                      Order #{String(order.id).slice(-8)}
                    </CardTitle>
                    <Badge
                      variant={
                        order.status === 'pending'
                          ? 'secondary'
                          : order.status === 'completed'
                            ? 'default'
                            : 'destructive'
                      }
                      data-oid="g7jrh1b"
                    >
                      {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                    </Badge>
                  </div>
                  <CardDescription data-oid="3jmc5sk">
                    Ordered: {new Date(order.orderDate).toLocaleDateString()}
                  </CardDescription>
                </CardHeader>

                <CardContent data-oid=":47a:i5">
                  <div className="space-y-4" data-oid="w_u8:ss">
                    {order.items.map((item, index: number) => (
                      <div
                        key={index}
                        className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg"
                        data-oid="441um.u"
                      >
                        {item.snack &&
                          typeof item.snack === 'object' &&
                          typeof item.snack.image === 'object' &&
                          item.snack.image?.url && ( // Simplified image access
                            <div
                              className="relative w-16 h-16 rounded overflow-hidden"
                              data-oid="tu9j9bo"
                            >
                              <Image
                                src={item.snack.image.url}
                                alt={item.snack.image.alt || item.snack.name}
                                fill
                                className="object-cover"
                                data-oid="2v2wwp-"
                              />
                            </div>
                          )}
                        <div className="flex-1" data-oid="l8f187d">
                          <h4 className="font-medium" data-oid="df504fd">
                            {typeof item.snack === 'object' ? item.snack.name : 'Unknown Item'}
                          </h4>
                          <p className="text-sm text-gray-600" data-oid="orm6xxf">
                            Quantity: {item.quantity}
                          </p>
                          <p className="text-sm text-gray-600" data-oid=":74_d9u">
                            Price: $
                            {(
                              (typeof item.snack === 'object' ? item.snack.price : 0) *
                              item.quantity
                            ).toFixed(2)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>

                  <Separator className="my-4" data-oid="lb08fib" />

                  <div className="text-right" data-oid="hhbaf4d">
                    <span className="text-lg font-bold" data-oid="cr3dra5">
                      Total: ${order.totalAmount.toFixed(2)}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
