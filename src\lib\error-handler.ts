/**
 * Global error handler for API routes
 * Provides consistent error responses and logging
 */

import { NextRequest, NextResponse } from 'next/server';
import { logger } from './logger';

export interface ApiError extends Error {
  statusCode?: number;
  code?: string;
  details?: Record<string, any>;
}

/**
 * Create a standardized API error
 */
export function createApiError({
  message,
  statusCode = 500,
  code = 'INTERNAL_SERVER_ERROR',
  details,
}: {
  message: string;
  statusCode?: number;
  code?: string;
  details?: Record<string, any>;
}): ApiError {
  const error = new Error(message) as ApiError;
  error.statusCode = statusCode;
  error.code = code;
  error.details = details;
  return error;
}

/**
 * Handle API errors consistently
 */
export function handleApiError(error: unknown, req: NextRequest) {
  const apiError = error as ApiError;
  const statusCode = apiError.statusCode || 500;
  const errorCode = apiError.code || 'INTERNAL_SERVER_ERROR';
  const message = apiError.message || 'An unexpected error occurred';
  
  // Log the error with request details
  logger.error(apiError, {
    context: 'API',
    data: {
      url: req.url,
      method: req.method,
      ...(apiError.details || {}),
    },
  });

  // Return a standardized error response
  return NextResponse.json(
    {
      error: {
        code: errorCode,
        message: statusCode === 500 ? 'An unexpected error occurred' : message,
        ...(process.env.NODE_ENV !== 'production' && apiError.details ? { details: apiError.details } : {}),
      },
    },
    { status: statusCode }
  );
}

/**
 * Wrap an API handler with error handling
 */
export function withErrorHandling(
  handler: (req: NextRequest) => Promise<Response> | Response
) {
  return async (req: NextRequest) => {
    try {
      return await handler(req);
    } catch (error) {
      return handleApiError(error, req);
    }
  };
}