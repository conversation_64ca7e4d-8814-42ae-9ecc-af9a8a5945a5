'use client';

import { useState, useEffect } from 'react';
import { registerMCPServer, executeMCPTool, getMCPServers } from '@/lib/ai/mcp-server';

type MCPServerIntegrationProps = {
  className?: string;
};

type ServerStatus = {
  server_name: string;
  status: 'connected' | 'disconnected' | 'error';
  error?: string;
};

export default function MCPServerIntegration({ className = '' }: MCPServerIntegrationProps) {
  const [serverUrl, setServerUrl] = useState('');
  const [serverName, setServerName] = useState('');
  const [connecting, setConnecting] = useState(false);
  const [servers, setServers] = useState<ServerStatus[]>([]);
  const [selectedServer, setSelectedServer] = useState<string | null>(null);
  const [selectedTool, setSelectedTool] = useState<string | null>(null);
  const [toolArgs, setToolArgs] = useState('');
  const [executionResult, setExecutionResult] = useState<any>(null);
  const [executing, setExecuting] = useState(false);

  // Fetch registered servers on component mount
  useEffect(() => {
    const fetchServers = async () => {
      try {
        const mcpServers = await getMCPServers();
        setServers(
          mcpServers.map(server => ({
            server_name: server.server_name,
            status: 'connected',
          }))
        );
      } catch (error) {
        console.error('Failed to fetch MCP servers:', error);
      }
    };

    fetchServers();
  }, []);

  const connectServer = async () => {
    if (!serverUrl.trim() || !serverName.trim()) {
      return;
    }

    setConnecting(true);

    try {
      // In a real implementation, this would fetch server details from the URL
      // For now, we'll create a mock server
      const mockServer = {
        server_name: serverName,
        description: `MCP Server at ${serverUrl}`,
        tools: [
          {
            name: 'echo',
            description: 'Echoes back the input',
            parameters: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  description: 'Message to echo',
                },
              },
              required: ['message'],
            },
          },
          {
            name: 'analyze',
            description: 'Analyzes text content',
            parameters: {
              type: 'object',
              properties: {
                content: {
                  type: 'string',
                  description: 'Content to analyze',
                },
                depth: {
                  type: 'string',
                  enum: ['basic', 'advanced'],
                  description: 'Analysis depth',
                },
              },
              required: ['content'],
            },
          },
        ],
      };

      await registerMCPServer(mockServer);

      // Update servers list
      setServers(prev => [
        ...prev,
        {
          server_name: serverName,
          status: 'connected',
        },
      ]);

      // Reset form
      setServerUrl('');
      setServerName('');
    } catch (error) {
      console.error('Failed to connect MCP server:', error);
      setServers(prev => [
        ...prev,
        {
          server_name: serverName,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      ]);
    } finally {
      setConnecting(false);
    }
  };

  const executeTool = async () => {
    if (!selectedServer || !selectedTool || !toolArgs.trim()) {
      return;
    }

    setExecuting(true);
    setExecutionResult(null);

    try {
      // Parse tool arguments
      const args = JSON.parse(toolArgs);

      // Execute the tool
      const result = await executeMCPTool(selectedServer, selectedTool, args);
      setExecutionResult(result);
    } catch (error) {
      console.error('Failed to execute MCP tool:', error);
      setExecutionResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    } finally {
      setExecuting(false);
    }
  };

  return (
    <div className={`rounded-lg border p-4 ${className}`} data-oid="awo1cnz">
      <h2 className="text-xl font-semibold mb-4" data-oid="1uoof_h">
        MCP Server Integration
      </h2>

      {/* Connect Server Form */}
      <div className="mb-6 p-4 border rounded-md" data-oid="5o16av_">
        <h3 className="text-lg font-medium mb-3" data-oid="afayh9o">
          Connect MCP Server
        </h3>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2" data-oid="pnx7_57">
          <div data-oid="40p661s">
            <label
              htmlFor="server-url"
              className="block text-sm font-medium mb-1"
              data-oid="o50i38x"
            >
              Server URL
            </label>
            <input
              id="server-url"
              type="text"
              className="w-full p-2 border rounded-md"
              value={serverUrl}
              onChange={e => setServerUrl(e.target.value)}
              placeholder="https://example.com/mcp"
              data-oid="b6wh7ow"
            />
          </div>

          <div data-oid="hrs7oyx">
            <label
              htmlFor="server-name"
              className="block text-sm font-medium mb-1"
              data-oid="yha6zza"
            >
              Server Name
            </label>
            <input
              id="server-name"
              type="text"
              className="w-full p-2 border rounded-md"
              value={serverName}
              onChange={e => setServerName(e.target.value)}
              placeholder="my-mcp-server"
              data-oid="go5scxg"
            />
          </div>
        </div>

        <button
          onClick={connectServer}
          disabled={connecting || !serverUrl.trim() || !serverName.trim()}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
          data-oid="ug73yn6"
        >
          {connecting ? 'Connecting...' : 'Connect Server'}
        </button>
      </div>

      {/* Registered Servers */}
      <div className="mb-6" data-oid="zpnfmd1">
        <h3 className="text-lg font-medium mb-3" data-oid="ci.4dyr">
          Registered Servers
        </h3>

        {servers.length === 0 ? (
          <p className="text-gray-500" data-oid="u3q15wb">
            No MCP servers registered
          </p>
        ) : (
          <ul className="divide-y" data-oid="1ww42rw">
            {servers.map(server => (
              <li
                key={server.server_name}
                className="py-3 flex items-center justify-between"
                data-oid="s6t1q-e"
              >
                <div data-oid="bwh171-">
                  <span className="font-medium" data-oid="dcox-4x">
                    {server.server_name}
                  </span>
                  <span
                    className={`ml-2 inline-block w-3 h-3 rounded-full ${server.status === 'connected' ? 'bg-green-500' : 'bg-red-500'}`}
                    data-oid="fjyeh7x"
                  ></span>
                </div>
                <button
                  onClick={() => setSelectedServer(server.server_name)}
                  className="px-3 py-1 text-sm bg-gray-200 rounded-md hover:bg-gray-300"
                  data-oid="x1j9dyh"
                >
                  Select
                </button>
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* Execute Tool */}
      {selectedServer && (
        <div className="mb-6 p-4 border rounded-md" data-oid="8fiaqvk">
          <h3 className="text-lg font-medium mb-3" data-oid="0cxx6v7">
            Execute Tool on {selectedServer}
          </h3>

          <div className="mb-4" data-oid="8ilt9p3">
            <label
              htmlFor="tool-select"
              className="block text-sm font-medium mb-1"
              data-oid="a9u_6rh"
            >
              Select Tool
            </label>
            <select
              id="tool-select"
              className="w-full p-2 border rounded-md"
              value={selectedTool || ''}
              onChange={e => setSelectedTool(e.target.value || null)}
              data-oid="qrb46uy"
            >
              <option value="" data-oid="efx0pjv">
                Select a tool...
              </option>
              <option value="echo" data-oid="x9n2apx">
                echo
              </option>
              <option value="analyze" data-oid="d0p24e0">
                analyze
              </option>
            </select>
          </div>

          <div className="mb-4" data-oid="2tdcsj8">
            <label
              htmlFor="tool-args"
              className="block text-sm font-medium mb-1"
              data-oid="gqu2o.v"
            >
              Tool Arguments (JSON)
            </label>
            <textarea
              id="tool-args"
              rows={4}
              className="w-full p-2 border rounded-md font-mono text-sm"
              value={toolArgs}
              onChange={e => setToolArgs(e.target.value)}
              placeholder={'{"message": "Hello, MCP!"}'}
              data-oid="905el08"
            />
          </div>

          <button
            onClick={executeTool}
            disabled={executing || !selectedTool || !toolArgs.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
            data-oid="11fmxx7"
          >
            {executing ? 'Executing...' : 'Execute Tool'}
          </button>
        </div>
      )}

      {/* Execution Result */}
      {executionResult && (
        <div className="p-4 border rounded-md" data-oid="a.buo7a">
          <h3 className="text-lg font-medium mb-2" data-oid="ijvqfsw">
            Execution Result
          </h3>

          <div className="bg-gray-100 p-3 rounded-md" data-oid="s4zy76-">
            <pre className="whitespace-pre-wrap font-mono text-sm" data-oid="p-:b83w">
              {JSON.stringify(executionResult, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}
