'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageCircle, X, Send, Star, Bug, Lightbulb, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useUser } from '@clerk/nextjs';
import { useFeatureFlag } from '@/lib/feature-flags';

interface FeedbackData {
  type: 'bug' | 'feature' | 'general' | 'love';
  rating: number;
  message: string;
  page: string;
  userAgent: string;
  timestamp: string;
  userId?: string;
  userEmail?: string;
}

export function FeedbackWidget() {
  const { isEnabled: feedbackEnabled } = useFeatureFlag('enable-feedback-widget');
  const { user } = useUser();
  const [isOpen, setIsOpen] = useState(false);
  const [step, setStep] = useState<'type' | 'rating' | 'message' | 'success'>('type');
  const [feedbackType, setFeedbackType] = useState<FeedbackData['type']>('general');
  const [rating, setRating] = useState(0);
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (!feedbackEnabled) return null;

  const feedbackTypes = [
    { type: 'bug' as const, icon: Bug, label: 'Report Bug', color: 'text-red-500' },
    {
      type: 'feature' as const,
      icon: Lightbulb,
      label: 'Feature Request',
      color: 'text-yellow-500',
    },
    {
      type: 'general' as const,
      icon: MessageCircle,
      label: 'General Feedback',
      color: 'text-blue-500',
    },
    { type: 'love' as const, icon: Heart, label: 'I Love This!', color: 'text-pink-500' },
  ];

  const handleSubmit = async () => {
    if (!message.trim()) return;

    setIsSubmitting(true);

    const feedbackData: FeedbackData = {
      type: feedbackType,
      rating,
      message: message.trim(),
      page: window.location.pathname,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      userId: user?.id,
      userEmail: user?.emailAddresses[0]?.emailAddress,
    };

    try {
      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(feedbackData),
      });

      if (response.ok) {
        setStep('success');
        // Reset form after 3 seconds
        setTimeout(() => {
          setIsOpen(false);
          setStep('type');
          setRating(0);
          setMessage('');
          setFeedbackType('general');
        }, 3000);
      } else {
        throw new Error('Failed to submit feedback');
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      alert('Failed to submit feedback. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (step) {
      case 'type':
        return (
          <div className="space-y-4" data-oid="rqy_n61">
            <h3 className="text-lg font-semibold text-gray-900" data-oid="v0g0jk8">
              What type of feedback do you have?
            </h3>
            <div className="grid grid-cols-2 gap-3" data-oid="7wlk_bd">
              {feedbackTypes.map(({ type, icon: Icon, label, color }) => (
                <button
                  key={type}
                  onClick={() => {
                    setFeedbackType(type);
                    setStep(type === 'love' ? 'message' : 'rating');
                  }}
                  className="flex flex-col items-center p-4 border-2 border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
                  data-oid="3gdlleg"
                >
                  <Icon className={`h-8 w-8 ${color} mb-2`} data-oid="vchrz6s" />
                  <span className="text-sm font-medium text-gray-700" data-oid="s-2q7fj">
                    {label}
                  </span>
                </button>
              ))}
            </div>
          </div>
        );

      case 'rating':
        return (
          <div className="space-y-4" data-oid="gof3jof">
            <h3 className="text-lg font-semibold text-gray-900" data-oid="6t0ssiv">
              How would you rate your experience?
            </h3>
            <div className="flex justify-center space-x-2" data-oid="vol1o6h">
              {[1, 2, 3, 4, 5].map(star => (
                <button
                  key={star}
                  onClick={() => setRating(star)}
                  className="p-1"
                  data-oid="0xnqe05"
                >
                  <Star
                    className={`h-8 w-8 ${
                      star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                    } hover:text-yellow-400 transition-colors`}
                    data-oid="n5nr1jp"
                  />
                </button>
              ))}
            </div>
            <div className="flex justify-between" data-oid="46w.47-">
              <Button variant="outline" onClick={() => setStep('type')} data-oid="b-ed8pu">
                Back
              </Button>
              <Button onClick={() => setStep('message')} disabled={rating === 0} data-oid="7woht6i">
                Continue
              </Button>
            </div>
          </div>
        );

      case 'message':
        return (
          <div className="space-y-4" data-oid="44lgsks">
            <h3 className="text-lg font-semibold text-gray-900" data-oid="2k3dl3w">
              Tell us more about your {feedbackType === 'love' ? 'experience' : feedbackType}
            </h3>
            <textarea
              value={message}
              onChange={e => setMessage(e.target.value)}
              placeholder={
                feedbackType === 'bug'
                  ? 'Please describe the bug you encountered...'
                  : feedbackType === 'feature'
                    ? 'What feature would you like to see?'
                    : feedbackType === 'love'
                      ? 'What do you love about GGsim?'
                      : 'Share your thoughts with us...'
              }
              className="w-full h-32 p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              maxLength={1000}
              data-oid="j-3yex-"
            />

            <div className="text-right text-sm text-gray-500" data-oid="8934fxh">
              {message.length}/1000
            </div>
            <div className="flex justify-between" data-oid="e5rw51z">
              <Button
                variant="outline"
                onClick={() => setStep(feedbackType === 'love' ? 'type' : 'rating')}
                data-oid=":xx-gkv"
              >
                Back
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={!message.trim() || isSubmitting}
                className="flex items-center"
                data-oid="skezxci"
              >
                {isSubmitting ? (
                  <>
                    <div
                      className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
                      data-oid="p-_ztli"
                    />
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" data-oid="_hb4vs." />
                    Send Feedback
                  </>
                )}
              </Button>
            </div>
          </div>
        );

      case 'success':
        return (
          <div className="text-center space-y-4" data-oid="p7_8-3a">
            <div className="flex justify-center" data-oid="co6d09l">
              <div className="rounded-full bg-green-100 p-3" data-oid="6ptenst">
                <Heart className="h-8 w-8 text-green-500" data-oid="0j5_rqy" />
              </div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900" data-oid="bnmj1fo">
              Thank you for your feedback!
            </h3>
            <p className="text-gray-600" data-oid="j.banbz">
              Your feedback helps us improve GGsim. We'll review it and get back to you if needed.
            </p>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      {/* Feedback Button */}
      <motion.button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white rounded-full p-4 shadow-lg z-50 transition-colors"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2 }}
        data-oid="h6jxmfe"
      >
        <MessageCircle className="h-6 w-6" data-oid="oocdi7d" />
      </motion.button>

      {/* Feedback Modal */}
      <AnimatePresence data-oid="uidsn05">
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={() => setIsOpen(false)}
            data-oid="0t0vsr7"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg shadow-xl max-w-md w-full p-6"
              onClick={e => e.stopPropagation()}
              data-oid="r_zyvlk"
            >
              <div className="flex justify-between items-center mb-6" data-oid="w93uujc">
                <h2 className="text-xl font-bold text-gray-900" data-oid="s54r-sa">
                  Feedback
                </h2>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600"
                  data-oid="-w-1220"
                >
                  <X className="h-6 w-6" data-oid="a6caj0u" />
                </button>
              </div>

              {renderStepContent()}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
