import { 
  SignInButton, 
  SignUpButton, 
  SignedIn, 
  SignedOut, 
  UserButton 
} from '@clerk/nextjs'

export default function TestClerkPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">
          Clerk Authentication Test
        </h1>
        
        <SignedOut>
          <div className="space-y-4">
            <p className="text-gray-600 mb-4">
              You are not signed in. Please sign in or sign up to continue.
            </p>
            <div className="flex space-x-4 justify-center">
              <SignInButton mode="modal">
                <button className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md transition-colors">
                  Sign In
                </button>
              </SignInButton>
              <SignUpButton mode="modal">
                <button className="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-md transition-colors">
                  Sign Up
                </button>
              </SignUpButton>
            </div>
          </div>
        </SignedOut>
        
        <SignedIn>
          <div className="space-y-4">
            <p className="text-gray-600 mb-4">
              🎉 You are successfully signed in!
            </p>
            <div className="flex justify-center">
              <UserButton afterSignOutUrl="/test-clerk" />
            </div>
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="text-sm font-medium text-green-800 mb-2">
                ✅ Clerk Integration Working
              </h3>
              <p className="text-sm text-green-700">
                Authentication is properly configured and working!
              </p>
            </div>
          </div>
        </SignedIn>
        
        <div className="mt-8 pt-6 border-t border-gray-200">
          <p className="text-xs text-gray-500">
            GGsim Beta Testing Environment
          </p>
        </div>
      </div>
    </div>
  )
}
