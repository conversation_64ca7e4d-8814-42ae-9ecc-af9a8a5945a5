import { SignInButton, SignUpButton, SignedIn, SignedOut, UserButton } from '@clerk/nextjs';

export default function TestClerkPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50" data-oid="1l-n_o4">
      <div
        className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center"
        data-oid="kjk_cs4"
      >
        <h1 className="text-2xl font-bold text-gray-900 mb-6" data-oid="3brz6t1">
          Clerk Authentication Test
        </h1>

        <SignedOut data-oid="gbqp2zz">
          <div className="space-y-4" data-oid="_5lmjjx">
            <p className="text-gray-600 mb-4" data-oid="1n86m-g">
              You are not signed in. Please sign in or sign up to continue.
            </p>
            <div className="flex space-x-4 justify-center" data-oid="ksfzs0u">
              <SignInButton mode="modal" data-oid="d..wjk0">
                <button
                  className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md transition-colors"
                  data-oid="l__e-gs"
                >
                  Sign In
                </button>
              </SignInButton>
              <SignUpButton mode="modal" data-oid="gbhz-pr">
                <button
                  className="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-md transition-colors"
                  data-oid="0o2ffar"
                >
                  Sign Up
                </button>
              </SignUpButton>
            </div>
          </div>
        </SignedOut>

        <SignedIn data-oid="8:20_:i">
          <div className="space-y-4" data-oid="uq-:dki">
            <p className="text-gray-600 mb-4" data-oid="tmpm363">
              🎉 You are successfully signed in!
            </p>
            <div className="flex justify-center" data-oid="m2v-2zk">
              <UserButton afterSignOutUrl="/test-clerk" data-oid="t_yxmdg" />
            </div>
            <div
              className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg"
              data-oid="gp.syql"
            >
              <h3 className="text-sm font-medium text-green-800 mb-2" data-oid="5hjd5.x">
                ✅ Clerk Integration Working
              </h3>
              <p className="text-sm text-green-700" data-oid="sl7q_za">
                Authentication is properly configured and working!
              </p>
            </div>
          </div>
        </SignedIn>

        <div className="mt-8 pt-6 border-t border-gray-200" data-oid="z_t3zy1">
          <p className="text-xs text-gray-500" data-oid=".w7xcmj">
            GGsim Beta Testing Environment
          </p>
        </div>
      </div>
    </div>
  );
}
