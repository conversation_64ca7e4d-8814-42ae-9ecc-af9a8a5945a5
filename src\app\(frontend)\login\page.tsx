'use client';

import React, { useState, useEffect, Suspense } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { SiteHeader } from '@/components/site-header';

function LoginForm() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const messageParam = searchParams.get('message');
    if (messageParam) {
      setMessage(messageParam);
    }
  }, [searchParams]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    if (!formData.email || !formData.password) {
      setError('Email and password are required');
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await fetch('/api/users/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
        }),
      });

      if (response.ok) {
        // Login successful, redirect to home
        router.push('/');
        router.refresh();
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Login failed. Please check your credentials.');
      }
    } catch (err) {
      setError('Login failed. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div
      className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4"
      data-oid="ewyo50k"
    >
      <div className="max-w-md w-full space-y-8" data-oid="557z0bu">
        <SiteHeader
          title="Sign in to your account"
          subtitle={
            <>
              Don&apos;t have an account?{' '}
              <Link
                href="/register"
                className="font-medium text-green-600 hover:text-green-500"
                data-oid="c1ui:6v"
              >
                Sign up
              </Link>
            </>
          }
          data-oid="xrr6reh"
        />

        {/* Login Form */}
        <Card data-oid="hfgluv8">
          <CardHeader data-oid="8tjjz0k">
            <CardTitle data-oid="1aocgx.">Welcome back</CardTitle>
            <CardDescription data-oid="vidwoym">
              Enter your email and password to sign in
            </CardDescription>
          </CardHeader>
          <CardContent data-oid="1_byxvi">
            <form onSubmit={handleSubmit} className="space-y-4" data-oid="c:-_7hv">
              {message && (
                <Alert data-oid="4ko5eu9">
                  <AlertDescription data-oid="a5kqdig">{message}</AlertDescription>
                </Alert>
              )}

              {error && (
                <Alert variant="destructive" data-oid="gg:3aah">
                  <AlertDescription data-oid="19sirom">{error}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2" data-oid="nz6fvvb">
                <label
                  htmlFor="email"
                  className="text-sm font-medium text-gray-700"
                  data-oid="j.tbl1t"
                >
                  Email address
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  required
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="<EMAIL>"
                  data-oid="jg4wptg"
                />
              </div>

              <div className="space-y-2" data-oid="xtsnita">
                <div className="flex items-center justify-between" data-oid="bd1pjug">
                  <label
                    htmlFor="password"
                    className="text-sm font-medium text-gray-700"
                    data-oid="xipmcck"
                  >
                    Password
                  </label>
                  <Link
                    href="/forgot-password"
                    className="text-sm font-medium text-green-600 hover:text-green-500"
                    data-oid=":q_4qvn"
                  >
                    Forgot password?
                  </Link>
                </div>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  required
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="••••••••"
                  data-oid="4bngu4z"
                />
              </div>

              <Button type="submit" className="w-full" disabled={isSubmitting} data-oid="6l129_v">
                {isSubmitting ? 'Signing in...' : 'Sign in'}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Back to Home */}
        <div className="text-center" data-oid="r76cbu8">
          <Link href="/" className="text-sm text-gray-600 hover:text-gray-900" data-oid="0uuzhzc">
            ← Back to home
          </Link>
        </div>
      </div>
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense
      fallback={
        <div
          className="min-h-screen bg-gray-50 flex items-center justify-center"
          data-oid="-u:tqa2"
        >
          Loading...
        </div>
      }
      data-oid="82u4b.w"
    >
      <LoginForm data-oid="db3wusv" />
    </Suspense>
  );
}
