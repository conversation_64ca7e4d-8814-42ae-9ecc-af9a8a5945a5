import Stripe from 'stripe'

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set in environment variables')
}

// Initialize Stripe with the secret key
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
  typescript: true,
})

// Stripe configuration
export const STRIPE_CONFIG = {
  currency: 'usd',
  paymentMethodTypes: ['card'],
  automaticPaymentMethods: {
    enabled: true,
  },
} as const

// Helper function to create a payment intent
export async function createPaymentIntent({
  amount,
  currency = STRIPE_CONFIG.currency,
  customerId,
  metadata = {},
}: {
  amount: number // Amount in cents
  currency?: string
  customerId?: string
  metadata?: Record<string, string>
}): Promise<Stripe.PaymentIntent> {
  try {
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      customer: customerId,
      metadata,
      automatic_payment_methods: STRIPE_CONFIG.automaticPaymentMethods,
    })

    return paymentIntent
  } catch (error) {
    console.error('Error creating payment intent:', error)
    throw new Error('Failed to create payment intent')
  }
}

// Helper function to create or retrieve a Stripe customer
export async function createOrRetrieveCustomer({
  email,
  name,
  userId,
}: {
  email: string
  name?: string
  userId: string
}): Promise<Stripe.Customer> {
  try {
    // First, try to find existing customer by email
    const existingCustomers = await stripe.customers.list({
      email,
      limit: 1,
    })

    if (existingCustomers.data.length > 0) {
      return existingCustomers.data[0]
    }

    // Create new customer if none exists
    const customer = await stripe.customers.create({
      email,
      name,
      metadata: {
        userId,
      },
    })

    return customer
  } catch (error) {
    console.error('Error creating/retrieving customer:', error)
    throw new Error('Failed to create or retrieve customer')
  }
}

// Helper function to confirm a payment intent
export async function confirmPaymentIntent(
  paymentIntentId: string,
  paymentMethodId?: string
): Promise<Stripe.PaymentIntent> {
  try {
    const paymentIntent = await stripe.paymentIntents.confirm(paymentIntentId, {
      payment_method: paymentMethodId,
    })

    return paymentIntent
  } catch (error) {
    console.error('Error confirming payment intent:', error)
    throw new Error('Failed to confirm payment')
  }
}

// Helper function to retrieve a payment intent
export async function retrievePaymentIntent(
  paymentIntentId: string
): Promise<Stripe.PaymentIntent> {
  try {
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId)
    return paymentIntent
  } catch (error) {
    console.error('Error retrieving payment intent:', error)
    throw new Error('Failed to retrieve payment intent')
  }
}

// Helper function to create a refund
export async function createRefund({
  paymentIntentId,
  amount,
  reason = 'requested_by_customer',
}: {
  paymentIntentId: string
  amount?: number // Amount in cents, if not provided, full refund
  reason?: Stripe.RefundCreateParams.Reason
}): Promise<Stripe.Refund> {
  try {
    const refund = await stripe.refunds.create({
      payment_intent: paymentIntentId,
      amount,
      reason,
    })

    return refund
  } catch (error) {
    console.error('Error creating refund:', error)
    throw new Error('Failed to create refund')
  }
}

// Helper function to verify webhook signature
export function verifyWebhookSignature(
  payload: string | Buffer,
  signature: string,
  secret: string
): Stripe.Event {
  try {
    return stripe.webhooks.constructEvent(payload, signature, secret)
  } catch (error) {
    console.error('Error verifying webhook signature:', error)
    throw new Error('Invalid webhook signature')
  }
}

// Helper function to format amount for display
export function formatAmount(amount: number, currency = 'usd'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase(),
  }).format(amount / 100)
}

// Helper function to convert dollars to cents
export function dollarsToCents(dollars: number): number {
  return Math.round(dollars * 100)
}

// Helper function to convert cents to dollars
export function centsToDollars(cents: number): number {
  return cents / 100
}

// Types for our application
export interface PaymentIntentData {
  id: string
  amount: number
  currency: string
  status: Stripe.PaymentIntent.Status
  clientSecret: string | null
  customerId?: string
  metadata: Record<string, string>
}

export interface CustomerData {
  id: string
  email: string | null
  name: string | null
  metadata: Record<string, string>
}

// Error types
export class StripeError extends Error {
  constructor(
    message: string,
    public code?: string,
    public type?: string
  ) {
    super(message)
    this.name = 'StripeError'
  }
}

// Helper to handle Stripe errors
export function handleStripeError(error: any): StripeError {
  if (error.type === 'StripeCardError') {
    return new StripeError(
      error.message || 'Your card was declined.',
      error.code,
      'card_error'
    )
  } else if (error.type === 'StripeRateLimitError') {
    return new StripeError(
      'Too many requests made to the API too quickly',
      error.code,
      'rate_limit_error'
    )
  } else if (error.type === 'StripeInvalidRequestError') {
    return new StripeError(
      'Invalid parameters were supplied to Stripe\'s API',
      error.code,
      'invalid_request_error'
    )
  } else if (error.type === 'StripeAPIError') {
    return new StripeError(
      'An error occurred internally with Stripe\'s API',
      error.code,
      'api_error'
    )
  } else if (error.type === 'StripeConnectionError') {
    return new StripeError(
      'Some kind of error occurred during the HTTPS communication',
      error.code,
      'connection_error'
    )
  } else if (error.type === 'StripeAuthenticationError') {
    return new StripeError(
      'You probably used an incorrect API key',
      error.code,
      'authentication_error'
    )
  } else {
    return new StripeError(
      'An unknown error occurred',
      'unknown',
      'unknown_error'
    )
  }
}
