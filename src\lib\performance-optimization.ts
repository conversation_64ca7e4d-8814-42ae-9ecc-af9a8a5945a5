/**
 * Performance optimization utilities for Next.js application
 * 
 * This module provides utilities to optimize the performance of the application,
 * including image loading, code splitting, and resource prefetching.
 */

import React, { useEffect } from 'react';
import { usePathname } from 'next/navigation';

/**
 * Configuration for performance optimization
 */
export const performanceConfig = {
  // Enable/disable performance optimizations
  enabled: process.env.NODE_ENV === 'production',
  
  // Image optimization settings
  images: {
    lazyLoading: true,
    placeholderBlur: true,
  },
  
  // Prefetching settings
  prefetch: {
    enabled: true,
    // Links that should be prefetched
    routes: ['/shop', '/account', '/cart'],
  },
  
  // Code splitting settings
  codeSplitting: {
    enabled: true,
  },
};

/**
 * Hook to prefetch critical resources based on the current route
 * @returns void
 */
export function usePrefetchResources(): void {
  const pathname = usePathname();

  useEffect(() => {
    if (!performanceConfig.enabled || !performanceConfig.prefetch.enabled) {
      return;
    }

    // Only run on client side
    if (typeof window === 'undefined') {
      return;
    }

    // Prefetch critical routes using native browser prefetching
    performanceConfig.prefetch.routes.forEach(route => {
      if (pathname !== route) {
        // Use link prefetching instead of router.prefetch during SSG
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = route;
        document.head.appendChild(link);
      }
    });
  }, [pathname]);
}

/**
 * Optimizes image loading by setting appropriate sizes and loading strategies
 * @param src Image source
 * @param width Image width
 * @param height Image height
 * @returns Optimized image props
 */
export function optimizeImage(src: string, width: number, height: number) {
  return {
    src,
    width,
    height,
    loading: (performanceConfig.images.lazyLoading ? 'lazy' : 'eager') as 'lazy' | 'eager',
    placeholder: (performanceConfig.images.placeholderBlur ? 'blur' : 'empty') as 'blur' | 'empty',
    blurDataURL: performanceConfig.images.placeholderBlur ? 
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiNmMGYwZjAiIC8+PHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjZTBlMGUwIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZCkiIC8+PC9zdmc+' : 
      undefined,
  };
}

/**
 * Dynamically imports a component with code splitting
 * @param importFn Import function
 * @returns Dynamic import with loading and error states
 */
export function dynamicImport(importFn: () => Promise<any>) {
  if (!performanceConfig.enabled || !performanceConfig.codeSplitting.enabled) {
    return importFn();
  }
  
  return {
    loader: importFn,
    loading: () => React.createElement('div', { className: 'loading-placeholder' }, 'Loading...'),
    ssr: true,
  };
}
