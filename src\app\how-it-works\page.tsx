import type { Metadata } from 'next'
import { HowItWorksClient } from './HowItWorksClient'

export const metadata: Metadata = {
  title: 'How eSIM Works - Complete Guide | GGsim',
  description: 'Learn how eSIM technology works and how to activate your digital SIM card in 3 simple steps. Complete guide to eSIM installation, compatibility, and troubleshooting.',
  keywords: [
    'how eSIM works',
    'eSIM activation guide',
    'digital SIM card setup',
    'eSIM installation steps',
    'mobile connectivity guide',
    'travel SIM card',
    'international roaming alternative'
  ],
  openGraph: {
    title: 'How eSIM Works - Complete Guide | GGsim',
    description: 'Learn how eSIM technology works and how to activate your digital SIM card in 3 simple steps. Complete guide for travelers.',
    type: 'article',
    images: [
      {
        url: '/images/LogoT.png',
        width: 1200,
        height: 630,
        alt: 'How eSIM Works - Step by step guide',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'How eSIM Works - Complete Guide | GGsim',
    description: 'Learn how eSIM technology works and activate your digital SIM in 3 simple steps.',
    images: ['/images/LogoT.png'],
  },
  alternates: {
    canonical: 'https://ggsim.me/how-it-works',
  },
}

export default function HowItWorksPage() {
  return <HowItWorksClient />
}
