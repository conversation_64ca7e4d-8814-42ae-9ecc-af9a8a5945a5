import React from 'react';
import { OptimizedLayout } from '@/components/layout/OptimizedLayout';
import { PerformanceOptimizedImage } from '@/components/performance/PerformanceOptimizedImage';

export const metadata = {
  title: 'Performance Demo | GGsim',
  description: 'Demonstration of performance optimizations in GGsim',
};

/**
 * Demo page showcasing performance optimizations
 */
export default function PerformanceDemoPage() {
  return (
    <OptimizedLayout data-oid="zd6l8fi">
      <div className="container mx-auto px-4 py-8" data-oid="n5w8i4_">
        <h1 className="text-3xl font-bold mb-6" data-oid="n_kg9el">
          Performance Optimizations Demo
        </h1>

        <div className="mb-8" data-oid="snglhlo">
          <p className="text-gray-600 mb-4" data-oid="d7:m4uu">
            This page demonstrates various performance optimizations implemented in the GGsim
            application, including optimized image loading, resource prefetching, and performance
            monitoring.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12" data-oid="9cepz6k">
          <div className="bg-white rounded-lg shadow-md p-6" data-oid="txg8i5:">
            <h2 className="text-xl font-bold mb-4" data-oid="artq8_s">
              Optimized Image Loading
            </h2>
            <p className="text-gray-600 mb-4" data-oid="h.gh6kk">
              Images are loaded with next/image for optimal performance, including lazy loading,
              proper sizing, and format optimization.
            </p>
            <div className="mt-4" data-oid="6s2.is8">
              <PerformanceOptimizedImage
                src="/images/LogoT.png"
                alt="GGsim Logo"
                width={300}
                height={200}
                className="rounded-md"
                data-oid="g3gp6.:"
              />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6" data-oid="z:_2ql9">
            <h2 className="text-xl font-bold mb-4" data-oid="75j:t7h">
              Resource Prefetching
            </h2>
            <p className="text-gray-600 mb-4" data-oid="ojyxmij">
              Critical resources are prefetched to improve navigation performance. The
              OptimizedLayout component automatically prefetches important routes.
            </p>
            <div className="mt-4 space-y-2" data-oid="5-k2hvu">
              <div className="flex items-center" data-oid="9dxnmyw">
                <div className="w-4 h-4 rounded-full bg-green-500 mr-2" data-oid="hjclyod"></div>
                <span data-oid="iitm68g">Prefetching enabled for critical routes</span>
              </div>
              <div className="flex items-center" data-oid=".giel15">
                <div className="w-4 h-4 rounded-full bg-green-500 mr-2" data-oid="9rpeq17"></div>
                <span data-oid="msli4mg">Code splitting for optimal bundle size</span>
              </div>
              <div className="flex items-center" data-oid="7m4lxen">
                <div className="w-4 h-4 rounded-full bg-green-500 mr-2" data-oid="5e8s:9p"></div>
                <span data-oid="y4501-n">Automatic performance monitoring</span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mb-8" data-oid=":ptamxl">
          <h2 className="text-xl font-bold mb-4" data-oid="3xm:hdi">
            Core Web Vitals
          </h2>
          <p className="text-gray-600 mb-4" data-oid="t1bc542">
            The application is optimized for Core Web Vitals, which are important metrics for user
            experience and SEO:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6" data-oid="eipdwp0">
            <div className="border border-gray-200 rounded-lg p-4" data-oid=":t0wlg8">
              <h3 className="font-semibold mb-2" data-oid="b.j_p07">
                Largest Contentful Paint (LCP)
              </h3>
              <p className="text-sm text-gray-500" data-oid="u2ac8q3">
                Measures loading performance. To provide a good user experience, LCP should occur
                within 2.5 seconds of when the page first starts loading.
              </p>
            </div>

            <div className="border border-gray-200 rounded-lg p-4" data-oid="duon.c9">
              <h3 className="font-semibold mb-2" data-oid="yd6ora_">
                First Input Delay (FID)
              </h3>
              <p className="text-sm text-gray-500" data-oid="irojmh2">
                Measures interactivity. To provide a good user experience, pages should have a FID
                of 100 milliseconds or less.
              </p>
            </div>

            <div className="border border-gray-200 rounded-lg p-4" data-oid="_v4c_c7">
              <h3 className="font-semibold mb-2" data-oid="b70_roe">
                Cumulative Layout Shift (CLS)
              </h3>
              <p className="text-sm text-gray-500" data-oid="2mo-:ig">
                Measures visual stability. To provide a good user experience, pages should maintain
                a CLS of 0.1 or less.
              </p>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 rounded-lg p-6" data-oid="zfe1vd.">
          <h2 className="text-xl font-bold text-blue-700 mb-4" data-oid="h0c6knb">
            Performance Best Practices
          </h2>
          <ul className="list-disc pl-5 space-y-2 text-blue-600" data-oid="_khdbl5">
            <li data-oid=":ke2::4">Use next/image for automatic image optimization</li>
            <li data-oid="rbp8at5">Implement code splitting with dynamic imports</li>
            <li data-oid="a7q70vr">Prefetch critical resources and routes</li>
            <li data-oid="bxr30l3">Monitor Core Web Vitals</li>
            <li data-oid="tfsrtmu">Optimize JavaScript bundle size</li>
            <li data-oid="ie.o6c8">Use server components where appropriate</li>
            <li data-oid="4oeb46c">Implement proper caching strategies</li>
          </ul>
        </div>
      </div>
    </OptimizedLayout>
  );
}
