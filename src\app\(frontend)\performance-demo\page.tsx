import React from 'react';
import { OptimizedLayout } from '@/components/layout/OptimizedLayout';
import { PerformanceOptimizedImage } from '@/components/performance/PerformanceOptimizedImage';

export const metadata = {
  title: 'Performance Demo | GGsim',
  description: 'Demonstration of performance optimizations in GGsim',
};

/**
 * Demo page showcasing performance optimizations
 */
export default function PerformanceDemoPage() {
  return (
    <OptimizedLayout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">Performance Optimizations Demo</h1>
        
        <div className="mb-8">
          <p className="text-gray-600 mb-4">
            This page demonstrates various performance optimizations implemented in the GGsim application,
            including optimized image loading, resource prefetching, and performance monitoring.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold mb-4">Optimized Image Loading</h2>
            <p className="text-gray-600 mb-4">
              Images are loaded with next/image for optimal performance, including lazy loading,
              proper sizing, and format optimization.
            </p>
            <div className="mt-4">
              <PerformanceOptimizedImage
                src="/images/LogoT.png"
                alt="GGsim Logo"
                width={300}
                height={200}
                className="rounded-md"
              />
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold mb-4">Resource Prefetching</h2>
            <p className="text-gray-600 mb-4">
              Critical resources are prefetched to improve navigation performance.
              The OptimizedLayout component automatically prefetches important routes.
            </p>
            <div className="mt-4 space-y-2">
              <div className="flex items-center">
                <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
                <span>Prefetching enabled for critical routes</span>
              </div>
              <div className="flex items-center">
                <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
                <span>Code splitting for optimal bundle size</span>
              </div>
              <div className="flex items-center">
                <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
                <span>Automatic performance monitoring</span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-bold mb-4">Core Web Vitals</h2>
          <p className="text-gray-600 mb-4">
            The application is optimized for Core Web Vitals, which are important metrics for
            user experience and SEO:
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold mb-2">Largest Contentful Paint (LCP)</h3>
              <p className="text-sm text-gray-500">
                Measures loading performance. To provide a good user experience, LCP should occur
                within 2.5 seconds of when the page first starts loading.
              </p>
            </div>
            
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold mb-2">First Input Delay (FID)</h3>
              <p className="text-sm text-gray-500">
                Measures interactivity. To provide a good user experience, pages should have a FID
                of 100 milliseconds or less.
              </p>
            </div>
            
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold mb-2">Cumulative Layout Shift (CLS)</h3>
              <p className="text-sm text-gray-500">
                Measures visual stability. To provide a good user experience, pages should maintain
                a CLS of 0.1 or less.
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-blue-50 rounded-lg p-6">
          <h2 className="text-xl font-bold text-blue-700 mb-4">Performance Best Practices</h2>
          <ul className="list-disc pl-5 space-y-2 text-blue-600">
            <li>Use next/image for automatic image optimization</li>
            <li>Implement code splitting with dynamic imports</li>
            <li>Prefetch critical resources and routes</li>
            <li>Monitor Core Web Vitals</li>
            <li>Optimize JavaScript bundle size</li>
            <li>Use server components where appropriate</li>
            <li>Implement proper caching strategies</li>
          </ul>
        </div>
      </div>
    </OptimizedLayout>
  );
}