import { sanitizeInput, sanitizeObject, applySecurityHeaders } from '../security-enhancements';

describe('Security Enhancement Utilities', () => {
  describe('sanitizeInput', () => {
    it('should remove script tags from HTML strings', () => {
      const input = '<script>alert("XSS");</script>Hello World';
      const sanitized = sanitizeInput(input);
      
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).toContain('Hello World');
    });

    it('should handle non-string inputs by converting to string', () => {
      const input = 123;
      const sanitized = sanitizeInput(input);
      
      expect(sanitized).toBe('123');
    });

    it('should handle null and undefined inputs', () => {
      expect(sanitizeInput(null)).toBe('');
      expect(sanitizeInput(undefined)).toBe('');
    });

    it('should remove dangerous attributes', () => {
      const input = '<a href="javascript:alert(1)">Click me</a>';
      const sanitized = sanitizeInput(input);
      
      expect(sanitized).not.toContain('javascript:');
      expect(sanitized).toContain('Click me');
    });
  });

  describe('sanitizeObject', () => {
    it('should sanitize all string values in an object', () => {
      const obj = {
        name: '<script>alert("XSS");</script>John',
        description: 'Normal text',
        nested: {
          value: '<img src="x" onerror="alert(1)" />',
        },
      };

      const sanitized = sanitizeObject(obj);
      
      expect(sanitized.name).not.toContain('<script>');
      expect(sanitized.name).toContain('John');
      expect(sanitized.description).toBe('Normal text');
      expect(sanitized.nested.value).not.toContain('onerror');
    });

    it('should handle arrays', () => {
      const obj = {
        items: [
          '<script>alert(1)</script>',
          'Normal item',
          { text: '<img src="x" onerror="alert(1)" />' },
        ],
      };

      const sanitized = sanitizeObject(obj);
      
      expect(sanitized.items[0]).not.toContain('<script>');
      expect(sanitized.items[1]).toBe('Normal item');
      expect(sanitized.items[2].text).not.toContain('onerror');
    });

    it('should not modify non-string values', () => {
      const obj = {
        number: 123,
        boolean: true,
        nullValue: null,
      };

      const sanitized = sanitizeObject(obj);
      
      expect(sanitized.number).toBe(123);
      expect(sanitized.boolean).toBe(true);
      expect(sanitized.nullValue).toBe(null);
    });
  });

  describe('applySecurityHeaders', () => {
    it('should apply security headers to a response object', () => {
      // Mock response object
      const headers = new Map();
      const response = {
        headers: {
          set: (key, value) => headers.set(key, value),
          get: (key) => headers.get(key),
        },
      };

      // @ts-ignore - Simplified for testing
      applySecurityHeaders(response);
      
      // Check that important security headers are set
      expect(headers.get('Content-Security-Policy')).toBeDefined();
      expect(headers.get('X-Content-Type-Options')).toBe('nosniff');
      expect(headers.get('X-Frame-Options')).toBeDefined();
      expect(headers.get('Strict-Transport-Security')).toBeDefined();
      expect(headers.get('Referrer-Policy')).toBeDefined();
    });
  });
});