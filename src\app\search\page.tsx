import type { Metadata } from 'next'
import { SearchClient } from './SearchClient'

export const metadata: Metadata = {
  title: 'Search eSIM Plans - Find the Best Data Plans for Travel | GGsim',
  description: 'Search and compare eSIM data plans from top providers for 200+ countries. Find the perfect travel data plan with instant activation, competitive prices, and reliable coverage.',
  keywords: [
    'eSIM plans search',
    'travel data plans',
    'international eSIM',
    'mobile data abroad',
    'eSIM comparison',
    'travel SIM card',
    'global connectivity',
    'roaming alternative',
    'prepaid data plans',
    'instant activation eSIM'
  ],
  openGraph: {
    title: 'Search eSIM Plans - Find the Best Data Plans for Travel | GGsim',
    description: 'Search and compare eSIM data plans from top providers for 200+ countries. Instant activation, competitive prices.',
    type: 'website',
    images: [
      {
        url: '/images/LogoT.png',
        width: 1200,
        height: 630,
        alt: 'Search eSIM Plans - Global Coverage Map',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Search eSIM Plans - Find the Best Data Plans | GGsim',
    description: 'Compare eSIM data plans from top providers for 200+ countries. Instant activation.',
    images: ['/images/LogoT.png'],
  },
  alternates: {
    canonical: 'https://ggsim.me/search',
  },
}

export default function SearchPage() {
  return <SearchClient />
}
