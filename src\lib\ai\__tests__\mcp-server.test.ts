import { createMCPAI, registerMCPServer, getMCPServers, executeTool } from '../mcp-server';

// Mock the AI SDK
jest.mock('ai', () => ({
  createAI: jest.fn(() => ({
    submitUserMessage: jest.fn(),
    currentMessages: [],
  })),
}));

describe('MCP Server Integration', () => {
  beforeEach(() => {
    // Clear any registered servers between tests
    // This assumes we have access to the internal state, which might need adjustment
    // based on the actual implementation
    jest.resetAllMocks();
  });

  describe('registerMCPServer', () => {
    it('should register a valid MCP server', () => {
      const mockServer = {
        name: 'test-server',
        description: 'A test server',
        tools: [
          {
            name: 'test-tool',
            description: 'A test tool',
            inputSchema: { type: 'object', properties: {} },
          },
        ],
      };

      const result = registerMCPServer(mockServer);
      expect(result).toBe(true);

      const servers = getMCPServers();
      expect(servers).toHaveLength(1);
      expect(servers[0].name).toBe('test-server');
    });

    it('should reject a server with invalid structure', () => {
      const invalidServer = {
        name: 'invalid-server',
        // Missing required properties
      };

      // @ts-ignore - Testing invalid input
      const result = registerMCPServer(invalidServer);
      expect(result).toBe(false);

      const servers = getMCPServers();
      expect(servers).toHaveLength(0);
    });

    it('should not register duplicate servers', () => {
      const mockServer = {
        name: 'test-server',
        description: 'A test server',
        tools: [
          {
            name: 'test-tool',
            description: 'A test tool',
            inputSchema: { type: 'object', properties: {} },
          },
        ],
      };

      registerMCPServer(mockServer);
      const result = registerMCPServer(mockServer); // Try to register again
      
      expect(result).toBe(false); // Should fail on duplicate
      
      const servers = getMCPServers();
      expect(servers).toHaveLength(1); // Still only one server
    });
  });

  describe('executeTool', () => {
    it('should execute a tool on a registered server', async () => {
      // Register a mock server
      const mockServer = {
        name: 'test-server',
        description: 'A test server',
        tools: [
          {
            name: 'echo',
            description: 'Echoes back the input',
            inputSchema: { 
              type: 'object', 
              properties: { 
                message: { type: 'string' } 
              } 
            },
          },
        ],
      };

      registerMCPServer(mockServer);

      // Mock the fetch function
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue({ result: 'Hello, World!' }),
      });

      const result = await executeTool('test-server', 'echo', { message: 'Hello, World!' });
      
      expect(result).toEqual({ result: 'Hello, World!' });
      expect(global.fetch).toHaveBeenCalled();
    });

    it('should throw an error for non-existent server', async () => {
      await expect(executeTool('non-existent', 'tool', {})).rejects.toThrow(
        'Server not found: non-existent'
      );
    });

    it('should throw an error for non-existent tool', async () => {
      // Register a mock server
      const mockServer = {
        name: 'test-server',
        description: 'A test server',
        tools: [
          {
            name: 'echo',
            description: 'Echoes back the input',
            inputSchema: { type: 'object', properties: {} },
          },
        ],
      };

      registerMCPServer(mockServer);

      await expect(executeTool('test-server', 'non-existent', {})).rejects.toThrow(
        'Tool not found: non-existent'
      );
    });
  });

  describe('createMCPAI', () => {
    it('should create an AI instance with MCP support', () => {
      const ai = createMCPAI({
        initialMessages: [],
        initialAIState: {},
        initialUIState: {},
      });

      expect(ai).toBeDefined();
      // Additional expectations would depend on the actual implementation
    });
  });
});