#!/bin/bash

# GGsim Beta Deployment Script
# This script automates the deployment of the GGsim beta testing environment

set -e

echo "🚀 Starting GGsim Beta Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 20+"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        print_error "Git is not installed. Please install Git"
        exit 1
    fi
    
    if ! command -v vercel &> /dev/null; then
        print_warning "Vercel CLI not found. Installing..."
        npm install -g vercel@latest
    fi
    
    print_success "All dependencies are available"
}

# Check environment variables
check_environment() {
    print_status "Checking environment configuration..."
    
    if [ ! -f ".env.local" ]; then
        print_warning ".env.local not found. Creating from .env.example..."
        cp .env.example .env.local
        print_warning "Please configure .env.local with your actual values before continuing"
        exit 1
    fi
    
    # Check for required environment variables
    required_vars=(
        "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY"
        "CLERK_SECRET_KEY"
        "DATABASE_URL"
    )
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^${var}=" .env.local; then
            print_error "Required environment variable ${var} not found in .env.local"
            exit 1
        fi
    done
    
    print_success "Environment configuration is valid"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    npm ci
    print_success "Dependencies installed"
}

# Run tests
run_tests() {
    print_status "Running tests..."
    
    # Type checking
    print_status "Running TypeScript type check..."
    npx tsc --noEmit
    
    # Linting
    print_status "Running ESLint..."
    npm run lint
    
    # Unit tests
    print_status "Running unit tests..."
    npm run test -- --passWithNoTests
    
    print_success "All tests passed"
}

# Build application
build_application() {
    print_status "Building application..."
    npm run build
    print_success "Application built successfully"
}

# Deploy to Vercel
deploy_to_vercel() {
    print_status "Deploying to Vercel..."
    
    # Check if project is linked
    if [ ! -f ".vercel/project.json" ]; then
        print_status "Linking project to Vercel..."
        vercel link
    fi
    
    # Deploy
    if [ "$1" = "production" ]; then
        print_status "Deploying to production..."
        vercel --prod
    else
        print_status "Deploying to preview..."
        vercel
    fi
    
    print_success "Deployment completed"
}

# Set up monitoring
setup_monitoring() {
    print_status "Setting up monitoring..."
    
    # Check if Sentry is configured
    if grep -q "SENTRY_DSN" .env.local; then
        print_status "Sentry configuration found"
    else
        print_warning "Sentry not configured. Error tracking will be limited."
    fi
    
    # Check if analytics are configured
    if grep -q "NEXT_PUBLIC_VERCEL_ANALYTICS_ID" .env.local; then
        print_status "Vercel Analytics configuration found"
    else
        print_warning "Vercel Analytics not configured."
    fi
    
    print_success "Monitoring setup completed"
}

# Create GitHub repository (if needed)
setup_github() {
    print_status "Setting up GitHub repository..."
    
    if [ ! -d ".git" ]; then
        print_status "Initializing Git repository..."
        git init
        git add .
        git commit -m "feat: initial commit with beta testing environment"
    fi
    
    # Check if remote exists
    if ! git remote get-url origin &> /dev/null; then
        print_warning "No GitHub remote found. Please add your repository:"
        print_warning "git remote add origin https://github.com/yourusername/ggsim.git"
        print_warning "git push -u origin main"
    else
        print_status "Pushing to GitHub..."
        git add .
        git commit -m "feat: deploy beta testing environment" || true
        git push origin main || git push origin master
    fi
    
    print_success "GitHub setup completed"
}

# Main deployment function
main() {
    echo "🎯 GGsim Beta Testing Environment Deployment"
    echo "============================================="
    
    # Parse command line arguments
    ENVIRONMENT=${1:-preview}
    
    if [ "$ENVIRONMENT" != "production" ] && [ "$ENVIRONMENT" != "preview" ]; then
        print_error "Invalid environment. Use 'production' or 'preview'"
        exit 1
    fi
    
    print_status "Deploying to: $ENVIRONMENT"
    
    # Run deployment steps
    check_dependencies
    check_environment
    install_dependencies
    run_tests
    build_application
    setup_monitoring
    deploy_to_vercel $ENVIRONMENT
    setup_github
    
    echo ""
    echo "🎉 Deployment completed successfully!"
    echo "============================================="
    
    if [ "$ENVIRONMENT" = "production" ]; then
        echo "🌐 Production URL: https://beta.ggsim.me"
    else
        echo "🔍 Preview URL: Check Vercel dashboard for the preview URL"
    fi
    
    echo ""
    echo "📋 Next Steps:"
    echo "1. Configure your custom domain in Vercel dashboard"
    echo "2. Set up your Clerk authentication allowlist"
    echo "3. Configure monitoring and analytics services"
    echo "4. Test the beta environment with approved users"
    echo "5. Monitor feedback and analytics dashboards"
    echo ""
    echo "📚 Documentation: README.md"
    echo "🐛 Issues: Check GitHub Issues"
    echo "💬 Feedback: Use the in-app feedback widget"
}

# Run main function with all arguments
main "$@"
