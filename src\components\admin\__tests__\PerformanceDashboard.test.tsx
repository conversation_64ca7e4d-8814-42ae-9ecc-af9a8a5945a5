import React from 'react';
import { render, screen } from '@testing-library/react';
import { PerformanceDashboard } from '../PerformanceDashboard';

describe('PerformanceDashboard', () => {
  it('renders the dashboard title', () => {
    render(<PerformanceDashboard />);
    expect(screen.getByText('Performance Dashboard')).toBeInTheDocument();
  });

  it('renders all performance metrics sections', () => {
    render(<PerformanceDashboard />);
    
    // Check for Core Web Vitals section
    expect(screen.getByText('Core Web Vitals')).toBeInTheDocument();
    
    // Check for individual metrics
    expect(screen.getByText('Largest Contentful Paint (LCP)')).toBeInTheDocument();
    expect(screen.getByText('First Input Delay (FID)')).toBeInTheDocument();
    expect(screen.getByText('Cumulative Layout Shift (CLS)')).toBeInTheDocument();
    expect(screen.getByText('Time to First Byte (TTFB)')).toBeInTheDocument();
    expect(screen.getByText('First Contentful Paint (FCP)')).toBeInTheDocument();
  });

  it('displays metric values with correct formatting', () => {
    render(<PerformanceDashboard />);
    
    // Check for metric values with units
    expect(screen.getByText(/\d+\.\d+ s/)).toBeInTheDocument(); // LCP in seconds
    expect(screen.getByText(/\d+ ms/)).toBeInTheDocument(); // FID in milliseconds
    expect(screen.getByText(/0\.\d+/)).toBeInTheDocument(); // CLS (unitless)
  });

  it('applies the correct status colors based on metric values', () => {
    render(<PerformanceDashboard />);
    
    // Get all status indicators
    const statusIndicators = screen.getAllByTestId('status-indicator');
    
    // Verify we have status indicators
    expect(statusIndicators.length).toBeGreaterThan(0);
    
    // Check that each indicator has one of the expected status classes
    statusIndicators.forEach(indicator => {
      const hasStatusClass = [
        'bg-green-500', // Good
        'bg-yellow-500', // Needs Improvement
        'bg-red-500', // Poor
      ].some(className => indicator.classList.contains(className));
      
      expect(hasStatusClass).toBe(true);
    });
  });

  it('renders the explanation section for each metric', () => {
    render(<PerformanceDashboard />);
    
    // Check for explanation sections
    expect(screen.getByText(/LCP measures/)).toBeInTheDocument();
    expect(screen.getByText(/FID measures/)).toBeInTheDocument();
    expect(screen.getByText(/CLS measures/)).toBeInTheDocument();
  });

  it('displays the resource usage section', () => {
    render(<PerformanceDashboard />);
    
    expect(screen.getByText('Resource Usage')).toBeInTheDocument();
    expect(screen.getByText(/Memory Usage/)).toBeInTheDocument();
    expect(screen.getByText(/CPU Usage/)).toBeInTheDocument();
    expect(screen.getByText(/Network Requests/)).toBeInTheDocument();
  });
});