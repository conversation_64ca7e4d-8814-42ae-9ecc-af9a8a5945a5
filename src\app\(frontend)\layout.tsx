import React from 'react';
import { CartProvider } from '@/lib/cart-context';
import { CartSidebar } from '@/components/cart-sidebar';
import '../globals.css';

export const metadata = {
  description: 'A mini store template using Payload built with ggsim.',
  title: 'ggsim Portal Mini Store Template',
  icons: {
    icon: '/favicon.ico',
  },
};

export default async function RootLayout(props: { children: React.ReactNode }) {
  const { children } = props;

  return (
    <html lang="en" data-oid="3mbi2tj">
      <body data-oid="in9h_oh">
        <CartProvider data-oid="6qcnhb-">
          <main data-oid="8sj15w.">{children}</main>
          <CartSidebar data-oid="-acbote" />
        </CartProvider>
      </body>
    </html>
  );
}
