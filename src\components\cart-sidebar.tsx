'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { X, Plus, Minus, ShoppingCart } from 'lucide-react';

import { useCart } from '@/lib/cart-context';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export const CartSidebar: React.FC = () => {
  const { state, removeItem, updateQuantity, closeCart, getTotalItems, getTotalPrice } = useCart();

  if (!state.isOpen) return null;

  return (
    <>
      {/* Enhanced Overlay */}
      <div
        className="fixed inset-0 bg-black/40 backdrop-blur-sm z-40 transition-opacity duration-300"
        onClick={closeCart}
        data-oid="uobgvq8"
      />

      {/* Sidebar */}
      <div
        className="fixed right-0 top-0 h-full w-full max-w-md bg-white/95 backdrop-blur-xl border-l border-gray-200/60 shadow-2xl z-50 overflow-hidden flex flex-col"
        data-oid="n9sn013"
      >
        {/* Header */}
        <div
          className="p-6 border-b border-gray-200/60 bg-gradient-to-r from-gray-50/80 to-white/80 backdrop-blur-sm"
          data-oid="2ymna-g"
        >
          <div className="flex items-center justify-between" data-oid="uwgfvzo">
            <h2
              className="text-xl font-bold text-gray-800 flex items-center gap-3"
              data-oid="o_4f:yp"
            >
              <div
                className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"
                data-oid="74.60:c"
              >
                <ShoppingCart className="h-5 w-5 text-white" data-oid="sa5wejx" />
              </div>
              Your Cart ({getTotalItems()})
            </h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={closeCart}
              className="h-10 w-10 p-0 hover:bg-gray-100 rounded-full transition-all duration-300 hover:scale-105"
              data-oid="sifnu:x"
            >
              <X className="h-5 w-5 text-gray-600" data-oid="7jc9xn:" />
            </Button>
          </div>
        </div>

        {state.items.length === 0 ? (
          /* Empty Cart */
          <div className="flex-1 flex items-center justify-center p-8" data-oid="3ghe6lp">
            <div className="text-center space-y-6" data-oid="ebboqmn">
              <div
                className="w-24 h-24 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full mx-auto flex items-center justify-center"
                data-oid="5w74jhk"
              >
                <ShoppingCart className="h-12 w-12 text-white" data-oid="f0od.wb" />
              </div>
              <div className="space-y-2" data-oid="mi7z25a">
                <p className="text-xl font-semibold text-gray-800" data-oid="40rbo_i">
                  Your cart is empty
                </p>
                <p className="text-gray-600" data-oid="v236:_8">
                  Add some delicious snacks to get started!
                </p>
              </div>
              <Button
                onClick={closeCart}
                className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 border-0 text-white px-6 py-3 rounded-full shadow-lg hover:shadow-green-500/25 transition-all duration-300 hover:scale-105"
                data-oid="2fn:lk-"
              >
                Continue Shopping
              </Button>
            </div>
          </div>
        ) : (
          <>
            {/* Cart Items */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4" data-oid="fpvtwkv">
              {state.items.map((item, index) => (
                <Card
                  key={item.id}
                  className="group relative overflow-hidden rounded-2xl border border-gray-200/60 bg-white/90 backdrop-blur-xl shadow-lg transition-all duration-300 hover:shadow-xl hover:shadow-green-500/10 hover:border-green-300/50 p-0"
                  style={{
                    animationDelay: `${index * 50}ms`,
                  }}
                  data-oid="x6uqs_k"
                >
                  {/* Card Glow Effect */}
                  <div
                    className="absolute inset-0 bg-gradient-to-r from-green-100/20 to-emerald-100/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    data-oid="4yx1ki7"
                  ></div>

                  <div className="relative z-10 p-4" data-oid="2dkd11q">
                    <div className="flex gap-4" data-oid="w_9dr1-">
                      {/* Item Image */}
                      {item.image?.url && (
                        <div
                          className="relative w-20 h-20 rounded-xl overflow-hidden flex-shrink-0 shadow-md"
                          data-oid="rkta55v"
                        >
                          <Image
                            src={item.image.url}
                            alt={item.image.alt || item.name}
                            fill
                            className="object-cover transition-transform duration-300 group-hover:scale-105"
                            data-oid="uf5ga0w"
                          />
                        </div>
                      )}

                      {/* Item Details */}
                      <div className="flex-1 min-w-0 space-y-2" data-oid="nj9oq3u">
                        <div className="flex items-start justify-between" data-oid="_981kcd">
                          <h3
                            className="font-semibold text-gray-800 group-hover:text-green-600 transition-colors duration-300 leading-tight"
                            data-oid="skfsvtm"
                          >
                            {item.name}
                          </h3>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-500 hover:text-red-600 hover:bg-red-50 ml-2 h-8 w-8 p-0 rounded-full"
                            onClick={() => removeItem(item.id)}
                            data-oid="e48twts"
                          >
                            <X className="h-4 w-4" data-oid="xbolpcz" />
                          </Button>
                        </div>

                        <Badge
                          variant="secondary"
                          className="bg-gray-100 text-gray-700 border border-gray-200/60 text-xs font-medium"
                          data-oid="s3h8f27"
                        >
                          {item.category}
                        </Badge>

                        <p
                          className="text-lg font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent"
                          data-oid="b308:8:"
                        >
                          ${item.price.toFixed(2)} each
                        </p>

                        {/* Quantity Controls */}
                        <div className="flex items-center gap-3 pt-2" data-oid="do_:rct">
                          <div
                            className="flex items-center gap-2 bg-gray-50/80 rounded-full p-1"
                            data-oid="wjinv13"
                          >
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 rounded-full hover:bg-white transition-all duration-200"
                              onClick={() => updateQuantity(item.id, item.quantity - 1)}
                              data-oid="lwipngu"
                            >
                              <Minus className="h-4 w-4" data-oid="yl9kav." />
                            </Button>
                            <span
                              className="text-sm font-semibold w-8 text-center bg-white rounded-full py-1"
                              data-oid="4a:sixw"
                            >
                              {item.quantity}
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 rounded-full hover:bg-white transition-all duration-200"
                              onClick={() => updateQuantity(item.id, item.quantity + 1)}
                              data-oid=":k1k6xa"
                            >
                              <Plus className="h-4 w-4" data-oid="og7a1sv" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Item Total */}
                    <div
                      className="flex justify-between items-center mt-4 pt-3 border-t border-gray-200/60"
                      data-oid="eiweyxi"
                    >
                      <span className="text-sm text-gray-600 font-medium" data-oid="o_hmkc1">
                        Subtotal:
                      </span>
                      <span className="text-lg font-bold text-gray-800" data-oid="03fl7w-">
                        ${(item.price * item.quantity).toFixed(2)}
                      </span>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Footer */}
            <div
              className="border-t border-gray-200/60 bg-gradient-to-r from-gray-50/80 to-white/80 backdrop-blur-sm p-6 space-y-6"
              data-oid="jj2gjgf"
            >
              <div className="flex justify-between items-center" data-oid="qpv8:0t">
                <span className="text-xl font-bold text-gray-800" data-oid="v.zq2s4">
                  Total:
                </span>
                <span
                  className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent"
                  data-oid="m41lf9p"
                >
                  ${getTotalPrice().toFixed(2)}
                </span>
              </div>

              <div
                className="h-px bg-gradient-to-r from-green-400 to-emerald-400 rounded-full"
                data-oid="56pba8g"
              ></div>

              <div className="space-y-3" data-oid="tuvui3j">
                <Button
                  asChild
                  className="w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 border-0 text-white py-3 rounded-full shadow-lg hover:shadow-green-500/25 transition-all duration-300 hover:scale-105"
                  size="lg"
                  data-oid="ivgzr1u"
                >
                  <Link href="/checkout" onClick={closeCart} data-oid="qb0p.tg">
                    Proceed to Checkout
                  </Link>
                </Button>
                <Button
                  variant="outline"
                  className="w-full border-gray-300 bg-white/80 backdrop-blur-sm hover:bg-gray-50 text-gray-700 py-3 rounded-full transition-all duration-300"
                  onClick={closeCart}
                  data-oid="t62quk-"
                >
                  Continue Shopping
                </Button>
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );
};
