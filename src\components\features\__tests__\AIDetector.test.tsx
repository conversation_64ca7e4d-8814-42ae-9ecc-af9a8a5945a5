import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AIDetector } from '../AIDetector';
import * as aiDetection from '@/lib/ai/ai-detection';

// Mock the AI detection module
jest.mock('@/lib/ai/ai-detection', () => ({
  detectAIContent: jest.fn(),
}));

describe('AIDetector', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Default mock implementation
    (aiDetection.detectAIContent as jest.Mock).mockResolvedValue({
      score: 0.3,
      isAIGenerated: false,
      confidence: 'high',
      explanation: 'This content appears to be human-written.',
    });
  });

  it('renders the component with initial state', () => {
    render(<AIDetector data-oid="zus8vg5" />);

    expect(screen.getByText('AI Content Detector')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter text to analyze...')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /analyze/i })).toBeInTheDocument();
  });

  it('updates textarea value on input', () => {
    render(<AIDetector data-oid="yta8u.d" />);

    const textarea = screen.getByPlaceholderText('Enter text to analyze...');
    fireEvent.change(textarea, { target: { value: 'Test content' } });

    expect(textarea).toHaveValue('Test content');
  });

  it('calls detectAIContent when Analyze button is clicked', async () => {
    render(<AIDetector data-oid="lvmv7yd" />);

    const textarea = screen.getByPlaceholderText('Enter text to analyze...');
    fireEvent.change(textarea, { target: { value: 'Test content' } });

    const analyzeButton = screen.getByRole('button', { name: /analyze/i });
    fireEvent.click(analyzeButton);

    await waitFor(() => {
      expect(aiDetection.detectAIContent).toHaveBeenCalledWith('Test content');
    });
  });

  it('displays loading state during analysis', async () => {
    // Delay the mock resolution to test loading state
    (aiDetection.detectAIContent as jest.Mock).mockImplementation(
      () =>
        new Promise(resolve =>
          setTimeout(
            () =>
              resolve({
                score: 0.3,
                isAIGenerated: false,
                confidence: 'high',
                explanation: 'This content appears to be human-written.',
              }),
            100
          )
        )
    );

    render(<AIDetector data-oid="jb4d13g" />);

    const textarea = screen.getByPlaceholderText('Enter text to analyze...');
    fireEvent.change(textarea, { target: { value: 'Test content' } });

    const analyzeButton = screen.getByRole('button', { name: /analyze/i });
    fireEvent.click(analyzeButton);

    expect(screen.getByText(/analyzing/i)).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.queryByText(/analyzing/i)).not.toBeInTheDocument();
    });
  });

  it('displays human-written content results correctly', async () => {
    (aiDetection.detectAIContent as jest.Mock).mockResolvedValue({
      score: 0.2,
      isAIGenerated: false,
      confidence: 'high',
      explanation: 'This content appears to be human-written.',
    });

    render(<AIDetector data-oid="_91a-5c" />);

    const textarea = screen.getByPlaceholderText('Enter text to analyze...');
    fireEvent.change(textarea, { target: { value: 'Test human content' } });

    const analyzeButton = screen.getByRole('button', { name: /analyze/i });
    fireEvent.click(analyzeButton);

    await waitFor(() => {
      expect(screen.getByText(/human-written/i)).toBeInTheDocument();
      expect(screen.getByText(/20%/i)).toBeInTheDocument(); // 0.2 = 20%
    });
  });

  it('displays AI-generated content results correctly', async () => {
    (aiDetection.detectAIContent as jest.Mock).mockResolvedValue({
      score: 0.8,
      isAIGenerated: true,
      confidence: 'high',
      explanation: 'This content appears to be AI-generated.',
    });

    render(<AIDetector data-oid="my8q6xu" />);

    const textarea = screen.getByPlaceholderText('Enter text to analyze...');
    fireEvent.change(textarea, { target: { value: 'Test AI content' } });

    const analyzeButton = screen.getByRole('button', { name: /analyze/i });
    fireEvent.click(analyzeButton);

    await waitFor(() => {
      expect(screen.getByText(/ai-generated/i)).toBeInTheDocument();
      expect(screen.getByText(/80%/i)).toBeInTheDocument(); // 0.8 = 80%
    });
  });

  it('handles errors during analysis', async () => {
    (aiDetection.detectAIContent as jest.Mock).mockRejectedValue(new Error('Analysis failed'));

    render(<AIDetector data-oid="l8lq5wk" />);

    const textarea = screen.getByPlaceholderText('Enter text to analyze...');
    fireEvent.change(textarea, { target: { value: 'Test content' } });

    const analyzeButton = screen.getByRole('button', { name: /analyze/i });
    fireEvent.click(analyzeButton);

    await waitFor(() => {
      expect(screen.getByText(/error/i)).toBeInTheDocument();
    });
  });
});
