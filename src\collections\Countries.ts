import type { CollectionConfig } from 'payload'
import { admins, adminsOnly, anyone } from './access'

export const Countries: CollectionConfig = {
  slug: 'countries',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'code', 'region', 'popular'],
  },
  access: {
    read: anyone, // Anyone can read countries (for public viewing)
    create: admins,
    update: admins,
    delete: admins,
    admin: adminsOnly,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      unique: true,
      admin: {
        description: 'Full country name (e.g., "Kuwait", "United Arab Emirates")',
      },
    },
    {
      name: 'code',
      type: 'text',
      required: true,
      unique: true,
      maxLength: 2,
      admin: {
        description: 'ISO 3166-1 alpha-2 country code (e.g., "KW", "AE")',
      },
    },
    {
      name: 'code3',
      type: 'text',
      maxLength: 3,
      admin: {
        description: 'ISO 3166-1 alpha-3 country code (e.g., "KWT", "ARE")',
      },
    },
    {
      name: 'region',
      type: 'select',
      options: [
        { label: 'Middle East', value: 'middle-east' },
        { label: 'Europe', value: 'europe' },
        { label: 'Asia', value: 'asia' },
        { label: 'North America', value: 'north-america' },
        { label: 'South America', value: 'south-america' },
        { label: 'Africa', value: 'africa' },
        { label: 'Oceania', value: 'oceania' },
      ],
      required: true,
      admin: {
        description: 'Geographic region',
      },
    },
    {
      name: 'flag',
      type: 'text',
      admin: {
        description: 'Flag emoji or image URL',
      },
    },
    {
      name: 'popular',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Mark as popular destination for featured display',
      },
    },
    {
      name: 'currency',
      type: 'group',
      fields: [
        {
          name: 'code',
          type: 'text',
          maxLength: 3,
          admin: {
            description: 'Currency code (e.g., "KWD", "USD")',
          },
        },
        {
          name: 'symbol',
          type: 'text',
          maxLength: 5,
          admin: {
            description: 'Currency symbol (e.g., "د.ك", "$")',
          },
        },
        {
          name: 'name',
          type: 'text',
          admin: {
            description: 'Currency name (e.g., "Kuwaiti Dinar", "US Dollar")',
          },
        },
      ],
      admin: {
        description: 'Local currency information',
      },
    },
    {
      name: 'timezone',
      type: 'group',
      fields: [
        {
          name: 'name',
          type: 'text',
          admin: {
            description: 'Timezone name (e.g., "Asia/Kuwait")',
          },
        },
        {
          name: 'offset',
          type: 'text',
          admin: {
            description: 'UTC offset (e.g., "+03:00")',
          },
        },
      ],
      admin: {
        description: 'Timezone information',
      },
    },
    {
      name: 'languages',
      type: 'array',
      fields: [
        {
          name: 'language',
          type: 'group',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
              admin: {
                description: 'Language name (e.g., "Arabic", "English")',
              },
            },
            {
              name: 'code',
              type: 'text',
              required: true,
              maxLength: 2,
              admin: {
                description: 'ISO 639-1 language code (e.g., "ar", "en")',
              },
            },
            {
              name: 'primary',
              type: 'checkbox',
              defaultValue: false,
              admin: {
                description: 'Is this the primary language?',
              },
            },
          ],
        },
      ],
      admin: {
        description: 'Languages spoken in this country',
      },
    },
    {
      name: 'travelInfo',
      type: 'group',
      fields: [
        {
          name: 'visaRequired',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Visa typically required for tourists',
          },
        },
        {
          name: 'emergencyNumber',
          type: 'text',
          admin: {
            description: 'Emergency services number (e.g., "112", "911")',
          },
        },
        {
          name: 'electricalOutlet',
          type: 'text',
          admin: {
            description: 'Electrical outlet type (e.g., "Type G", "Type A/B")',
          },
        },
        {
          name: 'drivingSide',
          type: 'select',
          options: [
            { label: 'Right', value: 'right' },
            { label: 'Left', value: 'left' },
          ],
          admin: {
            description: 'Which side of the road do they drive on?',
          },
        },
      ],
      admin: {
        description: 'Useful travel information',
      },
    },
    {
      name: 'networkInfo',
      type: 'group',
      fields: [
        {
          name: 'operators',
          type: 'array',
          fields: [
            {
              name: 'operator',
              type: 'group',
              fields: [
                {
                  name: 'name',
                  type: 'text',
                  required: true,
                  admin: {
                    description: 'Network operator name (e.g., "Zain", "Ooredoo")',
                  },
                },
                {
                  name: 'coverage',
                  type: 'select',
                  options: [
                    { label: 'Excellent', value: 'excellent' },
                    { label: 'Good', value: 'good' },
                    { label: 'Fair', value: 'fair' },
                    { label: 'Poor', value: 'poor' },
                  ],
                  admin: {
                    description: 'Network coverage quality',
                  },
                },
                {
                  name: 'technologies',
                  type: 'array',
                  fields: [
                    {
                      name: 'tech',
                      type: 'select',
                      options: [
                        { label: '5G', value: '5g' },
                        { label: '4G LTE', value: '4g' },
                        { label: '3G', value: '3g' },
                        { label: '2G', value: '2g' },
                      ],
                      required: true,
                    },
                  ],
                  admin: {
                    description: 'Supported network technologies',
                  },
                },
              ],
            },
          ],
          admin: {
            description: 'Mobile network operators in this country',
          },
        },
      ],
      admin: {
        description: 'Mobile network information',
      },
    },
    {
      name: 'metadata',
      type: 'group',
      fields: [
        {
          name: 'population',
          type: 'number',
          admin: {
            description: 'Country population',
          },
        },
        {
          name: 'area',
          type: 'number',
          admin: {
            description: 'Country area in square kilometers',
          },
        },
        {
          name: 'capital',
          type: 'text',
          admin: {
            description: 'Capital city',
          },
        },
        {
          name: 'coordinates',
          type: 'group',
          fields: [
            {
              name: 'latitude',
              type: 'number',
            },
            {
              name: 'longitude',
              type: 'number',
            },
          ],
          admin: {
            description: 'Geographic coordinates for map display',
          },
        },
      ],
      admin: {
        description: 'Additional country metadata',
      },
    },
  ],
  hooks: {
    beforeValidate: [
      ({ data }) => {
        // Auto-uppercase country codes
        if (data && data.code) {
          data.code = data.code.toUpperCase()
        }
        if (data && data.code3) {
          data.code3 = data.code3.toUpperCase()
        }
        return data
      },
    ],
  },
}
