'use server'

// Define the initial AI state type
const initialAIState = {
  mcp: {
    servers: [] as MCPServer[],
  },
}

interface MCPServer {
  server_name: string
  description: string
  tools: Array<{
    name: string
    description: string
    parameters: Record<string, any>
  }>
}

// Simple in-memory state management (in a real app, this would use a database)
let currentAIState = { ...initialAIState }

/**
 * Register an MCP server with the AI system
 */
export const registerMCPServer = async (server: MCPServer) => {
  const currentServers = currentAIState.mcp.servers
  const existingIndex = currentServers.findIndex(s => s.server_name === server.server_name)

  if (existingIndex >= 0) {
    // Update existing server
    currentServers[existingIndex] = server
  } else {
    // Add new server
    currentServers.push(server)
  }

  currentAIState = {
    ...currentAIState,
    mcp: {
      servers: currentServers,
    },
  }

  return { success: true, server }
}

/**
 * Execute a tool from an MCP server
 */
export const executeMCPTool = async (
  serverName: string,
  toolName: string,
  args: Record<string, any>
) => {
  const server = currentAIState.mcp.servers.find(s => s.server_name === serverName)

  if (!server) {
    throw new Error(`MCP server '${serverName}' not found`)
  }

  const tool = server.tools.find(t => t.name === toolName)
  if (!tool) {
    throw new Error(`Tool '${toolName}' not found in server '${serverName}'`)
  }

  // In a real implementation, this would execute the actual tool
  // For now, we'll return a mock response
  return {
    success: true,
    result: `Executed ${toolName} with args: ${JSON.stringify(args)}`,
    server: serverName,
    tool: toolName,
  }
}

/**
 * Get all registered MCP servers
 */
export const getMCPServers = async () => {
  return currentAIState.mcp.servers
}

/**
 * Remove an MCP server
 */
export const removeMCPServer = async (serverName: string) => {
  const currentServers = currentAIState.mcp.servers
  const filteredServers = currentServers.filter(s => s.server_name !== serverName)

  currentAIState = {
    ...currentAIState,
    mcp: {
      servers: filteredServers,
    },
  }

  return { success: true, removedServer: serverName }
}
