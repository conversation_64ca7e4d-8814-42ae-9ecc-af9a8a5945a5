import { SignUp } from '@clerk/nextjs';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Sign Up - GGsim',
  description: 'Create your GGsim account to access beta features.',
};

export default function SignUpPage() {
  return (
    <div
      className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"
      data-oid="t3mw.oy"
    >
      <div className="max-w-md w-full space-y-8" data-oid="p2jzvz2">
        <div className="text-center" data-oid="tcj_-ot">
          <h1 className="text-3xl font-bold text-gray-900 mb-2" data-oid="m21hvya">
            Join GGsim Beta
          </h1>
          <p className="text-gray-600 mb-8" data-oid="1hvfapq">
            Create your account to access beta features
          </p>
        </div>

        <div className="bg-white p-8 rounded-lg shadow-lg" data-oid="4zce2et">
          <SignUp
            appearance={{
              elements: {
                formButtonPrimary:
                  'bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md transition-colors',
                card: 'shadow-none border-0',
                headerTitle: 'text-2xl font-semibold text-gray-900',
                headerSubtitle: 'text-gray-600',
                socialButtonsBlockButton: 'border border-gray-300 hover:bg-gray-50',
                formFieldInput:
                  'border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                footerActionLink: 'text-blue-500 hover:text-blue-600',
              },
            }}
            redirectUrl="/dashboard"
            signInUrl="/sign-in"
            data-oid="xc6u2o3"
          />
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4" data-oid="99c9m-f">
          <div className="flex" data-oid="j.0y0_.">
            <div className="flex-shrink-0" data-oid="fbu59qr">
              <svg
                className="h-5 w-5 text-yellow-400"
                viewBox="0 0 20 20"
                fill="currentColor"
                data-oid="1gt:2:c"
              >
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                  data-oid="zqshc20"
                />
              </svg>
            </div>
            <div className="ml-3" data-oid="gcw25:q">
              <h3 className="text-sm font-medium text-yellow-800" data-oid="70tm7x6">
                Beta Access Required
              </h3>
              <div className="mt-2 text-sm text-yellow-700" data-oid="1o7rzz5">
                <p data-oid="pn3.:2v">
                  Registration is currently limited to approved email addresses. If you don't have
                  access, please contact our team.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
