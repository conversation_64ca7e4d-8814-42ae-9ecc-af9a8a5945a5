'use client'

import { loadStripe, Stripe } from '@stripe/stripe-js'

// Make sure to call `loadStripe` outside of a component's render to avoid
// recreating the `Stripe` object on every render.
let stripePromise: Promise<Stripe | null>

export const getStripe = () => {
  if (!stripePromise) {
    if (!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY) {
      throw new Error('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is not set')
    }
    
    stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY)
  }
  
  return stripePromise
}

// Helper function to create payment intent on the client side
export async function createPaymentIntent({
  amount,
  esimPlanId,
  metadata = {},
}: {
  amount: number
  esimPlanId: string
  metadata?: Record<string, string>
}) {
  const response = await fetch('/api/payments/create-intent', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      amount,
      esimPlanId,
      metadata,
    }),
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to create payment intent')
  }

  return response.json()
}

// Helper function to confirm payment on the client side
export async function confirmPayment({
  stripe,
  clientSecret,
  paymentMethod,
  returnUrl,
}: {
  stripe: Stripe
  clientSecret: string
  paymentMethod?: any
  returnUrl?: string
}) {
  const result = await stripe.confirmPayment({
    clientSecret,
    confirmParams: {
      payment_method: paymentMethod,
      return_url: returnUrl || `${window.location.origin}/my-esims`,
    },
  })

  return result
}

// Helper function to retrieve payment intent status
export async function getPaymentIntentStatus(paymentIntentId: string) {
  const response = await fetch(
    `/api/payments/create-intent?payment_intent_id=${paymentIntentId}`,
    {
      method: 'GET',
    }
  )

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to get payment status')
  }

  return response.json()
}

// Types for client-side usage
export interface PaymentIntentResponse {
  success: boolean
  paymentIntent: {
    id: string
    clientSecret: string
    amount: number
    currency: string
    status: string
  }
  order: {
    id: string
    status: string
    paymentStatus: string
  }
}

export interface PaymentError {
  error: string
  code?: string
}

// Stripe Elements appearance configuration
export const stripeElementsOptions = {
  appearance: {
    theme: 'stripe' as const,
    variables: {
      colorPrimary: '#10b981', // Emerald-500
      colorBackground: '#ffffff',
      colorText: '#1f2937', // Gray-800
      colorDanger: '#ef4444', // Red-500
      fontFamily: 'system-ui, sans-serif',
      spacingUnit: '4px',
      borderRadius: '8px',
    },
    rules: {
      '.Input': {
        border: '1px solid #d1d5db', // Gray-300
        borderRadius: '8px',
        padding: '12px',
        fontSize: '16px',
      },
      '.Input:focus': {
        borderColor: '#10b981', // Emerald-500
        boxShadow: '0 0 0 2px rgba(16, 185, 129, 0.1)',
      },
      '.Label': {
        fontSize: '14px',
        fontWeight: '500',
        color: '#374151', // Gray-700
        marginBottom: '4px',
      },
    },
  },
  loader: 'auto' as const,
}

// Payment method types we support
export const SUPPORTED_PAYMENT_METHODS = [
  'card',
  'apple_pay',
  'google_pay',
] as const

// Currency formatting helper
export function formatCurrency(
  amount: number,
  currency: string = 'USD'
): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase(),
  }).format(amount)
}

// Helper to validate card details
export function validateCardDetails(card: any): boolean {
  return card && card.complete && !card.error
}

// Helper to handle Stripe errors
export function getStripeErrorMessage(error: any): string {
  switch (error.code) {
    case 'card_declined':
      return 'Your card was declined. Please try a different payment method.'
    case 'expired_card':
      return 'Your card has expired. Please use a different card.'
    case 'incorrect_cvc':
      return 'Your card\'s security code is incorrect.'
    case 'processing_error':
      return 'An error occurred while processing your card. Please try again.'
    case 'incorrect_number':
      return 'Your card number is incorrect.'
    case 'incomplete_number':
      return 'Your card number is incomplete.'
    case 'incomplete_cvc':
      return 'Your card\'s security code is incomplete.'
    case 'incomplete_expiry':
      return 'Your card\'s expiration date is incomplete.'
    case 'insufficient_funds':
      return 'Your card has insufficient funds.'
    case 'invalid_expiry_month':
      return 'Your card\'s expiration month is invalid.'
    case 'invalid_expiry_year':
      return 'Your card\'s expiration year is invalid.'
    case 'invalid_number':
      return 'Your card number is invalid.'
    case 'postal_code_invalid':
      return 'Your postal code is invalid.'
    default:
      return error.message || 'An unexpected error occurred. Please try again.'
  }
}
