'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { SiteHeader } from '@/components/site-header';

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    if (!email) {
      setError('Email address is required');
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await fetch('/api/users/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
        }),
      });

      if (response.ok) {
        setSuccess(true);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Failed to send reset email. Please try again.');
      }
    } catch (err) {
      setError('Failed to send reset email. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (success) {
    return (
      <div
        className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4"
        data-oid="28q34w8"
      >
        <div className="max-w-md w-full space-y-8" data-oid="m5dt:.d">
          <SiteHeader title="Check your email" data-oid="slw19_l" />

          {/* Success Message */}
          <Card data-oid="zlckq2s">
            <CardHeader data-oid="mnlnh5t">
              <CardTitle data-oid="4d9ahdn">Reset link sent</CardTitle>
              <CardDescription data-oid=".aoz1:b">
                If an account with that email exists, we&apos;ve sent a password reset link to{' '}
                {email}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4" data-oid="me3gen_">
              <Alert data-oid="pf6b-yp">
                <AlertDescription data-oid="o_ozhe6">
                  Please check your email and follow the instructions to reset your password. The
                  link will expire in 1 hour.
                </AlertDescription>
              </Alert>

              <div className="space-y-2" data-oid="78d-f:q">
                <Link href="/login" data-oid="7j20rxz">
                  <Button className="w-full" data-oid="n1cuyuo">
                    Back to sign in
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          {/* Back to Home */}
          <div className="text-center" data-oid="4cu9etc">
            <Link href="/" className="text-sm text-gray-600 hover:text-gray-900" data-oid="028pgc:">
              ← Back to home
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4"
      data-oid="bp9::ys"
    >
      <div className="max-w-md w-full space-y-8" data-oid="33cajd.">
        <SiteHeader
          title="Reset your password"
          subtitle={
            <>
              Remember your password?{' '}
              <Link
                href="/login"
                className="font-medium text-green-600 hover:text-green-500"
                data-oid="8iqy_9w"
              >
                Sign in
              </Link>
            </>
          }
          data-oid="901lb:s"
        />

        {/* Forgot Password Form */}
        <Card data-oid="9k0b3v5">
          <CardHeader data-oid="b4bwyus">
            <CardTitle data-oid="nixazt_">Enter your email</CardTitle>
            <CardDescription data-oid="reyzo.a">
              We&apos;ll send you a link to reset your password
            </CardDescription>
          </CardHeader>
          <CardContent data-oid="evzmviz">
            <form onSubmit={handleSubmit} className="space-y-4" data-oid="-wlx3gd">
              {error && (
                <Alert variant="destructive" data-oid="84b4l6h">
                  <AlertDescription data-oid="mz84tw.">{error}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2" data-oid="ptv98qy">
                <label
                  htmlFor="email"
                  className="text-sm font-medium text-gray-700"
                  data-oid="5628c9."
                >
                  Email address
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  required
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  data-oid="m41bgtx"
                />
              </div>

              <Button type="submit" className="w-full" disabled={isSubmitting} data-oid="db--ql6">
                {isSubmitting ? 'Sending...' : 'Send reset link'}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Back to Home */}
        <div className="text-center" data-oid="_h:v9qj">
          <Link href="/" className="text-sm text-gray-600 hover:text-gray-900" data-oid="fuv_neq">
            ← Back to home
          </Link>
        </div>
      </div>
    </div>
  );
}
