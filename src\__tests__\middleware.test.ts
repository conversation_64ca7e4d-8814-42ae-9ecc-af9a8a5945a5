import { NextRequest, NextResponse } from 'next/server';
import { middleware } from '../middleware';
import * as securityEnhancements from '@/lib/security-enhancements';

// Mock the security-enhancements module
jest.mock('@/lib/security-enhancements', () => ({
  applySecurityHeaders: jest.fn((response) => response),
}));

// Mock NextResponse
jest.mock('next/server', () => ({
  NextResponse: {
    next: jest.fn(() => ({ headers: new Map() })),
  },
}));

describe('Middleware', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('applies security headers to non-excluded paths', async () => {
    // Create a mock request for a regular page
    const request = {
      nextUrl: { pathname: '/some-page' },
    } as unknown as NextRequest;

    await middleware(request);

    // Check that NextResponse.next was called
    expect(NextResponse.next).toHaveBeenCalled();
    
    // Check that applySecurityHeaders was called
    expect(securityEnhancements.applySecurityHeaders).toHaveBeenCalled();
  });

  it('skips API routes', async () => {
    // Create a mock request for an API route
    const request = {
      nextUrl: { pathname: '/api/some-endpoint' },
    } as unknown as NextRequest;

    await middleware(request);

    // Check that NextResponse.next was called
    expect(NextResponse.next).toHaveBeenCalled();
    
    // Check that applySecurityHeaders was NOT called for API routes
    expect(securityEnhancements.applySecurityHeaders).not.toHaveBeenCalled();
  });

  it('skips static files', async () => {
    // Create a mock request for a static file
    const request = {
      nextUrl: { pathname: '/_next/static/chunks/main.js' },
    } as unknown as NextRequest;

    await middleware(request);

    // Check that NextResponse.next was called
    expect(NextResponse.next).toHaveBeenCalled();
    
    // Check that applySecurityHeaders was NOT called for static files
    expect(securityEnhancements.applySecurityHeaders).not.toHaveBeenCalled();
  });

  it('skips image optimization files', async () => {
    // Create a mock request for an image optimization file
    const request = {
      nextUrl: { pathname: '/_next/image' },
    } as unknown as NextRequest;

    await middleware(request);

    // Check that NextResponse.next was called
    expect(NextResponse.next).toHaveBeenCalled();
    
    // Check that applySecurityHeaders was NOT called for image optimization files
    expect(securityEnhancements.applySecurityHeaders).not.toHaveBeenCalled();
  });
});