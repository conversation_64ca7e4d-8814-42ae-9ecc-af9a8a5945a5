import { <PERSON>ada<PERSON> } from 'next'
import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Lock, ArrowLeft, Shield } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Unauthorized - GGsim',
  description: 'You do not have the required permissions to access this page.',
}

export default function UnauthorizedPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="max-w-md w-full text-center">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="flex justify-center mb-6">
            <Lock className="h-16 w-16 text-orange-500" />
          </div>
          
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Unauthorized Access
          </h1>
          
          <p className="text-gray-600 mb-6">
            You don't have the required permissions to access this page. 
            This area is restricted to users with specific roles.
          </p>
          
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <Shield className="h-5 w-5 text-orange-500 mt-0.5 mr-3 flex-shrink-0" />
              <div className="text-left">
                <h3 className="text-sm font-medium text-orange-800 mb-1">
                  Role-Based Access Control
                </h3>
                <p className="text-sm text-orange-700">
                  Different areas of the platform require different permission levels:
                </p>
                <ul className="text-sm text-orange-700 mt-2 space-y-1">
                  <li>• <strong>Beta Tester:</strong> Core features access</li>
                  <li>• <strong>Developer:</strong> Debug tools and advanced features</li>
                  <li>• <strong>Admin:</strong> Full platform management</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="space-y-3">
            <Link href="/dashboard">
              <Button className="w-full flex items-center justify-center">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
            
            <Link href="/">
              <Button variant="outline" className="w-full">
                Go to Home
              </Button>
            </Link>
          </div>
          
          <div className="mt-6 pt-6 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              If you need elevated permissions, please contact your administrator.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
