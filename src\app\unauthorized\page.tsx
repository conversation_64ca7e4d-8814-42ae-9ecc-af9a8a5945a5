import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Lock, ArrowLeft, Shield } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Unauthorized - GGsim',
  description: 'You do not have the required permissions to access this page.',
};

export default function UnauthorizedPage() {
  return (
    <div
      className="min-h-screen flex items-center justify-center bg-gray-50 px-4"
      data-oid="3r85u19"
    >
      <div className="max-w-md w-full text-center" data-oid="co82kq9">
        <div className="bg-white rounded-lg shadow-lg p-8" data-oid="1msds6b">
          <div className="flex justify-center mb-6" data-oid="7h_jpvp">
            <Lock className="h-16 w-16 text-orange-500" data-oid="mk0s7k0" />
          </div>

          <h1 className="text-2xl font-bold text-gray-900 mb-4" data-oid="crcld9q">
            Unauthorized Access
          </h1>

          <p className="text-gray-600 mb-6" data-oid="1gcmq:0">
            You don't have the required permissions to access this page. This area is restricted to
            users with specific roles.
          </p>

          <div
            className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6"
            data-oid="ac7o5p1"
          >
            <div className="flex items-start" data-oid="-jmgvge">
              <Shield
                className="h-5 w-5 text-orange-500 mt-0.5 mr-3 flex-shrink-0"
                data-oid="quc5vsx"
              />

              <div className="text-left" data-oid="qf__ahz">
                <h3 className="text-sm font-medium text-orange-800 mb-1" data-oid="bvpqvex">
                  Role-Based Access Control
                </h3>
                <p className="text-sm text-orange-700" data-oid="dj825zg">
                  Different areas of the platform require different permission levels:
                </p>
                <ul className="text-sm text-orange-700 mt-2 space-y-1" data-oid="..9g61j">
                  <li data-oid="e2-p.-k">
                    • <strong data-oid="8j.6j4c">Beta Tester:</strong> Core features access
                  </li>
                  <li data-oid="t06j44_">
                    • <strong data-oid="xe1w5ed">Developer:</strong> Debug tools and advanced
                    features
                  </li>
                  <li data-oid="et6n2ty">
                    • <strong data-oid="cnx155y">Admin:</strong> Full platform management
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div className="space-y-3" data-oid="-aukc7x">
            <Link href="/dashboard" data-oid="0ac_bol">
              <Button className="w-full flex items-center justify-center" data-oid="u_e8r4n">
                <ArrowLeft className="h-4 w-4 mr-2" data-oid="17rptcu" />
                Back to Dashboard
              </Button>
            </Link>

            <Link href="/" data-oid="yz3c1p0">
              <Button variant="outline" className="w-full" data-oid="vom5y6o">
                Go to Home
              </Button>
            </Link>
          </div>

          <div className="mt-6 pt-6 border-t border-gray-200" data-oid="4a-4:t7">
            <p className="text-xs text-gray-500" data-oid="sblk445">
              If you need elevated permissions, please contact your administrator.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
