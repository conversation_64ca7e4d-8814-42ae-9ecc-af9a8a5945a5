import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { verifyWebhookSignature } from '@/lib/stripe'
import Stripe from 'stripe'

// This endpoint should be configured in your Stripe dashboard
// URL: https://yourdomain.com/api/payments/webhook
// Events to send: payment_intent.succeeded, payment_intent.payment_failed

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const headersList = await headers()
    const signature = headersList.get('stripe-signature')

    if (!signature) {
      console.error('Missing Stripe signature')
      return NextResponse.json(
        { error: 'Missing signature' },
        { status: 400 }
      )
    }

    if (!process.env.STRIPE_WEBHOOK_SECRET) {
      console.error('Missing STRIPE_WEBHOOK_SECRET environment variable')
      return NextResponse.json(
        { error: 'Webhook secret not configured' },
        { status: 500 }
      )
    }

    // Verify the webhook signature
    let event: Stripe.Event
    try {
      event = verifyWebhookSignature(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET
      )
    } catch (error) {
      console.error('Webhook signature verification failed:', error)
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      )
    }

    const payloadConfig = await config
    const payload = await getPayload({ config: payloadConfig })

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent, payload)
        break

      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.PaymentIntent, payload)
        break

      case 'payment_intent.canceled':
        await handlePaymentCanceled(event.data.object as Stripe.PaymentIntent, payload)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Webhook error:', error)
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    )
  }
}

async function handlePaymentSucceeded(
  paymentIntent: Stripe.PaymentIntent,
  payload: any
) {
  try {
    console.log('Payment succeeded:', paymentIntent.id)

    // Find the order associated with this payment intent
    const orders = await payload.find({
      collection: 'orders',
      where: {
        stripePaymentIntentId: {
          equals: paymentIntent.id,
        },
      },
      limit: 1,
    })

    if (orders.docs.length === 0) {
      console.error('Order not found for payment intent:', paymentIntent.id)
      return
    }

    const order = orders.docs[0]

    // Update order status to completed
    await payload.update({
      collection: 'orders',
      id: order.id,
      data: {
        status: 'completed',
        paymentStatus: 'completed',
        esimData: {
          ...order.esimData,
          activationStatus: 'ready',
        },
      },
    })

    // Trigger eSIM provisioning from the provider
    // This calls the eSIM provider's API to purchase the plan and get activation data
    await provisionESIM(order, payload)

    console.log('Order updated successfully:', order.id)
  } catch (error) {
    console.error('Error handling payment success:', error)
  }
}

async function handlePaymentFailed(
  paymentIntent: Stripe.PaymentIntent,
  payload: any
) {
  try {
    console.log('Payment failed:', paymentIntent.id)

    // Find the order associated with this payment intent
    const orders = await payload.find({
      collection: 'orders',
      where: {
        stripePaymentIntentId: {
          equals: paymentIntent.id,
        },
      },
      limit: 1,
    })

    if (orders.docs.length === 0) {
      console.error('Order not found for payment intent:', paymentIntent.id)
      return
    }

    const order = orders.docs[0]

    // Update order status to failed
    await payload.update({
      collection: 'orders',
      id: order.id,
      data: {
        status: 'cancelled',
        paymentStatus: 'failed',
      },
    })

    console.log('Order marked as failed:', order.id)
  } catch (error) {
    console.error('Error handling payment failure:', error)
  }
}

async function handlePaymentCanceled(
  paymentIntent: Stripe.PaymentIntent,
  payload: any
) {
  try {
    console.log('Payment canceled:', paymentIntent.id)

    // Find the order associated with this payment intent
    const orders = await payload.find({
      collection: 'orders',
      where: {
        stripePaymentIntentId: {
          equals: paymentIntent.id,
        },
      },
      limit: 1,
    })

    if (orders.docs.length === 0) {
      console.error('Order not found for payment intent:', paymentIntent.id)
      return
    }

    const order = orders.docs[0]

    // Update order status to cancelled
    await payload.update({
      collection: 'orders',
      id: order.id,
      data: {
        status: 'cancelled',
        paymentStatus: 'failed',
      },
    })

    console.log('Order marked as cancelled:', order.id)
  } catch (error) {
    console.error('Error handling payment cancellation:', error)
  }
}

// Placeholder function for eSIM provisioning
// This will be implemented when we add the provider integrations
async function provisionESIM(order: any, payload: any) {
  try {
    console.log('Provisioning eSIM for order:', order.id)

    // Get the eSIM plan details
    const esimPlan = await payload.findByID({
      collection: 'esim-plans',
      id: order.items[0].esimPlan,
    })

    if (!esimPlan) {
      throw new Error('eSIM plan not found')
    }

    // Call the appropriate provider API based on esimPlan.provider
    // Currently using mock data for development - integrate with actual provider APIs in production
    const mockActivationCode = `LPA:1$${esimPlan.provider}.com$${generateMockICCID()}`
    const mockICCID = generateMockICCID()

    // Calculate expiry date based on plan validity
    const expiryDate = new Date()
    expiryDate.setDate(expiryDate.getDate() + esimPlan.validity)

    // Update order with eSIM data
    await payload.update({
      collection: 'orders',
      id: order.id,
      data: {
        esimData: {
          ...order.esimData,
          activationCode: mockActivationCode,
          iccid: mockICCID,
          activationStatus: 'ready',
          expiryDate: expiryDate.toISOString(),
          providerOrderId: `${esimPlan.provider}-${Date.now()}`,
        },
      },
    })

    // Future: Send activation email to customer with QR code and instructions
    console.log('eSIM provisioned successfully for order:', order.id)
  } catch (error) {
    console.error('Error provisioning eSIM:', error)
    
    // Update order to indicate provisioning failed
    await payload.update({
      collection: 'orders',
      id: order.id,
      data: {
        esimData: {
          ...order.esimData,
          activationStatus: 'pending',
        },
      },
    })
  }
}

// Helper function to generate mock ICCID for testing
function generateMockICCID(): string {
  // ICCID format: 89 (telecom) + country code + issuer + account + check digit
  // This is just for testing - real ICCIDs come from the provider
  const prefix = '8901' // 89 + 01 (test country code)
  const issuer = '234' // Mock issuer code
  const account = Math.random().toString().slice(2, 12) // 10 digits
  const checkDigit = '0' // Simplified check digit
  
  return prefix + issuer + account + checkDigit
}
