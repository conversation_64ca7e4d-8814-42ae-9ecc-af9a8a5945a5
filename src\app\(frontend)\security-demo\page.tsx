import React from 'react';
import Link from 'next/link';

export const metadata = {
  title: 'Security Features | GGsim',
  description: 'Demonstration of security enhancements in GGsim',
};

/**
 * Demo page showcasing security enhancements
 */
export default function SecurityDemoPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Security Enhancements Demo</h1>
      
      <div className="mb-8">
        <p className="text-gray-600 mb-4">
          This page demonstrates various security enhancements implemented in the GGsim application,
          including Content Security Policy, input sanitization, and CSRF protection.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold mb-4">Content Security Policy (CSP)</h2>
          <p className="text-gray-600 mb-4">
            CSP helps prevent Cross-Site Scripting (XSS) attacks by controlling which resources can be
            loaded and executed on this page.
          </p>
          <div className="mt-4 space-y-2">
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
              <span>Restricts inline scripts</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
              <span>Controls external resource loading</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
              <span>Prevents eval() and similar functions</span>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold mb-4">Input Sanitization</h2>
          <p className="text-gray-600 mb-4">
            All user inputs are sanitized to prevent injection attacks and ensure data integrity.
          </p>
          <div className="mt-4 space-y-2">
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
              <span>HTML sanitization for rich text</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
              <span>SQL injection prevention</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
              <span>Object sanitization for API requests</span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold mb-4">CSRF Protection</h2>
          <p className="text-gray-600 mb-4">
            Cross-Site Request Forgery protection ensures that only legitimate requests from our
            application are processed.
          </p>
          <div className="mt-4 space-y-2">
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
              <span>Token validation for state-changing operations</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
              <span>Same-site cookie policies</span>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold mb-4">Security Headers</h2>
          <p className="text-gray-600 mb-4">
            HTTP security headers provide an additional layer of protection against various attacks.
          </p>
          <div className="mt-4 space-y-2">
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
              <span>Strict-Transport-Security (HSTS)</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
              <span>X-Content-Type-Options: nosniff</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
              <span>X-Frame-Options: DENY</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
              <span>Referrer-Policy: strict-origin-when-cross-origin</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
              <span>Permissions-Policy</span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-red-50 rounded-lg p-6 mb-8">
        <h2 className="text-xl font-bold text-red-700 mb-4">Security Best Practices</h2>
        <ul className="list-disc pl-5 space-y-2 text-red-600">
          <li>Implement proper authentication and authorization</li>
          <li>Use HTTPS for all communications</li>
          <li>Sanitize all user inputs</li>
          <li>Implement Content Security Policy</li>
          <li>Use security headers</li>
          <li>Protect against CSRF attacks</li>
          <li>Keep dependencies updated</li>
          <li>Implement rate limiting</li>
          <li>Use secure cookies</li>
          <li>Validate file uploads</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold mb-4">Learn More</h2>
        <p className="text-gray-600 mb-4">
          Explore our other feature demos:
        </p>
        <div className="flex flex-wrap gap-4">
          <Link href="/performance-demo" className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
            Performance Demo
          </Link>
          <Link href="/ai-features" className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors">
            AI Features
          </Link>
        </div>
      </div>
    </div>
  );
}