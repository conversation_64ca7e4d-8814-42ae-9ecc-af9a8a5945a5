import React from 'react';
import Link from 'next/link';

export const metadata = {
  title: 'Security Features | GGsim',
  description: 'Demonstration of security enhancements in GGsim',
};

/**
 * Demo page showcasing security enhancements
 */
export default function SecurityDemoPage() {
  return (
    <div className="container mx-auto px-4 py-8" data-oid="nbktqak">
      <h1 className="text-3xl font-bold mb-6" data-oid="enz2_uq">
        Security Enhancements Demo
      </h1>

      <div className="mb-8" data-oid="662n72l">
        <p className="text-gray-600 mb-4" data-oid="wass-17">
          This page demonstrates various security enhancements implemented in the GGsim application,
          including Content Security Policy, input sanitization, and CSRF protection.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12" data-oid=".66-dct">
        <div className="bg-white rounded-lg shadow-md p-6" data-oid="gsrofc-">
          <h2 className="text-xl font-bold mb-4" data-oid="t9p79rt">
            Content Security Policy (CSP)
          </h2>
          <p className="text-gray-600 mb-4" data-oid="fakzs4g">
            CSP helps prevent Cross-Site Scripting (XSS) attacks by controlling which resources can
            be loaded and executed on this page.
          </p>
          <div className="mt-4 space-y-2" data-oid="j6jqx7-">
            <div className="flex items-center" data-oid="y79au.p">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2" data-oid="h0xji8j"></div>
              <span data-oid="ikguw4x">Restricts inline scripts</span>
            </div>
            <div className="flex items-center" data-oid="8iub2us">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2" data-oid="wj1am8j"></div>
              <span data-oid="ug2i_pw">Controls external resource loading</span>
            </div>
            <div className="flex items-center" data-oid="66ifdrt">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2" data-oid="z6ojv18"></div>
              <span data-oid="ngo:p33">Prevents eval() and similar functions</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6" data-oid="_x_5l-6">
          <h2 className="text-xl font-bold mb-4" data-oid="gmxkj24">
            Input Sanitization
          </h2>
          <p className="text-gray-600 mb-4" data-oid="p05fug5">
            All user inputs are sanitized to prevent injection attacks and ensure data integrity.
          </p>
          <div className="mt-4 space-y-2" data-oid="7p:p:8_">
            <div className="flex items-center" data-oid="j0hs6qm">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2" data-oid="na9nvz1"></div>
              <span data-oid="09endm9">HTML sanitization for rich text</span>
            </div>
            <div className="flex items-center" data-oid="w.9i9w4">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2" data-oid="tdfx7v0"></div>
              <span data-oid="qr274k:">SQL injection prevention</span>
            </div>
            <div className="flex items-center" data-oid="zeo3mk3">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2" data-oid="1o:.hsd"></div>
              <span data-oid="y:aryqd">Object sanitization for API requests</span>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12" data-oid="t4jeo:5">
        <div className="bg-white rounded-lg shadow-md p-6" data-oid="tm74-wl">
          <h2 className="text-xl font-bold mb-4" data-oid="u3-3szw">
            CSRF Protection
          </h2>
          <p className="text-gray-600 mb-4" data-oid="b6:54fh">
            Cross-Site Request Forgery protection ensures that only legitimate requests from our
            application are processed.
          </p>
          <div className="mt-4 space-y-2" data-oid="e3xq9gc">
            <div className="flex items-center" data-oid="emwyieo">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2" data-oid="b0ia3p2"></div>
              <span data-oid="t45l6e3">Token validation for state-changing operations</span>
            </div>
            <div className="flex items-center" data-oid="2ao.-iv">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2" data-oid="-xxso2e"></div>
              <span data-oid="ittzyw0">Same-site cookie policies</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6" data-oid="abfl2-h">
          <h2 className="text-xl font-bold mb-4" data-oid="bepbiv9">
            Security Headers
          </h2>
          <p className="text-gray-600 mb-4" data-oid="_4un5z_">
            HTTP security headers provide an additional layer of protection against various attacks.
          </p>
          <div className="mt-4 space-y-2" data-oid="nxwem4t">
            <div className="flex items-center" data-oid="q2_7ccq">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2" data-oid="415l0e5"></div>
              <span data-oid="k_7.jtn">Strict-Transport-Security (HSTS)</span>
            </div>
            <div className="flex items-center" data-oid="ntff.35">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2" data-oid="mm7j6xj"></div>
              <span data-oid="j4j73-3">X-Content-Type-Options: nosniff</span>
            </div>
            <div className="flex items-center" data-oid="19q8d8k">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2" data-oid="mdqaw:8"></div>
              <span data-oid="dzaf9x7">X-Frame-Options: DENY</span>
            </div>
            <div className="flex items-center" data-oid="3s4al3z">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2" data-oid="n3d6r2i"></div>
              <span data-oid="90bea5e">Referrer-Policy: strict-origin-when-cross-origin</span>
            </div>
            <div className="flex items-center" data-oid="cm1d7d4">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2" data-oid="c-z1ng5"></div>
              <span data-oid="w:whhi1">Permissions-Policy</span>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-red-50 rounded-lg p-6 mb-8" data-oid="ykklfa5">
        <h2 className="text-xl font-bold text-red-700 mb-4" data-oid="b17-a8p">
          Security Best Practices
        </h2>
        <ul className="list-disc pl-5 space-y-2 text-red-600" data-oid="mge1q8v">
          <li data-oid="r4cinmb">Implement proper authentication and authorization</li>
          <li data-oid="-9im07e">Use HTTPS for all communications</li>
          <li data-oid="41rvj7i">Sanitize all user inputs</li>
          <li data-oid="jw9loec">Implement Content Security Policy</li>
          <li data-oid="p.r3n1b">Use security headers</li>
          <li data-oid="x.bmffu">Protect against CSRF attacks</li>
          <li data-oid="9hn-_sy">Keep dependencies updated</li>
          <li data-oid="wvyt0ef">Implement rate limiting</li>
          <li data-oid="j0-evdx">Use secure cookies</li>
          <li data-oid="8iwrikk">Validate file uploads</li>
        </ul>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6" data-oid="trj0g2_">
        <h2 className="text-xl font-bold mb-4" data-oid="hgmmr3q">
          Learn More
        </h2>
        <p className="text-gray-600 mb-4" data-oid="-tbgm4q">
          Explore our other feature demos:
        </p>
        <div className="flex flex-wrap gap-4" data-oid="xlt5e5f">
          <Link
            href="/performance-demo"
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            data-oid="4q6ajc:"
          >
            Performance Demo
          </Link>
          <Link
            href="/ai-features"
            className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors"
            data-oid="zqzyd-8"
          >
            AI Features
          </Link>
        </div>
      </div>
    </div>
  );
}
