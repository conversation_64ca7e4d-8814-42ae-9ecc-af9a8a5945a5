import React from 'react';
import { PerformanceDashboard } from '@/components/admin/PerformanceDashboard';

export const metadata = {
  title: 'Performance Monitoring | Admin Dashboard',
  description: 'Monitor and analyze application performance metrics',
};

/**
 * Admin page for performance monitoring
 */
export default function PerformanceMonitoringPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Performance Monitoring</h1>
      
      <div className="mb-8">
        <p className="text-gray-600 mb-4">
          This dashboard provides real-time insights into your application's performance metrics,
          helping you identify and address performance bottlenecks.
        </p>
      </div>
      
      <PerformanceDashboard />
      
      <div className="mt-12 grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold mb-4">Performance Recommendations</h2>
          <ul className="list-disc pl-5 space-y-2">
            <li>Optimize image loading with next/image component</li>
            <li>Implement code splitting for large components</li>
            <li>Minimize JavaScript bundle size</li>
            <li>Use server components where appropriate</li>
            <li>Implement proper caching strategies</li>
          </ul>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold mb-4">Resource Usage</h2>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium">Memory Usage</span>
                <span className="text-sm font-medium">65%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: '65%' }}></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium">CPU Usage</span>
                <span className="text-sm font-medium">40%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div className="bg-green-500 h-2.5 rounded-full" style={{ width: '40%' }}></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium">Bandwidth</span>
                <span className="text-sm font-medium">28%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div className="bg-purple-500 h-2.5 rounded-full" style={{ width: '28%' }}></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}