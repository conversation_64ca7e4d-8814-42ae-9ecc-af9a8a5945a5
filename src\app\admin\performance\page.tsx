import React from 'react';
import { PerformanceDashboard } from '@/components/admin/PerformanceDashboard';

export const metadata = {
  title: 'Performance Monitoring | Admin Dashboard',
  description: 'Monitor and analyze application performance metrics',
};

/**
 * Admin page for performance monitoring
 */
export default function PerformanceMonitoringPage() {
  return (
    <div className="container mx-auto px-4 py-8" data-oid="fp3ba3v">
      <h1 className="text-3xl font-bold mb-8" data-oid="76n_cfm">
        Performance Monitoring
      </h1>

      <div className="mb-8" data-oid="hy6-.ny">
        <p className="text-gray-600 mb-4" data-oid="wd2_6bu">
          This dashboard provides real-time insights into your application's performance metrics,
          helping you identify and address performance bottlenecks.
        </p>
      </div>

      <PerformanceDashboard data-oid=".t3rkup" />

      <div className="mt-12 grid grid-cols-1 md:grid-cols-2 gap-8" data-oid="lwv65c8">
        <div className="bg-white rounded-lg shadow-md p-6" data-oid="nfkd8z7">
          <h2 className="text-xl font-bold mb-4" data-oid="93ndc50">
            Performance Recommendations
          </h2>
          <ul className="list-disc pl-5 space-y-2" data-oid="2rebl4m">
            <li data-oid="7owqc47">Optimize image loading with next/image component</li>
            <li data-oid="tjtgali">Implement code splitting for large components</li>
            <li data-oid="iw2k4a1">Minimize JavaScript bundle size</li>
            <li data-oid="r4mha8g">Use server components where appropriate</li>
            <li data-oid="693vs5q">Implement proper caching strategies</li>
          </ul>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6" data-oid="eu_.f:b">
          <h2 className="text-xl font-bold mb-4" data-oid="bieo:al">
            Resource Usage
          </h2>
          <div className="space-y-4" data-oid="tz-69x6">
            <div data-oid="7x46u2s">
              <div className="flex justify-between mb-1" data-oid="-4nbrnc">
                <span className="text-sm font-medium" data-oid="hmr1p9m">
                  Memory Usage
                </span>
                <span className="text-sm font-medium" data-oid="blx_zb7">
                  65%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5" data-oid="dojw5gp">
                <div
                  className="bg-blue-600 h-2.5 rounded-full"
                  style={{ width: '65%' }}
                  data-oid="r36s2jr"
                ></div>
              </div>
            </div>

            <div data-oid="1xvn4_b">
              <div className="flex justify-between mb-1" data-oid="aazovjx">
                <span className="text-sm font-medium" data-oid="0xpt-pu">
                  CPU Usage
                </span>
                <span className="text-sm font-medium" data-oid="aw6iqtr">
                  40%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5" data-oid="hv23j6e">
                <div
                  className="bg-green-500 h-2.5 rounded-full"
                  style={{ width: '40%' }}
                  data-oid="1k4flkm"
                ></div>
              </div>
            </div>

            <div data-oid="i9:99:v">
              <div className="flex justify-between mb-1" data-oid="q2ka7_h">
                <span className="text-sm font-medium" data-oid="9pqtbgp">
                  Bandwidth
                </span>
                <span className="text-sm font-medium" data-oid="h0_:bi1">
                  28%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5" data-oid="-34brjs">
                <div
                  className="bg-purple-500 h-2.5 rounded-full"
                  style={{ width: '28%' }}
                  data-oid="76vgug7"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
