{"name": "ggsim", "version": "1.0.0", "private": true, "description": "GGsim Portal Mini Store Template", "type": "module", "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "analyze": "ANALYZE=true next build", "clean": "rimraf .next out dist", "postinstall": "echo 'Installation complete'", "test:e2e:debug": "playwright test --debug", "deploy:preview": "vercel", "deploy:production": "vercel --prod", "deploy:beta": "bash scripts/deploy-beta.sh preview", "deploy:beta:prod": "bash scripts/deploy-beta.sh production", "seed": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload seed", "generate:types": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload generate:types", "migrate": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload migrate", "prepare": "husky install"}, "dependencies": {"@clerk/nextjs": "^6.31.1", "@heroicons/react": "^2.2.0", "@payloadcms/db-vercel-postgres": "^3.52.0", "@payloadcms/email-nodemailer": "^3.52.0", "@payloadcms/next": "^3.52.0", "@payloadcms/richtext-lexical": "^3.52.0", "@payloadcms/ui": "^3.52.0", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@sentry/nextjs": "^10.5.0", "@stripe/stripe-js": "^2.4.0", "@supabase/supabase-js": "^2.55.0", "@vercel/analytics": "^1.5.0", "@vercel/flags": "^3.1.1", "@vercel/speed-insights": "^1.2.0", "ai": "^5.0.15", "axios": "^1.6.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^1.1.1", "critters": "^0.0.23", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.12", "graphql": "^16.11.0", "input-otp": "^1.4.2", "lucide-react": "^0.294.0", "next": "^15.4.6", "next-intl": "^3.0.0", "next-themes": "^0.4.6", "node-cache": "^5.1.2", "nodemailer": "^6.9.7", "payload": "^3.52.0", "qrcode": "^1.5.3", "react": "^19.1.1", "react-day-picker": "^9.8.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-resizable-panels": "^3.0.4", "react-simple-maps": "^3.0.0", "recharts": "^3.1.2", "sharp": "^0.33.0", "stripe": "^14.25.0", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.4", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.10", "@types/node": "^20.10.4", "@types/node-cache": "^4.2.5", "@types/nodemailer": "^6.4.14", "@types/qrcode": "^1.5.5", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^8.55.0", "eslint-config-next": "^14.0.4", "eslint-plugin-react": "^7.33.2", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.0", "next-router-mock": "^1.0.2", "postcss": "^8.5.6", "prettier": "^3.1.0", "tailwindcss": "^3.4.17", "ts-jest": "^29.1.1", "typescript": "^5.3.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix --max-warnings=0", "prettier --write"], "*.{json,css,scss,md,mdx,html,yml,yaml}": ["prettier --write"]}, "engines": {"node": ">=18.0.0"}}