/**
 * Application logger utility
 * Provides consistent logging across the application with different log levels
 * and contextual information
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogOptions {
  context?: string;
  data?: Record<string, any>;
  timestamp?: boolean;
}

const defaultOptions: LogOptions = {
  timestamp: true,
};

class Logger {
  private isProduction = process.env.NODE_ENV === 'production';
  private isTest = process.env.NODE_ENV === 'test';

  /**
   * Log a debug message (only in non-production environments)
   */
  debug(message: string, options: LogOptions = {}) {
    if (this.isProduction) return;
    this.log('debug', message, options);
  }

  /**
   * Log an info message
   */
  info(message: string, options: LogOptions = {}) {
    this.log('info', message, options);
  }

  /**
   * Log a warning message
   */
  warn(message: string, options: LogOptions = {}) {
    this.log('warn', message, options);
  }

  /**
   * Log an error message
   */
  error(message: string | Error, options: LogOptions = {}) {
    const errorMessage = message instanceof Error ? message.message : message;
    const errorStack = message instanceof Error ? message.stack : undefined;
    
    const enhancedOptions = {
      ...options,
      data: {
        ...(options.data || {}),
        ...(errorStack ? { stack: errorStack } : {}),
      },
    };
    
    this.log('error', errorMessage, enhancedOptions);
  }

  /**
   * Internal logging method
   */
  private log(level: LogLevel, message: string, options: LogOptions = {}) {
    const mergedOptions = { ...defaultOptions, ...options };
    const { context, data, timestamp } = mergedOptions;

    // Skip logging in test environment unless explicitly enabled
    if (this.isTest && process.env.ENABLE_TEST_LOGS !== 'true') return;

    const logObject: Record<string, any> = {
      level,
      message,
    };

    if (context) logObject.context = context;
    if (timestamp) logObject.timestamp = new Date().toISOString();
    if (data) logObject.data = data;

    // In production, format as JSON for better log processing
    if (this.isProduction) {
      console[level === 'debug' ? 'log' : level](JSON.stringify(logObject));
    } else {
      // In development, format for better readability
      const prefix = timestamp ? `[${new Date().toISOString()}]` : '';
      const contextStr = context ? `[${context}]` : '';
      const levelUppercase = level.toUpperCase();
      
      console[level === 'debug' ? 'log' : level](
        `${prefix} ${levelUppercase} ${contextStr} ${message}`,
        data ? data : ''
      );
    }
  }
}

// Export a singleton instance
export const logger = new Logger();