'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Order } from '@/payload-types'; // Import Order type for currentStatus

interface OrderStatusUpdateProps {
  orderId: number; // Assuming Payload IDs are numbers
  currentStatus: Order['status'];
}

const OrderStatusUpdate: React.FC<OrderStatusUpdateProps> = ({ orderId, currentStatus }) => {
  const [status, setStatus] = useState(currentStatus);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const handleStatusUpdate = async (newStatus: Order['status']) => {
    if (newStatus === status) return;

    setIsUpdating(true);
    setError('');

    try {
      const response = await fetch('/api/orders/update-status', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId,
          status: newStatus,
        }),
      });

      if (response.ok) {
        setStatus(newStatus);
        router.refresh(); // Refresh the page to show updated data
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Failed to update status');
      }
    } catch (err) {
      setError('Failed to update status. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="space-y-3" data-oid="1.-f75y">
      <div className="flex gap-2 flex-wrap" data-oid="lxva5b1">
        <Button
          onClick={() => handleStatusUpdate('pending')}
          disabled={isUpdating || status === 'pending'}
          variant={status === 'pending' ? 'default' : 'outline'}
          size="sm"
          data-oid="0-._iyt"
        >
          Pending
        </Button>
        <Button
          onClick={() => handleStatusUpdate('completed')}
          disabled={isUpdating || status === 'completed'}
          variant={status === 'completed' ? 'default' : 'outline'}
          size="sm"
          data-oid="hxwuc6."
        >
          Completed
        </Button>
        <Button
          onClick={() => handleStatusUpdate('cancelled')}
          disabled={isUpdating || status === 'cancelled'}
          variant={status === 'cancelled' ? 'destructive' : 'outline'}
          size="sm"
          data-oid="8ns632k"
        >
          Cancelled
        </Button>
      </div>
      {error && (
        <Alert variant="destructive" data-oid="wiwen73">
          <AlertDescription data-oid=".np9hp5">{error}</AlertDescription>
        </Alert>
      )}
      {isUpdating && (
        <div className="text-sm text-gray-500" data-oid="pgo3r:e">
          Updating...
        </div>
      )}
    </div>
  );
};

export default OrderStatusUpdate;
