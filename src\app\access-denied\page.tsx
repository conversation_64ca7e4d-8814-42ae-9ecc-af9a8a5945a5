import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { ShieldX, Mail, ArrowLeft } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Access Denied - GGsim',
  description: 'You do not have permission to access this resource.',
};

export default function AccessDeniedPage() {
  return (
    <div
      className="min-h-screen flex items-center justify-center bg-gray-50 px-4"
      data-oid="_7rrlbo"
    >
      <div className="max-w-md w-full text-center" data-oid="u-7.gs:">
        <div className="bg-white rounded-lg shadow-lg p-8" data-oid="tg_rujh">
          <div className="flex justify-center mb-6" data-oid="w0ul7dh">
            <ShieldX className="h-16 w-16 text-red-500" data-oid=":sq196p" />
          </div>

          <h1 className="text-2xl font-bold text-gray-900 mb-4" data-oid=":dwpb51">
            Access Denied
          </h1>

          <p className="text-gray-600 mb-6" data-oid="reva8fq">
            Your email address is not currently approved for beta access. Beta testing is limited to
            pre-approved users only.
          </p>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6" data-oid="-kr27_a">
            <div className="flex items-start" data-oid="gned96f">
              <Mail
                className="h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0"
                data-oid="odx.4zj"
              />

              <div className="text-left" data-oid="p-zz:gi">
                <h3 className="text-sm font-medium text-blue-800 mb-1" data-oid="rx1pdlq">
                  Request Beta Access
                </h3>
                <p className="text-sm text-blue-700" data-oid="0vi2..m">
                  Contact our team at{' '}
                  <a
                    href="mailto:<EMAIL>"
                    className="font-medium underline hover:no-underline"
                    data-oid="sz43fkf"
                  >
                    <EMAIL>
                  </a>{' '}
                  to request access to the beta program.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-3" data-oid="9ue5wnt">
            <Link href="/" data-oid="479mozw">
              <Button className="w-full flex items-center justify-center" data-oid="mba0840">
                <ArrowLeft className="h-4 w-4 mr-2" data-oid="drrf5cc" />
                Back to Home
              </Button>
            </Link>

            <Link href="/sign-in" data-oid=":lkp7:7">
              <Button variant="outline" className="w-full" data-oid="zncbix_">
                Try Different Account
              </Button>
            </Link>
          </div>

          <div className="mt-6 pt-6 border-t border-gray-200" data-oid="50.13_g">
            <p className="text-xs text-gray-500" data-oid="eapybhh">
              If you believe this is an error, please contact support with your email address.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
