import { <PERSON>ada<PERSON> } from 'next'
import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { ShieldX, Mail, ArrowLeft } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Access Denied - GGsim',
  description: 'You do not have permission to access this resource.',
}

export default function AccessDeniedPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="max-w-md w-full text-center">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="flex justify-center mb-6">
            <ShieldX className="h-16 w-16 text-red-500" />
          </div>
          
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Access Denied
          </h1>
          
          <p className="text-gray-600 mb-6">
            Your email address is not currently approved for beta access. 
            Beta testing is limited to pre-approved users only.
          </p>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <Mail className="h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" />
              <div className="text-left">
                <h3 className="text-sm font-medium text-blue-800 mb-1">
                  Request Beta Access
                </h3>
                <p className="text-sm text-blue-700">
                  Contact our team at{' '}
                  <a 
                    href="mailto:<EMAIL>" 
                    className="font-medium underline hover:no-underline"
                  >
                    <EMAIL>
                  </a>
                  {' '}to request access to the beta program.
                </p>
              </div>
            </div>
          </div>
          
          <div className="space-y-3">
            <Link href="/">
              <Button className="w-full flex items-center justify-center">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Button>
            </Link>
            
            <Link href="/sign-in">
              <Button variant="outline" className="w-full">
                Try Different Account
              </Button>
            </Link>
          </div>
          
          <div className="mt-6 pt-6 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              If you believe this is an error, please contact support with your email address.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
