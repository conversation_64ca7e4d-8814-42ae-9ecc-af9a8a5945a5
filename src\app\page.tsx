import React from 'react';
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { HomePageClient } from '@/components/HomePageClient';

export default async function HomePage() {
  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={messages} data-oid="xf9w10p">
      <HomePageClient data-oid="06-2sr4" />
    </NextIntlClientProvider>
  );
}
