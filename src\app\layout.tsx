import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs'
import { Analytics } from '@vercel/analytics/react'
import { SpeedInsights } from '@vercel/speed-insights/next'
import { GlobalErrorBoundary } from '@/components/error/GlobalErrorBoundary'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'https://ggsim.me'),
  title: {
    default: 'GGsim - Global eSIM Marketplace',
    template: '%s | GGsim'
  },
  description: 'Find the perfect eSIM for your journey. Compare plans from multiple providers and get instant access to global connectivity.',
  keywords: ['eSIM', 'travel', 'connectivity', 'mobile data', 'international roaming', 'global coverage', 'instant activation'],
  authors: [{ name: 'GGsim Team', url: 'https://ggsim.me' }],
  creator: 'GGsim Team',
  publisher: 'GGsim',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: 'GGsim - Global eSIM Marketplace',
    description: 'Find the perfect eSIM for your journey. Compare plans from multiple providers and get instant access to global connectivity.',
    url: 'https://ggsim.me',
    siteName: 'GGsim',
    images: [
      {
        url: '/images/LogoT.png',
        width: 1200,
        height: 630,
        alt: 'GGsim - Global eSIM Marketplace',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'GGsim - Global eSIM Marketplace',
    description: 'Find the perfect eSIM for your journey. Compare plans from multiple providers and get instant access to global connectivity.',
    images: ['/images/LogoT.png'],
    creator: '@ggsim',
  },
  manifest: '/manifest.json',
  icons: {
    icon: '/images/LogoT.png',
    shortcut: '/images/LogoT.png',
    apple: '/images/LogoT.png',
    other: {
      rel: 'apple-touch-icon-precomposed',
      url: '/images/LogoT.png',
    },
  },
  // verification: {
  //   google: process.env.GOOGLE_VERIFICATION_CODE,
  // },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ClerkProvider
      appearance={{
        baseTheme: undefined,
        variables: {
          colorPrimary: '#00a3ff',
          colorBackground: '#ffffff',
          colorInputBackground: '#ffffff',
          colorInputText: '#1f2937',
        },
        elements: {
          formButtonPrimary: 'bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md transition-colors',
          card: 'shadow-lg border border-gray-200',
          headerTitle: 'text-gray-900',
          headerSubtitle: 'text-gray-600',
          socialButtonsBlockButton: 'border border-gray-300 hover:bg-gray-50',
          formFieldInput: 'border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
          footerActionLink: 'text-blue-500 hover:text-blue-600',
        },
      }}
    >
      <html lang="en" suppressHydrationWarning>
        <head>
          <link rel="icon" href="/favicon.ico" />
          <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
          <link rel="manifest" href="/manifest.json" />
          <meta name="theme-color" content="#00a3ff" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
        </head>
        <body className={inter.className} suppressHydrationWarning>
          <GlobalErrorBoundary>
            <div id="root">
              {children}
            </div>
          </GlobalErrorBoundary>

          {/* Analytics and Performance Monitoring */}
          <Analytics />
          <SpeedInsights />
        </body>
      </html>
    </ClerkProvider>
  )
}
