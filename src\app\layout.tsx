import type { Metadata } from 'next';
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/next';
import { GlobalErrorBoundary } from '@/components/error/GlobalErrorBoundary';
import './globals.css';
export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'https://ggsim.me'),
  title: {
    default: 'GGsim - Global eSIM Marketplace',
    template: '%s | GGsim',
  },
  description:
    'Find the perfect eSIM for your journey. Compare plans from multiple providers and get instant access to global connectivity.',
  keywords: [
    'eSIM',
    'travel',
    'connectivity',
    'mobile data',
    'international roaming',
    'global coverage',
    'instant activation',
  ],

  authors: [
    {
      name: 'GGsim Team',
      url: 'https://ggsim.me',
    },
  ],

  creator: 'GGsim Team',
  publisher: 'GGsim',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: 'GGsim - Global eSIM Marketplace',
    description:
      'Find the perfect eSIM for your journey. Compare plans from multiple providers and get instant access to global connectivity.',
    url: 'https://ggsim.me',
    siteName: 'GGsim',
    images: [
      {
        url: '/images/LogoT.png',
        width: 1200,
        height: 630,
        alt: 'GGsim - Global eSIM Marketplace',
      },
    ],

    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'GGsim - Global eSIM Marketplace',
    description:
      'Find the perfect eSIM for your journey. Compare plans from multiple providers and get instant access to global connectivity.',
    images: ['/images/LogoT.png'],
    creator: '@ggsim',
  },
  manifest: '/manifest.json',
  icons: {
    icon: '/images/LogoT.png',
    shortcut: '/images/LogoT.png',
    apple: '/images/LogoT.png',
    other: {
      rel: 'apple-touch-icon-precomposed',
      url: '/images/LogoT.png',
    },
  },
  // verification: {
  //   google: process.env.GOOGLE_VERIFICATION_CODE,
  // },
};
export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <ClerkProvider
      appearance={{
        baseTheme: undefined,
        variables: {
          colorPrimary: '#00a3ff',
          colorBackground: '#ffffff',
          colorInputBackground: '#ffffff',
          colorInputText: '#1f2937',
        },
        elements: {
          formButtonPrimary:
            'bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md transition-colors',
          card: 'shadow-lg border border-gray-200',
          headerTitle: 'text-gray-900',
          headerSubtitle: 'text-gray-600',
          socialButtonsBlockButton: 'border border-gray-300 hover:bg-gray-50',
          formFieldInput:
            'border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
          footerActionLink: 'text-blue-500 hover:text-blue-600',
        },
      }}
      data-oid="5w-0_0c"
    >
      <html lang="en" suppressHydrationWarning data-oid="9zx47df">
        <head data-oid="-dv5_l-">
          <link rel="icon" href="/favicon.ico" data-oid="d9fad0u" />
          <link rel="apple-touch-icon" href="/apple-touch-icon.png" data-oid="do9zp_v" />
          <link rel="manifest" href="/manifest.json" data-oid="bfest8s" />
          <meta name="theme-color" content="#00a3ff" data-oid="qg67v2q" />
          <meta name="viewport" content="width=device-width, initial-scale=1" data-oid="ucppvyb" />
        </head>
        <body
          className="top-auto right-auto bottom-auto left-auto static h-[63px] w-[126px]"
          suppressHydrationWarning
          data-oid="rn.47hw"
        >
          <GlobalErrorBoundary data-oid="ley1f-1">
            <div
              id="root"
              className="relative top-auto right-auto bottom-auto left-auto w-[1448px]"
              data-oid="oawfz5m"
            >
              {children}
            </div>
          </GlobalErrorBoundary>

          {/* Analytics and Performance Monitoring */}
          <Analytics data-oid="b48wf_j" />
          <SpeedInsights data-oid="hpbnz3." />
        </body>
      </html>
    </ClerkProvider>
  );
}
