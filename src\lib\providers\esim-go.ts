import { AxiosRequestConfig } from 'axios'
import {
  BaseESIMProvider,
  ESIMPlan,
  ESIMPurchaseRequest,
  ESIMPurchaseResponse,
  PlanQuery,
  ProviderConfig,
} from './base-provider'

// eSIM Go specific interfaces
interface ESIMGoConfig extends ProviderConfig {
  apiKey: string
}

interface ESIMGoPlan {
  id: string
  title: string
  country: string
  country_code: string
  region: string
  data: number
  validity: number
  price: number
  currency: string
  operator: string
  network_type: string
  features: string[]
}

interface ESIMGoOrderRequest {
  plan_id: string
  customer_email: string
  customer_name?: string
  reference?: string
}

interface ESIMGoOrderResponse {
  order_id: string
  status: string
  qr_code: string
  iccid: string
  activation_code: string
  expiry_date: string
  instructions: string
  network_settings: {
    operator: string
    apn: string
    network_type: string
  }
}

export class ESIMGoProvider extends BaseESIMProvider {
  constructor(config: ESIMGoConfig) {
    super(config, 'eSIM Go')
  }

  addAuthHeaders(config: AxiosRequestConfig): AxiosRequestConfig {
    return {
      ...config,
      headers: {
        ...config.headers,
        'Authorization': `Bearer ${this.config.apiKey}`,
        'X-API-Version': '1.0',
      },
    }
  }

  async fetchPlans(query: PlanQuery): Promise<ESIMPlan[]> {
    this.validatePlanQuery(query)

    try {
      const params: any = {}
      
      if (query.country) params.country = query.country
      if (query.countryCode) params.country_code = query.countryCode
      if (query.region) params.region = query.region
      if (query.dataAmount) params.min_data = query.dataAmount
      if (query.maxPrice) params.max_price = query.maxPrice

      const response = await this.retryRequest(async () => {
        return await this.makeRequest<{ plans: ESIMGoPlan[] }>(
          'GET',
          '/plans',
          undefined,
          true
        )
      })

      return response.plans.map(plan => this.transformPlan(plan))
    } catch (error) {
      console.error('eSIM Go fetchPlans error:', error)
      throw error
    }
  }

  async purchasePlan(request: ESIMPurchaseRequest): Promise<ESIMPurchaseResponse> {
    try {
      const orderRequest: ESIMGoOrderRequest = {
        plan_id: request.planId,
        customer_email: request.customerEmail,
        customer_name: request.customerName,
        reference: request.metadata?.orderId,
      }

      const response = await this.retryRequest(async () => {
        return await this.makeRequest<ESIMGoOrderResponse>(
          'POST',
          '/orders',
          orderRequest,
          false
        )
      })

      return this.transformOrderResponse(response)
    } catch (error) {
      console.error('eSIM Go purchasePlan error:', error)
      throw error
    }
  }

  async getOrderStatus(orderId: string): Promise<ESIMPurchaseResponse> {
    try {
      const response = await this.retryRequest(async () => {
        return await this.makeRequest<ESIMGoOrderResponse>(
          'GET',
          `/orders/${orderId}`,
          undefined,
          false
        )
      })

      return this.transformOrderResponse(response)
    } catch (error) {
      console.error('eSIM Go getOrderStatus error:', error)
      throw error
    }
  }

  // Transform eSIM Go plan format to our standard format
  private transformPlan(plan: ESIMGoPlan): ESIMPlan {
    return {
      id: plan.id,
      name: plan.title,
      country: plan.country,
      countryCode: plan.country_code,
      region: plan.region,
      dataAmount: plan.data,
      validity: plan.validity,
      price: this.formatPrice(plan.price, plan.currency),
      currency: 'USD', // Normalized to USD
      provider: 'esim-go',
      features: plan.features || [],
      coverage: [plan.country],
      networkInfo: {
        operator: plan.operator,
        networkType: plan.network_type,
      },
    }
  }

  // Transform eSIM Go order response to our standard format
  private transformOrderResponse(response: ESIMGoOrderResponse): ESIMPurchaseResponse {
    return {
      orderId: response.order_id,
      activationCode: response.qr_code || response.activation_code,
      iccid: response.iccid,
      status: this.mapStatus(response.status),
      expiryDate: response.expiry_date,
      activationInstructions: response.instructions,
      networkInfo: response.network_settings ? {
        operator: response.network_settings.operator,
        apn: response.network_settings.apn,
        networkType: response.network_settings.network_type,
      } : undefined,
    }
  }

  // Map eSIM Go status to our standard status
  private mapStatus(status: string): 'pending' | 'ready' | 'activated' {
    switch (status.toLowerCase()) {
      case 'pending':
      case 'processing':
        return 'pending'
      case 'completed':
      case 'ready':
      case 'delivered':
        return 'ready'
      case 'activated':
      case 'active':
        return 'activated'
      default:
        return 'pending'
    }
  }

  // eSIM Go specific health check
  async healthCheck(): Promise<boolean> {
    try {
      // Try to fetch a small number of plans as a health check
      await this.makeRequest('GET', '/plans?limit=1', undefined, false)
      return true
    } catch (error) {
      console.error('eSIM Go health check failed:', error)
      return false
    }
  }

  // Get available countries from eSIM Go
  async getAvailableCountries(): Promise<Array<{ code: string; name: string; region: string }>> {
    try {
      const response = await this.makeRequest<{ countries: Array<{ code: string; name: string; region: string }> }>(
        'GET',
        '/countries',
        undefined,
        true
      )

      return response.countries
    } catch (error) {
      console.error('eSIM Go getAvailableCountries error:', error)
      return []
    }
  }

  // Get plan details by ID
  async getPlanDetails(planId: string): Promise<ESIMPlan | null> {
    try {
      const response = await this.makeRequest<ESIMGoPlan>(
        'GET',
        `/plans/${planId}`,
        undefined,
        true
      )

      return this.transformPlan(response)
    } catch (error) {
      console.error('eSIM Go getPlanDetails error:', error)
      return null
    }
  }

  // Cancel an order (if supported)
  async cancelOrder(orderId: string): Promise<boolean> {
    try {
      await this.makeRequest(
        'DELETE',
        `/orders/${orderId}`,
        undefined,
        false
      )
      return true
    } catch (error) {
      console.error('eSIM Go cancelOrder error:', error)
      return false
    }
  }

  // Get usage data for an activated eSIM (if supported)
  async getUsageData(iccid: string): Promise<{ used: number; remaining: number; total: number } | null> {
    try {
      const response = await this.makeRequest<{
        data_used: number
        data_remaining: number
        data_total: number
      }>(
        'GET',
        `/usage/${iccid}`,
        undefined,
        false
      )

      return {
        used: response.data_used,
        remaining: response.data_remaining,
        total: response.data_total,
      }
    } catch (error) {
      console.error('eSIM Go getUsageData error:', error)
      return null
    }
  }
}

// Factory function to create eSIM Go provider instance
export function createESIMGoProvider(): ESIMGoProvider {
  const config: ESIMGoConfig = {
    apiKey: process.env.ESIM_GO_API_KEY || '',
    apiUrl: process.env.ESIM_GO_API_URL || 'https://sandbox.esimgo.com/api/v1',
    environment: (process.env.ESIM_GO_ENVIRONMENT as 'sandbox' | 'production') || 'sandbox',
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 1000,
  }

  if (!config.apiKey) {
    throw new Error('ESIM_GO_API_KEY environment variable is required')
  }

  return new ESIMGoProvider(config)
}
