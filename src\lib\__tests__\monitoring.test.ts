import { initMonitoring, trackEvent, trackPageView, usePageViewTracking } from '../monitoring';
import { renderHook } from '@testing-library/react';

// Mock window.performance
Object.defineProperty(window, 'performance', {
  value: {
    mark: jest.fn(),
    measure: jest.fn(),
    getEntriesByType: jest.fn().mockReturnValue([]),
    getEntriesByName: jest.fn().mockReturnValue([]),
    clearMarks: jest.fn(),
    clearMeasures: jest.fn(),
  },
  writable: true,
});

describe('Monitoring Utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mocked functions
    window.performance.mark.mockClear();
    window.performance.measure.mockClear();
  });

  describe('initMonitoring', () => {
    it('should initialize monitoring with default config', () => {
      const result = initMonitoring();
      
      expect(result).toBe(true);
      // In a real implementation, we would check for side effects like
      // event listeners being added or external services being initialized
    });

    it('should initialize monitoring with custom config', () => {
      const config = {
        errorTracking: {
          enabled: true,
          sampleRate: 0.5,
        },
        performanceMonitoring: {
          enabled: false,
        },
      };

      const result = initMonitoring(config);
      
      expect(result).toBe(true);
    });
  });

  describe('trackEvent', () => {
    it('should track custom events', () => {
      // Mock any global analytics object that might be used
      global.dataLayer = [];
      
      trackEvent('button_click', { buttonId: 'submit', page: '/home' });
      
      // Check that the event was tracked
      expect(global.dataLayer).toContainEqual({
        event: 'button_click',
        buttonId: 'submit',
        page: '/home',
      });
    });

    it('should handle missing event data', () => {
      global.dataLayer = [];
      
      trackEvent('page_view');
      
      expect(global.dataLayer).toContainEqual({
        event: 'page_view',
      });
    });
  });

  describe('trackPageView', () => {
    it('should track page views with the correct path', () => {
      global.dataLayer = [];
      
      trackPageView('/products/123');
      
      expect(global.dataLayer).toContainEqual({
        event: 'page_view',
        page: '/products/123',
      });
    });

    it('should use window.location.pathname if no path is provided', () => {
      global.dataLayer = [];
      
      // Mock window.location
      const originalLocation = window.location;
      delete window.location;
      window.location = { pathname: '/about' } as Location;
      
      trackPageView();
      
      expect(global.dataLayer).toContainEqual({
        event: 'page_view',
        page: '/about',
      });
      
      // Restore original location
      window.location = originalLocation;
    });
  });

  describe('usePageViewTracking', () => {
    it('should call trackPageView on mount', () => {
      // Mock trackPageView
      const mockTrackPageView = jest.fn();
      jest.mock('../monitoring', () => ({
        ...jest.requireActual('../monitoring'),
        trackPageView: mockTrackPageView,
      }));

      // Render the hook
      renderHook(() => usePageViewTracking('/test-page'));
      
      // Check that trackPageView was called
      expect(mockTrackPageView).toHaveBeenCalledWith('/test-page');
    });
  });
});