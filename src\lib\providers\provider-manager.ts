import {
  BaseESIMProvider,
  ESIMPlan,
  ESIMPurchaseRequest,
  ESIMPurchaseResponse,
  PlanQuery,
} from './base-provider'
import { createESIMGoProvider } from './esim-go'
import { createOneGlobalProvider } from './one-global'
import { createTelnaProvider } from './telna'

export interface ProviderHealthStatus {
  name: string
  healthy: boolean
  environment: string
  lastChecked: Date
  responseTime?: number
}

export interface AggregatedPlan extends ESIMPlan {
  rank: number // For sorting by best value
  score: number // Composite score based on price, features, etc.
}

export class ProviderManager {
  private providers: Map<string, BaseESIMProvider> = new Map()
  private healthStatus: Map<string, ProviderHealthStatus> = new Map()
  private lastHealthCheck: Date = new Date(0)
  private healthCheckInterval: number = 5 * 60 * 1000 // 5 minutes

  constructor() {
    this.initializeProviders()
  }

  private initializeProviders(): void {
    try {
      // Initialize eSIM Go provider
      if (process.env.ESIM_GO_API_KEY) {
        const esimGoProvider = createESIMGoProvider()
        this.providers.set('esim-go', esimGoProvider)
        console.log('eSIM Go provider initialized')
      }

      // Initialize 1GLOBAL provider
      if (process.env.ONE_GLOBAL_API_KEY) {
        const oneGlobalProvider = createOneGlobalProvider()
        this.providers.set('one-global', oneGlobalProvider)
        console.log('1GLOBAL provider initialized')
      }

      // Initialize Telna provider
      if (process.env.TELNA_API_KEY) {
        const telnaProvider = createTelnaProvider()
        this.providers.set('telna', telnaProvider)
        console.log('Telna provider initialized')
      }

      if (this.providers.size === 0) {
        console.warn('No eSIM providers configured. Please set up API keys.')
      }
    } catch (error) {
      console.error('Error initializing providers:', error)
    }
  }

  // Get all available plans from all providers
  async getAllPlans(query: PlanQuery): Promise<AggregatedPlan[]> {
    const allPlans: ESIMPlan[] = []
    const providerPromises: Promise<ESIMPlan[]>[] = []

    // Fetch plans from all healthy providers in parallel
    for (const [providerName, provider] of Array.from(this.providers.entries())) {
      if (await this.isProviderHealthy(providerName)) {
        providerPromises.push(
          provider.fetchPlans(query).catch(error => {
            console.error(`Error fetching plans from ${providerName}:`, error)
            return [] // Return empty array on error
          })
        )
      }
    }

    // Wait for all providers to respond
    const results = await Promise.all(providerPromises)
    
    // Flatten results
    results.forEach(plans => allPlans.push(...plans))

    // Aggregate and rank plans
    return this.aggregateAndRankPlans(allPlans, query)
  }

  // Get plans from a specific provider
  async getPlansFromProvider(providerName: string, query: PlanQuery): Promise<ESIMPlan[]> {
    const provider = this.providers.get(providerName)
    if (!provider) {
      throw new Error(`Provider ${providerName} not found`)
    }

    if (!(await this.isProviderHealthy(providerName))) {
      throw new Error(`Provider ${providerName} is not healthy`)
    }

    return provider.fetchPlans(query)
  }

  // Purchase a plan from a specific provider
  async purchasePlan(
    providerName: string,
    request: ESIMPurchaseRequest
  ): Promise<ESIMPurchaseResponse> {
    const provider = this.providers.get(providerName)
    if (!provider) {
      throw new Error(`Provider ${providerName} not found`)
    }

    if (!(await this.isProviderHealthy(providerName))) {
      throw new Error(`Provider ${providerName} is not healthy`)
    }

    return provider.purchasePlan(request)
  }

  // Get order status from a specific provider
  async getOrderStatus(
    providerName: string,
    orderId: string
  ): Promise<ESIMPurchaseResponse> {
    const provider = this.providers.get(providerName)
    if (!provider) {
      throw new Error(`Provider ${providerName} not found`)
    }

    return provider.getOrderStatus(orderId)
  }

  // Find the best plan based on criteria
  async findBestPlan(query: PlanQuery): Promise<AggregatedPlan | null> {
    const plans = await this.getAllPlans(query)
    
    if (plans.length === 0) {
      return null
    }

    // Return the highest-ranked plan
    return plans[0]
  }

  // Find the cheapest plan
  async findCheapestPlan(query: PlanQuery): Promise<AggregatedPlan | null> {
    const plans = await this.getAllPlans(query)
    
    if (plans.length === 0) {
      return null
    }

    // Sort by price and return the cheapest
    const sortedByPrice = plans.sort((a, b) => a.price - b.price)
    return sortedByPrice[0]
  }

  // Get health status of all providers
  async getProvidersHealth(): Promise<ProviderHealthStatus[]> {
    // Check if we need to refresh health status
    const now = new Date()
    if (now.getTime() - this.lastHealthCheck.getTime() > this.healthCheckInterval) {
      await this.checkAllProvidersHealth()
    }

    return Array.from(this.healthStatus.values())
  }

  // Check health of all providers
  private async checkAllProvidersHealth(): Promise<void> {
    const healthPromises: Promise<void>[] = []

    for (const [providerName, provider] of Array.from(this.providers.entries())) {
      healthPromises.push(this.checkProviderHealth(providerName, provider))
    }

    await Promise.all(healthPromises)
    this.lastHealthCheck = new Date()
  }

  // Check health of a specific provider
  private async checkProviderHealth(
    providerName: string,
    provider: BaseESIMProvider
  ): Promise<void> {
    const startTime = Date.now()
    
    try {
      const isHealthy = await provider.healthCheck()
      const responseTime = Date.now() - startTime

      this.healthStatus.set(providerName, {
        name: providerName,
        healthy: isHealthy,
        environment: provider.getProviderInfo().environment,
        lastChecked: new Date(),
        responseTime,
      })
    } catch (error) {
      console.error(`Health check failed for ${providerName}:`, error)
      
      this.healthStatus.set(providerName, {
        name: providerName,
        healthy: false,
        environment: provider.getProviderInfo().environment,
        lastChecked: new Date(),
      })
    }
  }

  // Check if a specific provider is healthy
  private async isProviderHealthy(providerName: string): Promise<boolean> {
    const status = this.healthStatus.get(providerName)
    
    if (!status) {
      // If no status, check health now
      const provider = this.providers.get(providerName)
      if (provider) {
        await this.checkProviderHealth(providerName, provider)
        const newStatus = this.healthStatus.get(providerName)
        return newStatus?.healthy || false
      }
      return false
    }

    // Check if status is recent enough
    const now = new Date()
    const statusAge = now.getTime() - status.lastChecked.getTime()
    
    if (statusAge > this.healthCheckInterval) {
      // Status is stale, refresh it
      const provider = this.providers.get(providerName)
      if (provider) {
        await this.checkProviderHealth(providerName, provider)
        const newStatus = this.healthStatus.get(providerName)
        return newStatus?.healthy || false
      }
    }

    return status.healthy
  }

  // Aggregate and rank plans
  private aggregateAndRankPlans(plans: ESIMPlan[], query: PlanQuery): AggregatedPlan[] {
    // Remove duplicates (same plan from different providers)
    const uniquePlans = this.removeDuplicatePlans(plans)

    // Calculate scores and rank plans
    const rankedPlans = uniquePlans.map((plan, index) => ({
      ...plan,
      rank: index + 1,
      score: this.calculatePlanScore(plan, query),
    }))

    // Sort by score (higher is better)
    rankedPlans.sort((a, b) => b.score - a.score)

    // Update ranks after sorting
    rankedPlans.forEach((plan, index) => {
      plan.rank = index + 1
    })

    return rankedPlans
  }

  // Remove duplicate plans (basic implementation)
  private removeDuplicatePlans(plans: ESIMPlan[]): ESIMPlan[] {
    const seen = new Set<string>()
    return plans.filter(plan => {
      const key = `${plan.country}-${plan.dataAmount}-${plan.validity}-${plan.price}`
      if (seen.has(key)) {
        return false
      }
      seen.add(key)
      return true
    })
  }

  // Calculate a composite score for a plan
  private calculatePlanScore(plan: ESIMPlan, query: PlanQuery): number {
    let score = 0

    // Price score (lower price = higher score)
    const maxPrice = query.maxPrice || 100
    const priceScore = Math.max(0, (maxPrice - plan.price) / maxPrice) * 40

    // Data amount score
    const dataScore = Math.min(plan.dataAmount / 10, 1) * 30 // Max score at 10GB

    // Validity score
    const validityScore = Math.min(plan.validity / 30, 1) * 20 // Max score at 30 days

    // Features score
    const featuresScore = (plan.features.length / 5) * 10 // Max score with 5+ features

    score = priceScore + dataScore + validityScore + featuresScore

    // Bonus for exact matches
    if (query.country && plan.country.toLowerCase() === query.country.toLowerCase()) {
      score += 5
    }
    if (query.dataAmount && plan.dataAmount === query.dataAmount) {
      score += 5
    }

    return Math.round(score * 100) / 100 // Round to 2 decimal places
  }

  // Get list of available providers
  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys())
  }

  // Get provider info
  getProviderInfo(providerName: string): any {
    const provider = this.providers.get(providerName)
    return provider?.getProviderInfo() || null
  }
}

// Singleton instance
let providerManager: ProviderManager | null = null

export function getProviderManager(): ProviderManager {
  if (!providerManager) {
    providerManager = new ProviderManager()
  }
  return providerManager
}
