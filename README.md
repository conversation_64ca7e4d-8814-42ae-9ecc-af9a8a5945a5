# GGsim eSIM Platform - Beta Testing Environment

A modern, secure eSIM marketplace platform built with Next.js 15, TypeScript, and Clerk authentication. This repository contains the complete beta testing environment with comprehensive authentication, monitoring, and feedback collection systems.

## 🚀 Features

- Next.js App Router architecture
- PayloadCMS integration for content management
- PostgreSQL database with Vercel Postgres
- TypeScript for type safety
- Comprehensive testing setup with Jest
- CI/CD pipeline with GitHub Actions
- AI integration with MCP server support
- AI content detection capabilities

## 📋 Prerequisites

- Node.js 20.x or later
- PostgreSQL database
- npm or yarn

## 🛠️ Installation

1. Clone the repository

```bash
git clone https://github.com/your-username/ggsim.git
cd ggsim
```

2. Install dependencies

```bash
npm install
```

3. Set up environment variables

Copy the `.env.example` file to `.env.local` and update the values:

```bash
cp .env.example .env.local
```

4. Start the development server

```bash
npm run dev
```

## 🏗️ Project Structure

```
├── .github/            # GitHub Actions workflows
├── public/             # Static assets
├── src/
│   ├── app/            # Next.js App Router
│   │   ├── (frontend)/ # Frontend routes
│   │   ├── api/        # API routes
│   │   └── admin/      # Admin panel routes
│   ├── components/     # React components
│   ├── lib/            # Utility functions and libraries
│   │   ├── ai/         # AI integration modules
│   │   └── error-handler.ts # Error handling utilities
│   ├── payload.config.ts # PayloadCMS configuration
│   └── payload-types.ts  # Generated PayloadCMS types
├── .env.example        # Example environment variables
├── .gitignore          # Git ignore file
├── jest.config.js      # Jest configuration
├── next.config.js      # Next.js configuration
├── package.json        # Project dependencies
├── tsconfig.json       # TypeScript configuration
└── vercel.json         # Vercel deployment configuration
```

## 🧪 Testing

Run tests with:

```bash
npm test
```

Run tests in watch mode:

```bash
npm run test:watch
```

## 🚢 Deployment

The project is configured for deployment on Vercel. The CI/CD pipeline will automatically deploy:

- Preview deployments for pull requests
- Production deployment when merging to the main branch

### Manual Deployment

```bash
npm run build
vercel deploy --prod
```

## 🌿 Branching Strategy

We follow a simplified Git Flow branching strategy:

- `main`: Production-ready code
- `develop`: Integration branch for features
- `feature/*`: New features and enhancements
- `bugfix/*`: Bug fixes
- `hotfix/*`: Urgent production fixes

## 🤖 AI Integration

### MCP Server Support

The project includes integration with MCP servers for enhanced AI capabilities. See `src/lib/ai/mcp-server.ts` for implementation details.

### AI Detection

The project includes AI content detection capabilities. See `src/lib/ai/ai-detection.ts` for implementation details.

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgements

- [Next.js](https://nextjs.org/)
- [PayloadCMS](https://payloadcms.com/)
- [Vercel](https://vercel.com/)
- [TypeScript](https://www.typescriptlang.org/)
- [Jest](https://jestjs.io/)
