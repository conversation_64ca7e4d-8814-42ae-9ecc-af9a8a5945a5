/**
 * How It Works Page - SEO Optimized
 * 
 * Comprehensive guide explaining eSIM technology and activation process
 * Structured for maximum SEO value and user understanding
 */
'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { 
  DevicePhoneMobileIcon,
  QrCodeIcon,
  GlobeAltIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { MainLayout } from '@/components/layout/MainLayout'
import { Button } from '@/components/ui/button'

const steps = [
  {
    id: 1,
    title: 'Choose Your eSIM Plan',
    description: 'Browse our extensive catalog of eSIM plans from trusted global providers. Filter by destination, data allowance, duration, and price to find the perfect plan for your needs.',
    icon: GlobeAltIcon,
    details: [
      'Compare plans from 200+ countries',
      'Filter by data, duration, and price',
      'Read reviews from verified travelers',
      'Check device compatibility instantly'
    ],
    image: '/images/step-1-choose-plan.jpg'
  },
  {
    id: 2,
    title: 'Complete Secure Purchase',
    description: 'Complete your purchase using our secure payment system. Receive your eSIM QR code and activation instructions immediately via email.',
    icon: CheckCircleIcon,
    details: [
      'Secure payment processing',
      'Instant email delivery',
      'QR code and manual setup options',
      '24/7 customer support included'
    ],
    image: '/images/step-2-purchase.jpg'
  },
  {
    id: 3,
    title: 'Scan QR Code & Connect',
    description: 'Simply scan the QR code with your device camera or enter the details manually. Your eSIM activates instantly and you\'re connected to the local network.',
    icon: QrCodeIcon,
    details: [
      'Instant activation via QR code',
      'Manual setup option available',
      'Automatic network connection',
      'Real-time activation status'
    ],
    image: '/images/step-3-activate.jpg'
  }
]

const faqs = [
  {
    question: 'What is an eSIM and how does it work?',
    answer: 'An eSIM (embedded SIM) is a digital SIM card built into your device. Instead of inserting a physical SIM card, you download your carrier profile digitally. This allows you to switch between carriers and plans without changing physical cards.'
  },
  {
    question: 'Which devices support eSIM technology?',
    answer: 'Most modern smartphones support eSIM, including iPhone XS and newer, Google Pixel 3 and newer, Samsung Galaxy S20 and newer, and many other flagship devices. Check our compatibility guide for a complete list.'
  },
  {
    question: 'Can I use eSIM alongside my regular SIM card?',
    answer: 'Yes! Most eSIM-compatible devices support dual SIM functionality, allowing you to use your regular SIM for calls/texts and eSIM for data while traveling. This eliminates expensive roaming charges.'
  },
  {
    question: 'How long does eSIM activation take?',
    answer: 'eSIM activation is instant once you scan the QR code. The entire process from purchase to connection typically takes less than 5 minutes. Some networks may take up to 15 minutes for full activation.'
  },
  {
    question: 'What happens if I have connection issues?',
    answer: 'Our 24/7 support team is available to help with any connection issues. Most problems are resolved by restarting your device or checking APN settings. We provide detailed troubleshooting guides for all supported devices.'
  }
]

export function HowItWorksClient() {
  return (
    <MainLayout>
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-white to-gray-50 py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
                How eSIM Technology{' '}
                <span className="bg-gradient-to-r from-green-600 to-green-500 bg-clip-text text-transparent">
                  Works
                </span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                Discover how eSIM technology revolutionizes mobile connectivity. Get connected instantly in 200+ countries without physical SIM cards, roaming fees, or carrier restrictions.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="bg-green-600 hover:bg-green-700" onClick={() => window.location.href = '/search'}>
                  Browse eSIM Plans
                </Button>
                <Button variant="outline" size="lg" onClick={() => window.location.href = '/compatibility'}>
                  Check Device Compatibility
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* What is eSIM Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                What is eSIM Technology?
              </h2>
              <div className="prose prose-lg mx-auto text-gray-600">
                <p>
                  An eSIM (embedded SIM) is a revolutionary digital SIM card technology that's built directly into your device. 
                  Unlike traditional physical SIM cards, eSIMs allow you to download and activate carrier profiles digitally, 
                  providing unprecedented flexibility for travelers and mobile users.
                </p>
                <p>
                  With eSIM technology, you can switch between carriers, activate new plans, and connect to local networks 
                  instantly without visiting stores or waiting for physical cards to arrive. This makes it perfect for 
                  international travel, business trips, and anyone who needs reliable global connectivity.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Step-by-Step Process */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Get Connected in 3 Simple Steps
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Our streamlined process makes it easy to get connected anywhere in the world. 
                From selection to activation, everything is designed for simplicity and speed.
              </p>
            </motion.div>

            <div className="space-y-20">
              {steps.map((step, index) => (
                <motion.div
                  key={step.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center gap-12`}
                >
                  {/* Content */}
                  <div className="flex-1">
                    <div className="flex items-center mb-6">
                      <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                        {step.id}
                      </div>
                      <step.icon className="w-8 h-8 text-green-600" />
                    </div>
                    
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">
                      {step.title}
                    </h3>
                    
                    <p className="text-lg text-gray-600 mb-6">
                      {step.description}
                    </p>
                    
                    <ul className="space-y-2">
                      {step.details.map((detail, idx) => (
                        <li key={idx} className="flex items-center text-gray-600">
                          <CheckCircleIcon className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                          {detail}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Image Placeholder */}
                  <div className="flex-1">
                    <div className="bg-gradient-to-br from-green-100 to-green-50 rounded-2xl p-8 h-80 flex items-center justify-center">
                      <step.icon className="w-24 h-24 text-green-600" />
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-gray-600">
                Everything you need to know about eSIM technology and our service.
              </p>
            </motion.div>

            <div className="space-y-6">
              {faqs.map((faq, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-gray-50 rounded-xl p-6"
                >
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-start">
                    <InformationCircleIcon className="w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" />
                    {faq.question}
                  </h3>
                  <p className="text-gray-600 ml-9">
                    {faq.answer}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-green-600 to-green-700">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl sm:text-4xl font-bold text-white mb-6">
                Ready to Experience eSIM?
              </h2>
              <p className="text-xl text-green-100 mb-8">
                Join thousands of travelers who trust GGsim for reliable global connectivity. 
                Get started with your first eSIM plan today.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="bg-white text-green-600 hover:bg-gray-100">
                  Browse eSIM Plans
                </Button>
                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-green-600">
                  Contact Support
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </MainLayout>
  )
}
